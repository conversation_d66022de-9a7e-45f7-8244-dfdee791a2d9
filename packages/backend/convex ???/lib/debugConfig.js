/**
 * Backend Debug Configuration System
 *
 * Convex-side debug controls with environment variable switches.
 * Ensures debug functionality and logging are controlled for production security.
 */
// Environment-based debug configuration for Convex
const getEnvBoolean = (key, defaultValue = false) => {
    const value = process.env[key];
    if (value === undefined)
        return defaultValue;
    return value === "true" || value === "1";
};
// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV !== "production";
// Master debug switch - controls all backend debug functionality
export const DEBUG_ENABLED = getEnvBoolean("DEBUG_MODE", isDevelopment);
// Granular debug controls for backend features
export const DEBUG_CONFIG = {
    // Authentication debugging
    auth: {
        enabled: DEBUG_ENABLED && getEnvBoolean("DEBUG_AUTH", true),
        logging: DEBUG_ENABLED && getEnvBoolean("DEBUG_AUTH_LOGGING", true),
        validation: DEBUG_ENABLED && getEnvBoolean("DEBUG_AUTH_VALIDATION", true),
        functions: DEBUG_ENABLED && getEnvBoolean("DEBUG_AUTH_FUNCTIONS", true),
    },
    // Performance monitoring debugging
    performance: {
        enabled: DEBUG_ENABLED && getEnvBoolean("DEBUG_PERFORMANCE", true),
        monitoring: DEBUG_ENABLED && getEnvBoolean("DEBUG_PERFORMANCE_MONITORING", true),
        caching: DEBUG_ENABLED && getEnvBoolean("DEBUG_CACHE", false),
        queries: DEBUG_ENABLED && getEnvBoolean("DEBUG_QUERIES", false),
    },
    // Database and query debugging
    database: {
        enabled: DEBUG_ENABLED && getEnvBoolean("DEBUG_DATABASE", false),
        queries: DEBUG_ENABLED && getEnvBoolean("DEBUG_DB_QUERIES", false),
        mutations: DEBUG_ENABLED && getEnvBoolean("DEBUG_DB_MUTATIONS", false),
        indexes: DEBUG_ENABLED && getEnvBoolean("DEBUG_DB_INDEXES", false),
    },
    // API and external service debugging
    external: {
        enabled: DEBUG_ENABLED && getEnvBoolean("DEBUG_EXTERNAL", true),
        twitter: DEBUG_ENABLED && getEnvBoolean("DEBUG_TWITTER_API", true),
        webhooks: DEBUG_ENABLED && getEnvBoolean("DEBUG_WEBHOOKS", true),
        integrations: DEBUG_ENABLED && getEnvBoolean("DEBUG_INTEGRATIONS", true),
    },
    // Workflow and automation debugging
    workflows: {
        enabled: DEBUG_ENABLED && getEnvBoolean("DEBUG_WORKFLOWS", true),
        crons: DEBUG_ENABLED && getEnvBoolean("DEBUG_CRONS", true),
        batching: DEBUG_ENABLED && getEnvBoolean("DEBUG_BATCHING", false),
    }
};
// Debug logging levels for backend
export var DebugLevel;
(function (DebugLevel) {
    DebugLevel[DebugLevel["ERROR"] = 0] = "ERROR";
    DebugLevel[DebugLevel["WARN"] = 1] = "WARN";
    DebugLevel[DebugLevel["INFO"] = 2] = "INFO";
    DebugLevel[DebugLevel["DEBUG"] = 3] = "DEBUG";
    DebugLevel[DebugLevel["TRACE"] = 4] = "TRACE";
})(DebugLevel || (DebugLevel = {}));
const currentLogLevel = DEBUG_ENABLED
    ? parseInt(process.env.DEBUG_LOG_LEVEL || "3")
    : DebugLevel.ERROR;
// Backend debug logging utility
export const debugLog = {
    error: (category, message, data) => {
        if (currentLogLevel >= DebugLevel.ERROR) {
            console.error(`🔴 [Backend:${category}]`, message, data ? JSON.stringify(data, null, 2) : '');
        }
    },
    warn: (category, message, data) => {
        if (currentLogLevel >= DebugLevel.WARN) {
            console.warn(`🟡 [Backend:${category}]`, message, data ? JSON.stringify(data, null, 2) : '');
        }
    },
    info: (category, message, data) => {
        if (currentLogLevel >= DebugLevel.INFO) {
            console.info(`🔵 [Backend:${category}]`, message, data ? JSON.stringify(data, null, 2) : '');
        }
    },
    debug: (category, message, data) => {
        if (currentLogLevel >= DebugLevel.DEBUG) {
            console.debug(`🟢 [Backend:${category}]`, message, data ? JSON.stringify(data, null, 2) : '');
        }
    },
    trace: (category, message, data) => {
        if (currentLogLevel >= DebugLevel.TRACE) {
            console.trace(`🔍 [Backend:${category}]`, message, data ? JSON.stringify(data, null, 2) : '');
        }
    },
};
// Performance timing utility for backend
export const debugTimer = {
    timers: new Map(),
    start: (label) => {
        if (DEBUG_CONFIG.performance.enabled) {
            debugTimer.timers.set(label, Date.now());
            debugLog.debug('Performance', `Timer started: ${label}`);
        }
    },
    end: (label) => {
        if (DEBUG_CONFIG.performance.enabled) {
            const startTime = debugTimer.timers.get(label);
            if (startTime) {
                const duration = Date.now() - startTime;
                debugLog.debug('Performance', `Timer ended: ${label} (${duration}ms)`);
                debugTimer.timers.delete(label);
                return duration;
            }
        }
        return 0;
    },
    mark: (label, data) => {
        if (DEBUG_CONFIG.performance.enabled) {
            debugLog.debug('Performance', `Checkpoint: ${label}`, data);
        }
    },
};
// Function execution wrapper with debug logging
export const withDebug = (category, functionName, fn) => {
    return async (...args) => {
        const isEnabled = DEBUG_CONFIG[category].enabled;
        if (isEnabled) {
            const timerId = `${category}:${functionName}`;
            debugTimer.start(timerId);
            debugLog.debug(category, `Function ${functionName} started`, { args: args.length > 0 ? 'with args' : 'no args' });
        }
        try {
            const result = await fn(...args);
            if (isEnabled) {
                const timerId = `${category}:${functionName}`;
                const duration = debugTimer.end(timerId);
                debugLog.debug(category, `Function ${functionName} completed (${duration}ms)`, {
                    hasResult: result !== undefined
                });
            }
            return result;
        }
        catch (error) {
            if (isEnabled) {
                debugLog.error(category, `Function ${functionName} failed`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
            throw error;
        }
    };
};
// Debug middleware for Convex functions
export const debugMiddleware = (category) => {
    return (target, propertyKey, descriptor) => {
        const originalMethod = descriptor.value;
        descriptor.value = withDebug(category, propertyKey, originalMethod);
        return descriptor;
    };
};
// Environment info for backend debugging
export const getBackendDebugInfo = () => {
    if (!DEBUG_ENABLED) {
        return { debug: false, mode: 'production' };
    }
    return {
        debug: DEBUG_ENABLED,
        mode: process.env.NODE_ENV || 'development',
        config: DEBUG_CONFIG,
        environment: {
            DEBUG_MODE: process.env.DEBUG_MODE,
            DEBUG_AUTH: process.env.DEBUG_AUTH,
            DEBUG_PERFORMANCE: process.env.DEBUG_PERFORMANCE,
            DEBUG_LOG_LEVEL: process.env.DEBUG_LOG_LEVEL,
            NODE_ENV: process.env.NODE_ENV,
        },
        timestamp: new Date().toISOString(),
    };
};
// Initialize backend debug mode
if (DEBUG_ENABLED) {
    debugLog.info('System', 'Backend debug mode initialized', getBackendDebugInfo());
}
else {
    // Create no-op functions for production
    const noOp = () => { };
    const noOpAsync = async () => { };
    Object.keys(debugLog).forEach(key => {
        debugLog[key] = noOp;
    });
    debugTimer.start = noOp;
    debugTimer.end = noOp;
    debugTimer.mark = noOp;
}
// Export debug conditional helpers
export const isDebugEnabled = (category) => {
    return DEBUG_CONFIG[category].enabled;
};
export const debugOnly = (category, fn) => {
    return DEBUG_CONFIG[category].enabled ? fn() : undefined;
};
