/**
 * TwitterAPI.io Usage Monitoring and Quota Management
 *
 * This module provides comprehensive monitoring of TwitterAPI.io usage,
 * including rate limiting, quota tracking, cost monitoring, and emergency controls.
 */
import { action, internalMutation, internalQuery, query } from "../_generated/server";
import { v } from "convex/values";
import { getTwitterAPIConfig, getQuotaConfig, getAPIMonitoringConfig } from "./config";
/**
 * Track API usage for monitoring and quota management
 */
export const trackAPIUsage = internalMutation({
    args: {
        endpoint: v.string(),
        requestCount: v.number(),
        responseSize: v.optional(v.number()),
        duration: v.optional(v.number()),
        status: v.union(v.literal("success"), v.literal("error"), v.literal("rate_limited")),
        errorMessage: v.optional(v.string()),
        estimatedCost: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const config = getAPIMonitoringConfig();
        if (!config.enableUsageLogging) {
            return { tracked: false, reason: "usage logging disabled" };
        }
        // Store the usage entry
        const usageId = await ctx.db.insert("twitterApiUsage", {
            timestamp: Date.now(),
            endpoint: args.endpoint,
            requestCount: args.requestCount,
            responseSize: args.responseSize,
            duration: args.duration,
            status: args.status,
            errorMessage: args.errorMessage,
            cost: args.estimatedCost || 0,
            createdAt: Date.now(),
        });
        // Clean up old entries based on retention policy
        const cutoffTime = Date.now() - (config.logRetentionDays * 24 * 60 * 60 * 1000);
        const oldEntries = await ctx.db
            .query("twitterApiUsage")
            .filter((q) => q.lt(q.field("timestamp"), cutoffTime))
            .collect();
        // Delete old entries in batches to avoid hitting limits
        for (const entry of oldEntries.slice(0, 100)) {
            await ctx.db.delete(entry._id);
        }
        return {
            tracked: true,
            usageId,
            cleanedUp: oldEntries.length > 0 ? Math.min(100, oldEntries.length) : 0
        };
    },
});
/**
 * Get current quota status
 */
export const getQuotaStatus = query({
    handler: async (ctx) => {
        const quotaConfig = getQuotaConfig();
        // Calculate current day's usage
        const dayStart = new Date();
        dayStart.setHours(0, 0, 0, 0);
        const dayStartTimestamp = dayStart.getTime();
        const todayUsage = await ctx.db
            .query("twitterApiUsage")
            .filter((q) => q.gte(q.field("timestamp"), dayStartTimestamp))
            .collect();
        const currentUsage = todayUsage.reduce((sum, entry) => sum + entry.requestCount, 0);
        const percentageUsed = (currentUsage / quotaConfig.dailyRequestLimit) * 100;
        // Calculate reset time (next midnight)
        const resetTime = new Date();
        resetTime.setDate(resetTime.getDate() + 1);
        resetTime.setHours(0, 0, 0, 0);
        return {
            currentUsage,
            dailyLimit: quotaConfig.dailyRequestLimit,
            percentageUsed,
            resetTime: resetTime.getTime(),
            isWarning: percentageUsed >= quotaConfig.warningThreshold,
            isEmergency: percentageUsed >= quotaConfig.emergencyStopThreshold,
            remainingRequests: Math.max(0, quotaConfig.dailyRequestLimit - currentUsage),
        };
    },
});
/**
 * Check if we should block requests due to quota limits
 */
export const shouldBlockRequests = internalQuery({
    handler: async (ctx) => {
        const quotaConfig = getQuotaConfig();
        if (!quotaConfig.enableQuotaTracking) {
            const mockQuotaStatus = {
                currentUsage: 0,
                dailyLimit: quotaConfig.dailyRequestLimit,
                percentageUsed: 0,
                resetTime: Date.now() + 24 * 60 * 60 * 1000,
                isWarning: false,
                isEmergency: false,
                remainingRequests: quotaConfig.dailyRequestLimit,
            };
            return { shouldBlock: false, quotaStatus: mockQuotaStatus };
        }
        const quotaStatus = await ctx.runQuery("lib/twitter_api_monitor:getQuotaStatus");
        if (quotaStatus.isEmergency) {
            return {
                shouldBlock: true,
                reason: `Emergency quota limit reached (${quotaStatus.percentageUsed.toFixed(1)}% of daily limit)`,
                quotaStatus,
            };
        }
        return { shouldBlock: false, quotaStatus };
    },
});
/**
 * Get API usage analytics
 */
export const getUsageAnalytics = query({
    args: {
        timeRange: v.optional(v.union(v.literal("hour"), v.literal("day"), v.literal("week"), v.literal("month"))),
        endpoint: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "day";
        const now = Date.now();
        let startTime;
        switch (timeRange) {
            case "hour":
                startTime = now - (60 * 60 * 1000);
                break;
            case "day":
                startTime = now - (24 * 60 * 60 * 1000);
                break;
            case "week":
                startTime = now - (7 * 24 * 60 * 60 * 1000);
                break;
            case "month":
                startTime = now - (30 * 24 * 60 * 60 * 1000);
                break;
        }
        let query = ctx.db
            .query("twitterApiUsage")
            .filter((q) => q.gte(q.field("timestamp"), startTime));
        if (args.endpoint) {
            query = query.filter((q) => q.eq(q.field("endpoint"), args.endpoint));
        }
        const usageEntries = await query.collect();
        // Aggregate statistics
        const totalRequests = usageEntries.reduce((sum, entry) => sum + entry.requestCount, 0);
        const totalCost = usageEntries.reduce((sum, entry) => sum + (entry.cost || 0), 0);
        const successfulRequests = usageEntries.filter(e => e.status === "success").length;
        const errorRequests = usageEntries.filter(e => e.status === "error").length;
        const rateLimitedRequests = usageEntries.filter(e => e.status === "rate_limited").length;
        // Calculate average response time
        const entriesWithDuration = usageEntries.filter(e => e.duration !== undefined);
        const averageResponseTime = entriesWithDuration.length > 0
            ? entriesWithDuration.reduce((sum, entry) => sum + (entry.duration || 0), 0) / entriesWithDuration.length
            : 0;
        // Group by endpoint
        const endpointStats = usageEntries.reduce((acc, entry) => {
            const endpoint = entry.endpoint;
            if (!acc[endpoint]) {
                acc[endpoint] = {
                    endpoint,
                    requestCount: 0,
                    successCount: 0,
                    errorCount: 0,
                    rateLimitedCount: 0,
                    totalCost: 0,
                    averageResponseTime: 0,
                };
            }
            acc[endpoint].requestCount += entry.requestCount;
            acc[endpoint].totalCost += entry.cost || 0;
            if (entry.status === "success")
                acc[endpoint].successCount++;
            else if (entry.status === "error")
                acc[endpoint].errorCount++;
            else if (entry.status === "rate_limited")
                acc[endpoint].rateLimitedCount++;
            return acc;
        }, {});
        return {
            timeRange,
            period: {
                startTime,
                endTime: now,
            },
            summary: {
                totalRequests,
                successfulRequests,
                errorRequests,
                rateLimitedRequests,
                successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
                totalCost,
                averageResponseTime,
            },
            endpointBreakdown: Object.values(endpointStats),
            recentEntries: usageEntries.slice(-10), // Last 10 entries for debugging
        };
    },
});
/**
 * Get rate limit status for all endpoints
 */
export const getRateLimitStatus = query({
    handler: async (ctx) => {
        // This would typically be stored in memory or cache
        // For now, return a mock status since rate limits are handled in the client
        const commonEndpoints = [
            "/v2/users/by/username",
            "/v2/users/{id}/tweets",
            "/v2/tweets/{id}",
            "/twitter/tweet/advanced_search",
        ];
        return commonEndpoints.map(endpoint => ({
            endpoint,
            limit: 100, // This would come from actual API headers
            remaining: 95, // This would come from actual API headers
            resetTime: Date.now() + 15 * 60 * 1000, // 15 minutes from now
            isLimited: false,
        }));
    },
});
/**
 * Health check for TwitterAPI.io integration
 */
export const apiHealthCheck = action({
    handler: async (ctx) => {
        const checks = [];
        try {
            // 1. Configuration validation
            const { validateConfig } = await import("./config");
            const configValidation = validateConfig();
            checks.push({
                name: "Configuration Validation",
                status: configValidation.valid ? "pass" : "fail",
                message: configValidation.valid
                    ? "All configuration is valid"
                    : `Configuration errors: ${configValidation.errors.join(", ")}`,
                details: configValidation,
            });
            // 2. Quota status check
            const quotaStatus = await ctx.runQuery("lib/twitter_api_monitor:getQuotaStatus");
            checks.push({
                name: "Quota Status",
                status: quotaStatus.isEmergency ? "fail" : quotaStatus.isWarning ? "warn" : "pass",
                message: `Using ${quotaStatus.percentageUsed.toFixed(1)}% of daily quota (${quotaStatus.currentUsage}/${quotaStatus.dailyLimit})`,
                details: quotaStatus,
            });
            // 3. Recent error rate check
            const analytics = await ctx.runQuery("lib/twitter_api_monitor:getUsageAnalytics", { timeRange: "hour" });
            const errorRate = analytics.summary.totalRequests > 0
                ? ((analytics.summary.errorRequests + analytics.summary.rateLimitedRequests) / analytics.summary.totalRequests) * 100
                : 0;
            checks.push({
                name: "Error Rate (Last Hour)",
                status: errorRate > 20 ? "fail" : errorRate > 10 ? "warn" : "pass",
                message: `${errorRate.toFixed(1)}% error rate (${analytics.summary.errorRequests + analytics.summary.rateLimitedRequests}/${analytics.summary.totalRequests} requests)`,
                details: { errorRate, ...analytics.summary },
            });
            // 4. API connectivity check (basic)
            try {
                const config = getTwitterAPIConfig();
                const testResponse = await fetch(`${config.baseUrl}/v2/users/by/username/twitter`, {
                    method: "HEAD", // Just check headers
                    headers: {
                        "X-API-Key": config.apiKey,
                    },
                });
                checks.push({
                    name: "API Connectivity",
                    status: testResponse.ok ? "pass" : "fail",
                    message: testResponse.ok
                        ? "API endpoint accessible"
                        : `API returned ${testResponse.status}: ${testResponse.statusText}`,
                    details: {
                        status: testResponse.status,
                        statusText: testResponse.statusText,
                        headers: Object.fromEntries(testResponse.headers.entries()),
                    },
                });
            }
            catch (error) {
                checks.push({
                    name: "API Connectivity",
                    status: "fail",
                    message: `Network error: ${error instanceof Error ? error.message : String(error)}`,
                    details: { error: String(error) },
                });
            }
        }
        catch (error) {
            checks.push({
                name: "Health Check Error",
                status: "fail",
                message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
                details: { error: String(error) },
            });
        }
        // Calculate summary
        const summary = {
            totalChecks: checks.length,
            passed: checks.filter(c => c.status === "pass").length,
            warnings: checks.filter(c => c.status === "warn").length,
            failures: checks.filter(c => c.status === "fail").length,
        };
        // Determine overall status
        let status;
        if (summary.failures > 0) {
            status = "critical";
        }
        else if (summary.warnings > 0) {
            status = "warning";
        }
        else {
            status = "healthy";
        }
        return {
            status,
            checks,
            summary,
        };
    },
});
/**
 * Get cost analysis for API usage
 */
export const getCostAnalysis = query({
    args: {
        timeRange: v.optional(v.union(v.literal("day"), v.literal("week"), v.literal("month"))),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "month";
        const now = Date.now();
        let startTime;
        switch (timeRange) {
            case "day":
                startTime = now - (24 * 60 * 60 * 1000);
                break;
            case "week":
                startTime = now - (7 * 24 * 60 * 60 * 1000);
                break;
            case "month":
                startTime = now - (30 * 24 * 60 * 60 * 1000);
                break;
        }
        const usageEntries = await ctx.db
            .query("twitterApiUsage")
            .filter((q) => q.gte(q.field("timestamp"), startTime))
            .collect();
        const totalCost = usageEntries.reduce((sum, entry) => sum + (entry.cost || 0), 0);
        const totalRequests = usageEntries.reduce((sum, entry) => sum + entry.requestCount, 0);
        const averageCostPerRequest = totalRequests > 0 ? totalCost / totalRequests : 0;
        // Group by day for trend analysis
        const dailyCosts = usageEntries.reduce((acc, entry) => {
            const day = new Date(entry.timestamp).toISOString().split('T')[0];
            if (!acc[day]) {
                acc[day] = { date: day, cost: 0, requests: 0 };
            }
            acc[day].cost += entry.cost || 0;
            acc[day].requests += entry.requestCount;
            return acc;
        }, {});
        return {
            timeRange,
            period: { startTime, endTime: now },
            summary: {
                totalCost,
                totalRequests,
                averageCostPerRequest,
                estimatedMonthlyCost: timeRange === "month" ? totalCost : totalCost * (30 / (timeRange === "week" ? 7 : 1)),
            },
            dailyBreakdown: Object.values(dailyCosts).sort((a, b) => a.date.localeCompare(b.date)),
        };
    },
});
