import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { createTwitterClient } from "../lib/twitter_client";

export const fetchTweetFromUrl = action({
  args: {
    url: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const apiKey = process.env.TWITTERAPI_IO_API_KEY;
      const twitterClient = createTwitterClient(apiKey);
      
      // Validate and extract tweet info from URL
      const result = await twitterClient.getTweetFromUrl(args.url);
      
      if (!result.tweet) {
        return {
          success: false,
          error: "Tweet not found or URL is invalid",
          tweet: null,
        };
      }

      const author = result.users.find(u => u.id === result.tweet!.author_id);
      
      // Format tweet data for frontend
      const tweetData = {
        id: result.tweet.id,
        content: result.tweet.text,
        author: {
          name: author?.name || "Unknown",
          username: author?.username || "unknown",
          profileImage: author?.profile_image_url,
          verified: author?.verified || false,
          followers: author?.followers_count || 0,
        },
        createdAt: result.tweet.created_at, // Pass the original timestamp string
        engagement: {
          likes: result.tweet.public_metrics?.like_count || 0,
          retweets: result.tweet.public_metrics?.retweet_count || 0,
          replies: result.tweet.public_metrics?.reply_count || 0,
          views: result.tweet.public_metrics?.impression_count || 0,
        },
        url: args.url,
        isRetweet: result.tweet.referenced_tweets?.some(ref => ref.type === "retweeted") || false,
        isReply: Boolean(result.tweet.in_reply_to_user_id),
        conversationId: result.tweet.conversation_id,
        metadata: {
          hasMedia: Boolean(result.tweet.attachments?.media_keys?.length),
          hasUrls: Boolean(result.tweet.entities?.urls?.length),
          hasHashtags: Boolean(result.tweet.entities?.hashtags?.length),
          hasMentions: Boolean(result.tweet.entities?.mentions?.length),
          language: result.tweet.entities?.hashtags?.length ? "detected" : undefined,
        },
      };

      return {
        success: true,
        tweet: tweetData,
        error: null,
      };
    } catch (error) {
      console.error("Error fetching tweet from URL:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
        tweet: null,
      };
    }
  },
});

export const analyzeTweetFromUrl = action({
  args: {
    url: v.string(),
    storeInDatabase: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    try {
      // First fetch the tweet
      const fetchResult = await ctx.runAction("twitter/fetchTweetFromUrl:fetchTweetFromUrl", {
        url: args.url,
      });

      if (!fetchResult.success || !fetchResult.tweet) {
        return fetchResult;
      }

      const tweet = fetchResult.tweet;

      // Basic sentiment and engagement analysis
      const contentLength = tweet.content.length;
      const hasLinks = tweet.metadata.hasUrls;
      const hasMedia = tweet.metadata.hasMedia;
      const hasHashtags = tweet.metadata.hasHashtags;
      const engagementRate = tweet.engagement.likes + tweet.engagement.retweets + tweet.engagement.replies;
      
      // Simple engagement score calculation
      const engagementScore = Math.min(
        (engagementRate / Math.max(tweet.author.followers / 100, 1)) * 10,
        10
      );

      // Content analysis
      const contentAnalysis = {
        length: contentLength,
        hasLinks,
        hasMedia,
        hasHashtags,
        engagementScore: Number(engagementScore.toFixed(2)),
        viralPotential: engagementScore > 5 ? "high" : engagementScore > 2 ? "medium" : "low",
        responseOpportunity: !tweet.isReply && engagementScore > 3 && contentLength > 50,
      };

      // Store in database if requested
      if (args.storeInDatabase) {
        // Find if we have this author as a monitored account
        const monitoredAccount = await ctx.runQuery(api.userQueries.getTwitterAccountByHandle, {
          handle: tweet.author.username,
        });

        if (monitoredAccount) {
          // Store the tweet
          await ctx.runMutation(api.tweets.storeTweet, {
            twitterAccountId: monitoredAccount._id,
            tweetId: tweet.id,
            content: tweet.content,
            author: tweet.author.name,
            authorHandle: tweet.author.username,
            authorProfileImage: tweet.author.profileImage,
            isRetweet: tweet.isRetweet,
            createdAt: tweet.createdAt,
            engagement: tweet.engagement,
            url: tweet.url,
            metadata: {
              isThread: tweet.conversationId !== tweet.id,
              hasMedia: tweet.metadata.hasMedia,
              language: tweet.metadata.language,
            },
          });
        }
      }

      return {
        success: true,
        tweet: {
          ...tweet,
          analysis: contentAnalysis,
        },
        error: null,
      };
    } catch (error) {
      console.error("Error analyzing tweet from URL:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
        tweet: null,
      };
    }
  },
});

export const validateTwitterUrl = action({
  args: {
    url: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Extract tweet ID from URL
      const patterns = [
        /twitter\.com\/[^\/]+\/status\/(\d+)/i,
        /x\.com\/[^\/]+\/status\/(\d+)/i,
      ];

      let tweetId = null;
      let username = null;

      for (const pattern of patterns) {
        const match = args.url.match(pattern);
        if (match && match[1]) {
          tweetId = match[1];
          break;
        }
      }

      // Extract username
      const usernamePatterns = [
        /twitter\.com\/([^\/\?]+)/i,
        /x\.com\/([^\/\?]+)/i,
      ];

      for (const pattern of usernamePatterns) {
        const match = args.url.match(pattern);
        if (match && match[1] && match[1] !== "i" && match[1] !== "home") {
          username = match[1];
          break;
        }
      }

      if (!tweetId) {
        return {
          valid: false,
          error: "Could not extract tweet ID from URL",
          tweetId: null,
          username: null,
        };
      }

      return {
        valid: true,
        tweetId,
        username,
        error: null,
      };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : "Unknown error",
        tweetId: null,
        username: null,
      };
    }
  },
});