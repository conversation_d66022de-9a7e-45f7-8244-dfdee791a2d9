import { action, query } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
/**
 * Export user data for backup or transfer
 */
export const exportUserData = action({
    args: {
        includeContent: v.optional(v.boolean()),
        includeAnalytics: v.optional(v.boolean()),
        includeEmbeddings: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            const exportData = {
                exportVersion: "1.0",
                exportedAt: Date.now(),
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    createdAt: user.createdAt,
                    preferences: user.walletPreferences,
                },
            };
            // Export Twitter accounts
            const twitterAccounts = await ctx.runQuery(api.users.getUserTwitterAccounts, {});
            exportData.twitterAccounts = twitterAccounts.map((acc) => ({
                id: acc._id,
                handle: acc.handle,
                displayName: acc.displayName,
                isActive: acc.isActive,
                isMonitoringEnabled: acc.isMonitoringEnabled,
                createdAt: acc.createdAt,
            }));
            const accountIds = twitterAccounts.map((acc) => acc._id);
            if (args.includeContent !== false) {
                // Export tweets
                const userTweets = await ctx.runQuery(api.userQueries.getTweetsByAccountIds, {
                    accountIds,
                    limit: 10000,
                });
                exportData.tweets = userTweets.map((tweet) => ({
                    id: tweet._id,
                    tweetId: tweet.tweetId,
                    content: tweet.content,
                    author: tweet.author,
                    authorHandle: tweet.authorHandle,
                    createdAt: tweet.createdAt,
                    scrapedAt: tweet.scrapedAt,
                    engagement: tweet.engagement,
                    analysisStatus: tweet.analysisStatus,
                    analysisScore: tweet.analysisScore,
                    url: tweet.url,
                }));
                // Export mentions
                const userMentions = await ctx.runQuery(api.userQueries.getMentionsByAccountIds, {
                    accountIds,
                    limit: 10000,
                });
                exportData.mentions = userMentions.map((mention) => ({
                    id: mention._id,
                    mentionTweetId: mention.mentionTweetId,
                    mentionContent: mention.mentionContent,
                    mentionAuthor: mention.mentionAuthor,
                    mentionAuthorHandle: mention.mentionAuthorHandle,
                    mentionType: mention.mentionType,
                    priority: mention.priority,
                    isProcessed: mention.isProcessed,
                    createdAt: mention.createdAt,
                    discoveredAt: mention.discoveredAt,
                    engagement: mention.engagement,
                    aiAnalysisResult: mention.aiAnalysisResult,
                }));
                // Export responses
                const allResponses = await ctx.runQuery(api.userQueries.getAllResponses, {
                    userId: user._id,
                    limit: 10000
                });
                const responses = allResponses.filter((response) => response.userId === user._id);
                exportData.responses = responses.map((response) => ({
                    id: response._id,
                    targetType: response.targetType,
                    targetId: response.targetId,
                    content: response.content,
                    style: response.style,
                    confidence: response.confidence,
                    status: response.status,
                    createdAt: response.createdAt,
                    postedAt: response.postedAt,
                    actualEngagement: response.actualEngagement,
                    userFeedback: response.userFeedback,
                }));
            }
            if (args.includeAnalytics !== false) {
                // Export user context
                const userContext = await ctx.runQuery(api.userQueries.getUserEmbeddings, {
                    userId: user._id,
                    limit: 10000,
                });
                exportData.userContext = userContext.map((ctx) => ({
                    id: ctx._id,
                    contextType: ctx.contextType,
                    content: ctx.content,
                    weight: ctx.weight,
                    createdAt: ctx.createdAt,
                    updatedAt: ctx.updatedAt,
                }));
            }
            if (args.includeEmbeddings === true) {
                // Export embeddings (warning: can be large)
                const tweetIds = exportData.tweets?.map((t) => t.id) || [];
                const mentionIds = exportData.mentions?.map((m) => m.id) || [];
                const responseIds = exportData.responses?.map((r) => r.id) || [];
                const allTweetEmbeddings = await ctx.runQuery(api.userQueries.getAllTweetEmbeddings, { limit: 10000 });
                const allMentionEmbeddings = await ctx.runQuery(api.userQueries.getAllMentionEmbeddings, { limit: 10000 });
                const allResponseEmbeddings = await ctx.runQuery(api.userQueries.getAllResponseEmbeddings, { limit: 10000 });
                exportData.embeddings = {
                    tweets: allTweetEmbeddings.filter((e) => tweetIds.includes(e.tweetId)),
                    mentions: allMentionEmbeddings.filter((e) => mentionIds.includes(e.mentionId)),
                    responses: allResponseEmbeddings.filter((e) => responseIds.includes(e.responseId)),
                    userContext: await ctx.runQuery(api.userQueries.getUserEmbeddings, {
                        userId: user._id,
                        limit: 10000,
                    }),
                };
            }
            // Calculate export statistics
            exportData.statistics = {
                twitterAccounts: exportData.twitterAccounts?.length || 0,
                tweets: exportData.tweets?.length || 0,
                mentions: exportData.mentions?.length || 0,
                responses: exportData.responses?.length || 0,
                userContext: exportData.userContext?.length || 0,
                embeddings: args.includeEmbeddings ? {
                    tweets: exportData.embeddings?.tweets?.length || 0,
                    mentions: exportData.embeddings?.mentions?.length || 0,
                    responses: exportData.embeddings?.responses?.length || 0,
                    userContext: exportData.embeddings?.userContext?.length || 0,
                } : null,
            };
            return {
                success: true,
                exportData,
                size: JSON.stringify(exportData).length,
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error("Data export failed:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                timestamp: Date.now(),
            };
        }
    },
});
/**
 * Get export preview (metadata only, no content)
 */
export const getExportPreview = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            // Get counts for preview
            const twitterAccounts = await ctx.db
                .query("twitterAccounts")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            const accountIds = twitterAccounts.map((acc) => acc._id);
            const allTweets = await ctx.db.query("tweets").collect();
            const userTweets = allTweets.filter((tweet) => accountIds.includes(tweet.twitterAccountId));
            const allMentions = await ctx.db.query("mentions").collect();
            const userMentions = allMentions.filter((mention) => accountIds.includes(mention.monitoredAccountId));
            const responses = await ctx.db
                .query("responses")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            const userContext = await ctx.db
                .query("userContextEmbeddings")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            // Get date ranges
            const tweetDateRange = userTweets.length > 0 ? {
                earliest: Math.min(...userTweets.map((t) => t.createdAt)),
                latest: Math.max(...userTweets.map((t) => t.createdAt)),
            } : null;
            const mentionDateRange = userMentions.length > 0 ? {
                earliest: Math.min(...userMentions.map((m) => m.createdAt)),
                latest: Math.max(...userMentions.map((m) => m.createdAt)),
            } : null;
            return {
                user: {
                    name: user.name,
                    email: user.email,
                    accountCreated: user.createdAt,
                },
                counts: {
                    twitterAccounts: twitterAccounts.length,
                    tweets: userTweets.length,
                    mentions: userMentions.length,
                    responses: responses.length,
                    userContext: userContext.length,
                },
                dateRanges: {
                    tweets: tweetDateRange,
                    mentions: mentionDateRange,
                },
                estimatedSizes: {
                    withoutEmbeddings: "~50-500KB",
                    withEmbeddings: "~5-50MB",
                },
            };
        }
        catch (error) {
            console.error("Export preview failed:", error);
            throw new Error(`Export preview failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Clean up old data based on retention policies
 */
export const cleanupOldData = action({
    args: {
        retentionDays: v.optional(v.number()),
        dryRun: v.optional(v.boolean()),
        cleanupTypes: v.optional(v.array(v.union(v.literal("tweets"), v.literal("mentions"), v.literal("responses"), v.literal("embeddings")))),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            const retentionDays = args.retentionDays || 90; // Default 3 months
            const cutoffDate = Date.now() - retentionDays * 24 * 60 * 60 * 1000;
            const dryRun = args.dryRun || false;
            const cleanupTypes = args.cleanupTypes || ["tweets", "mentions", "responses", "embeddings"];
            const results = {
                deleted: {
                    tweets: 0,
                    mentions: 0,
                    responses: 0,
                    embeddings: 0,
                },
                errors: [],
            };
            // Get user's Twitter accounts - filter from all accounts since we can't query directly in action
            const allTwitterAccounts = await ctx.runQuery(api.userQueries.getActiveTwitterAccounts, {});
            const twitterAccounts = allTwitterAccounts.filter((acc) => acc.userId === user._id);
            const accountIds = new Set(twitterAccounts.map((acc) => acc._id));
            // Clean up tweets
            if (cleanupTypes.includes("tweets")) {
                try {
                    const userTweets = await ctx.runQuery(api.userQueries.getTweetsByAccountIds, {
                        accountIds: Array.from(accountIds),
                        limit: 10000,
                    });
                    const oldTweets = userTweets.filter((tweet) => tweet.scrapedAt < cutoffDate);
                    if (!dryRun) {
                        await ctx.runMutation(api.helperMutations.bulkDeleteTweets, {
                            tweetIds: oldTweets.map((t) => t._id),
                        });
                    }
                    results.deleted.tweets = oldTweets.length;
                }
                catch (error) {
                    results.errors.push(`Tweet cleanup error: ${error instanceof Error ? error.message : 'Unknown'}`);
                }
            }
            // Clean up mentions
            if (cleanupTypes.includes("mentions")) {
                try {
                    const userMentions = await ctx.runQuery(api.userQueries.getMentionsByAccountIds, {
                        accountIds: Array.from(accountIds),
                        limit: 10000,
                    });
                    const oldMentions = userMentions.filter((mention) => mention.discoveredAt < cutoffDate);
                    if (!dryRun) {
                        await ctx.runMutation(api.helperMutations.bulkDeleteMentions, {
                            mentionIds: oldMentions.map((m) => m._id),
                        });
                    }
                    results.deleted.mentions = oldMentions.length;
                }
                catch (error) {
                    results.errors.push(`Mention cleanup error: ${error instanceof Error ? error.message : 'Unknown'}`);
                }
            }
            // Clean up responses
            if (cleanupTypes.includes("responses")) {
                try {
                    const allUserResponses = await ctx.runQuery(api.userQueries.getAllResponses, {
                        userId: user._id,
                        limit: 10000,
                    });
                    const oldResponses = allUserResponses.filter((response) => response.userId === user._id && response.createdAt < cutoffDate);
                    if (!dryRun) {
                        await ctx.runMutation(api.helperMutations.bulkDeleteResponses, {
                            responseIds: oldResponses.map((r) => r._id),
                        });
                    }
                    results.deleted.responses = oldResponses.length;
                }
                catch (error) {
                    results.errors.push(`Response cleanup error: ${error instanceof Error ? error.message : 'Unknown'}`);
                }
            }
            // Clean up orphaned embeddings
            if (cleanupTypes.includes("embeddings")) {
                try {
                    let embeddingsDeleted = 0;
                    // Clean up tweet embeddings for deleted tweets
                    const allTweetEmbeddings = await ctx.runQuery(api.userQueries.getAllTweetEmbeddings, { limit: 10000 });
                    const allTweets = await ctx.runQuery(api.userQueries.getAllTweets, { limit: 10000 });
                    const existingTweetIds = new Set(allTweets.map((t) => t._id));
                    const orphanedTweetEmbeddings = allTweetEmbeddings.filter((e) => !existingTweetIds.has(e.tweetId));
                    if (!dryRun && orphanedTweetEmbeddings.length > 0) {
                        for (const embedding of orphanedTweetEmbeddings) {
                            await ctx.runMutation(api.helperMutations.deleteEmbedding, {
                                embeddingId: embedding._id,
                                tableName: "tweetEmbeddings",
                            });
                        }
                    }
                    embeddingsDeleted += orphanedTweetEmbeddings.length;
                    // Similar cleanup for mention and response embeddings...
                    // (simplified for brevity)
                    results.deleted.embeddings = embeddingsDeleted;
                }
                catch (error) {
                    results.errors.push(`Embedding cleanup error: ${error instanceof Error ? error.message : 'Unknown'}`);
                }
            }
            return {
                success: true,
                dryRun,
                retentionDays,
                cutoffDate,
                results,
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error("Data cleanup failed:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                timestamp: Date.now(),
            };
        }
    },
});
/**
 * Get storage usage statistics
 */
export const getStorageStats = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            // Get user's Twitter accounts
            const twitterAccounts = await ctx.db
                .query("twitterAccounts")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            const accountIds = new Set(twitterAccounts.map((acc) => acc._id));
            // Count records by age
            const now = Date.now();
            const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
            const oneMonthAgo = now - 30 * 24 * 60 * 60 * 1000;
            const threeMonthsAgo = now - 90 * 24 * 60 * 60 * 1000;
            // Tweets
            const allTweets = await ctx.db.query("tweets").collect();
            const userTweets = allTweets.filter((tweet) => accountIds.has(tweet.twitterAccountId));
            const tweetStats = {
                total: userTweets.length,
                lastWeek: userTweets.filter((t) => t.scrapedAt >= oneWeekAgo).length,
                lastMonth: userTweets.filter((t) => t.scrapedAt >= oneMonthAgo).length,
                lastThreeMonths: userTweets.filter((t) => t.scrapedAt >= threeMonthsAgo).length,
                older: userTweets.filter((t) => t.scrapedAt < threeMonthsAgo).length,
                estimatedSize: userTweets.length * 0.5, // Rough estimate in KB
            };
            // Mentions
            const allMentions = await ctx.db.query("mentions").collect();
            const userMentions = allMentions.filter((mention) => accountIds.has(mention.monitoredAccountId));
            const mentionStats = {
                total: userMentions.length,
                lastWeek: userMentions.filter((m) => m.discoveredAt >= oneWeekAgo).length,
                lastMonth: userMentions.filter((m) => m.discoveredAt >= oneMonthAgo).length,
                lastThreeMonths: userMentions.filter((m) => m.discoveredAt >= threeMonthsAgo).length,
                older: userMentions.filter((m) => m.discoveredAt < threeMonthsAgo).length,
                estimatedSize: userMentions.length * 0.7, // Rough estimate in KB
            };
            // Responses
            const responses = await ctx.db
                .query("responses")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            const responseStats = {
                total: responses.length,
                lastWeek: responses.filter((r) => r.createdAt >= oneWeekAgo).length,
                lastMonth: responses.filter((r) => r.createdAt >= oneMonthAgo).length,
                lastThreeMonths: responses.filter((r) => r.createdAt >= threeMonthsAgo).length,
                older: responses.filter((r) => r.createdAt < threeMonthsAgo).length,
                estimatedSize: responses.length * 0.3, // Rough estimate in KB
            };
            // Embeddings
            const userContext = await ctx.db
                .query("userContextEmbeddings")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
            const embeddingStats = {
                userContext: userContext.length,
                estimatedSize: userContext.length * 6, // Embeddings are larger (6KB each roughly)
            };
            const totalEstimatedSize = tweetStats.estimatedSize +
                mentionStats.estimatedSize +
                responseStats.estimatedSize +
                embeddingStats.estimatedSize;
            return {
                summary: {
                    totalRecords: tweetStats.total + mentionStats.total + responseStats.total,
                    totalEstimatedSize: Math.round(totalEstimatedSize),
                    unit: "KB",
                },
                breakdown: {
                    tweets: tweetStats,
                    mentions: mentionStats,
                    responses: responseStats,
                    embeddings: embeddingStats,
                },
                recommendations: getStorageRecommendations(tweetStats, mentionStats, responseStats),
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error("Storage stats failed:", error);
            throw new Error(`Storage stats failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Helper function to generate storage recommendations
 */
function getStorageRecommendations(tweetStats, mentionStats, responseStats) {
    const recommendations = [];
    if (tweetStats.older > 1000) {
        recommendations.push({
            type: "cleanup",
            priority: "medium",
            message: `Consider archiving ${tweetStats.older} tweets older than 3 months`,
            action: "cleanup_old_tweets",
        });
    }
    if (mentionStats.older > 500) {
        recommendations.push({
            type: "cleanup",
            priority: "medium",
            message: `Consider archiving ${mentionStats.older} mentions older than 3 months`,
            action: "cleanup_old_mentions",
        });
    }
    if (responseStats.total > 5000) {
        recommendations.push({
            type: "optimization",
            priority: "low",
            message: "Large number of responses - consider implementing response archival",
            action: "implement_response_archival",
        });
    }
    const totalSize = tweetStats.estimatedSize + mentionStats.estimatedSize + responseStats.estimatedSize;
    if (totalSize > 10000) { // 10MB
        recommendations.push({
            type: "storage",
            priority: "high",
            message: "High storage usage detected - consider implementing data retention policies",
            action: "implement_retention_policy",
        });
    }
    return recommendations;
}
