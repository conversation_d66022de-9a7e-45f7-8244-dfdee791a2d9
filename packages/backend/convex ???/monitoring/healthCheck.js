import { mutation, query } from "../_generated/server";
import { v } from "convex/values";
/**
 * Health Check Functions
 * System monitoring and health verification
 */
export const runSystemHealthCheck = mutation({
    args: {},
    returns: v.object({
        timestamp: v.number(),
        status: v.union(v.literal("healthy"), v.literal("error")),
        checks: v.optional(v.object({
            database: v.literal("OK"),
            accounts: v.number(),
            mentions_24h: v.number(),
            responses_24h: v.number(),
            credits: v.number(),
            wallets: v.number(),
        })),
        error: v.optional(v.string()),
    }),
    handler: async (ctx) => {
        console.log("🏥 Running daily system health check...");
        try {
            // Check database connectivity
            const userCount = await ctx.db.query("users").collect().then(users => users.length);
            console.log(`✅ Database connectivity: OK (${userCount} users)`);
            // Check twitter accounts
            const accountCount = await ctx.db.query("twitterAccounts").collect().then(accounts => accounts.length);
            console.log(`✅ Twitter accounts: ${accountCount} monitored accounts`);
            // Check mentions in last 24h
            const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
            const recentMentions = await ctx.db.query("mentions")
                .filter((q) => q.gte(q.field("discoveredAt"), oneDayAgo))
                .collect();
            console.log(`✅ Recent mentions: ${recentMentions.length} in last 24h`);
            // Check responses generated
            const recentResponses = await ctx.db.query("responses")
                .filter((q) => q.gte(q.field("createdAt"), oneDayAgo))
                .collect();
            console.log(`✅ Recent responses: ${recentResponses.length} generated in last 24h`);
            // Check credits system
            const creditsCount = await ctx.db.query("credits").collect().then(credits => credits.length);
            console.log(`✅ Credits system: ${creditsCount} user credit records`);
            // Check wallets
            const walletCount = await ctx.db.query("wallets").collect().then(wallets => wallets.length);
            console.log(`✅ Wallet system: ${walletCount} connected wallets`);
            const healthStatus = {
                timestamp: Date.now(),
                status: "healthy",
                checks: {
                    database: "OK",
                    accounts: accountCount,
                    mentions_24h: recentMentions.length,
                    responses_24h: recentResponses.length,
                    credits: creditsCount,
                    wallets: walletCount,
                }
            };
            console.log("🎉 System health check completed successfully");
            return healthStatus;
        }
        catch (error) {
            console.error("❌ Health check failed:", error);
            return {
                timestamp: Date.now(),
                status: "error",
                error: error instanceof Error ? error.message : String(error),
            };
        }
    },
});
export const quickHealthCheck = mutation({
    args: {},
    returns: v.object({
        timestamp: v.number(),
        status: v.union(v.literal("healthy"), v.literal("warning"), v.literal("error")),
        type: v.literal("quick_check"),
        error: v.optional(v.string()),
    }),
    handler: async (ctx) => {
        console.log("⚡ Running quick health check...");
        try {
            // Quick connectivity test
            const userExists = await ctx.db.query("users").first();
            const healthStatus = {
                timestamp: Date.now(),
                status: userExists ? "healthy" : "warning",
                type: "quick_check",
            };
            console.log("✅ Quick health check completed");
            return healthStatus;
        }
        catch (error) {
            console.error("❌ Quick health check failed:", error);
            return {
                timestamp: Date.now(),
                status: "error",
                type: "quick_check",
                error: error instanceof Error ? error.message : String(error),
            };
        }
    },
});
export const getSystemHealth = query({
    args: {},
    returns: v.object({
        status: v.union(v.literal("healthy"), v.literal("warning"), v.literal("error")),
        timestamp: v.number(),
        stats: v.optional(v.object({
            users: v.number(),
            accounts: v.number(),
            activeAccounts: v.number(),
            recentMentions: v.number(),
            pendingAnalysis: v.number(),
            credits: v.number(),
            wallets: v.number(),
        })),
        uptime: v.optional(v.literal("Running")),
        error: v.optional(v.string()),
    }),
    handler: async (ctx) => {
        try {
            // Get recent health check results
            const now = Date.now();
            const oneHourAgo = now - 60 * 60 * 1000;
            // Count active components
            const stats = {
                users: await ctx.db.query("users").collect().then(u => u.length),
                accounts: await ctx.db.query("twitterAccounts").collect().then(a => a.length),
                activeAccounts: await ctx.db.query("twitterAccounts")
                    .filter((q) => q.eq(q.field("isActive"), true))
                    .collect().then(a => a.length),
                recentMentions: await ctx.db.query("mentions")
                    .filter((q) => q.gte(q.field("discoveredAt"), oneHourAgo))
                    .collect().then(m => m.length),
                pendingAnalysis: await ctx.db.query("mentions")
                    .filter((q) => q.eq(q.field("isProcessed"), false))
                    .collect().then(m => m.length),
                credits: await ctx.db.query("credits").collect().then(c => c.length),
                wallets: await ctx.db.query("wallets").collect().then(w => w.length),
            };
            const overallHealth = stats.users > 0 && stats.accounts > 0 ? "healthy" : "warning";
            return {
                status: overallHealth,
                timestamp: now,
                stats,
                uptime: "Running",
            };
        }
        catch (error) {
            console.error("Error getting system health:", error);
            return {
                status: "error",
                timestamp: Date.now(),
                error: error instanceof Error ? error.message : String(error),
            };
        }
    },
});
