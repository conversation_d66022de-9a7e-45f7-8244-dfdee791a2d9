import { action } from "./_generated/server";
import { v } from "convex/values";
import { AIFallbackClient } from "./lib/ai_fallback_client";
import { api } from "./_generated/api";

export const chatWithTools = action({
  args: {
    messages: v.array(
      v.object({ role: v.string(), content: v.string() })
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Authentication required");

    const history = args.messages
      .map((m) => `${m.role}: ${m.content}`)
      .join("\n");

    const ai = new AIFallbackClient();
    const response = await ai.generateResponse({
      prompt: history,
      systemPrompt:
        "You are the BuddyChip Pro assistant.\n" +
        "If you want to generate an image reply with 'image: <prompt>'.\n" +
        "If you want to search tweets reply with 'search: <query>'.",
      maxTokens: 400,
      temperature: 0.5,
    });

    let content = response.content.trim();

    if (content.startsWith("image:")) {
      const prompt = content.slice(6).trim();
      const img = await ctx.runAction(
        api.ai.unifiedImageGeneration.generateUnifiedImage,
        { prompt }
      );
      content = `Here is your image: ${img.url}`;
    } else if (content.startsWith("search:")) {
      const query = content.slice(7).trim();
      const result = await ctx.runAction(
        api.ai.xaiLiveSearch.xaiRealTimeContentAnalysis,
        { query }
      );
      content = result.content || "No results";
    }

    return { role: "assistant", content };
  },
});
