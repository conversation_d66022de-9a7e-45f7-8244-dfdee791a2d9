import { type ImageGenerationResponse, type ResponsesAPIImageResponse } from "./lib/openai_client";
/**
 * Generate images using OpenAI's DALL-E models
 */
export declare const generateImage: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    style?: "vivid" | "natural" | undefined;
    model?: "dall-e-2" | "dall-e-3" | undefined;
    size?: "256x256" | "512x512" | "1024x1024" | "1792x1024" | "1024x1792" | undefined;
    quality?: "standard" | "hd" | undefined;
    responseFormat?: "url" | "b64_json" | undefined;
    prompt: string;
}, Promise<ImageGenerationResponse & {
    generatedAt: number;
}>>;
/**
 * Generate social media optimized images
 */
export declare const generateSocialMediaImage: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    style?: "minimal" | "vibrant" | "professional" | "artistic" | undefined;
    platform?: "twitter" | "instagram" | "linkedin" | undefined;
    includeText?: string | undefined;
    aspectRatio?: "square" | "landscape" | "portrait" | undefined;
    description: string;
}, Promise<ImageGenerationResponse & {
    platform?: string;
    style?: string;
    generatedAt: number;
}>>;
/**
 * Generate response with accompanying image using OpenAI Responses API
 */
export declare const generateResponseWithImage: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    imagePrompt?: string | undefined;
    responseType?: string | undefined;
    systemPrompt?: string | undefined;
    maxTokens?: number | undefined;
    temperature?: number | undefined;
    imageModel?: "gpt-image-1" | undefined;
    imageSize?: "256x256" | "512x512" | "1024x1024" | "1792x1024" | "1024x1792" | undefined;
    imageQuality?: "standard" | "hd" | undefined;
    imageStyle?: "vivid" | "natural" | undefined;
    prompt: string;
}, Promise<ResponsesAPIImageResponse & {
    generatedAt: number;
}>>;
/**
 * Generate tweet with contextual image
 */
export declare const generateTweetWithImage: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    imageStyle?: "minimal" | "vibrant" | "professional" | "artistic" | undefined;
    tweetContext?: {
        authorHandle?: string | undefined;
        engagement?: {
            likes: number;
            retweets: number;
            replies: number;
        } | undefined;
        authorDisplayName?: string | undefined;
        authorIsVerified?: boolean | undefined;
    } | undefined;
    responseStyle?: string | undefined;
    userContext?: {
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        writingStyle?: string | undefined;
        brand?: string | undefined;
    } | undefined;
    maxLength?: number | undefined;
    tweetContent: string;
}, Promise<{
    textContent: string;
    characterCount: number;
    imageData: any;
    hasImage: boolean;
    responseStyle: string;
    imageStyle: "minimal" | "vibrant" | "professional" | "artistic" | undefined;
    model: string;
    generatedAt: number;
    estimatedEngagement: {
        likes: number;
        retweets: number;
        replies: number;
    };
}>>;
/**
 * Generate image for existing response content
 */
export declare const generateImageForResponse: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    platform?: "twitter" | "instagram" | "linkedin" | undefined;
    imageStyle?: "minimal" | "vibrant" | "professional" | "artistic" | undefined;
    responseContext?: string | undefined;
    responseContent: string;
}, Promise<ImageGenerationResponse & {
    responseContent?: string;
    platform?: string;
    style?: string;
    generatedAt: number;
}>>;
/**
 * Generate multiple image variations for a single prompt
 */
export declare const generateImageVariations: import("convex/server").RegisteredAction<"public", {
    userId?: string | undefined;
    model?: "dall-e-2" | "dall-e-3" | undefined;
    size?: "256x256" | "512x512" | "1024x1024" | "1792x1024" | "1024x1792" | undefined;
    count?: number | undefined;
    styles?: ("vivid" | "natural")[] | undefined;
    prompt: string;
}, Promise<{
    variations: {
        variationIndex: number;
        style: "vivid" | "natural";
        url?: string;
        base64?: string;
        revisedPrompt?: string;
        model: string;
        usage?: {
            totalTokens: number;
        };
    }[];
    originalPrompt: string;
    totalCount: number;
    requestedCount: number;
    generatedAt: number;
}>>;
/**
 * Test image generation functionality
 */
export declare const testImageGeneration: import("convex/server").RegisteredAction<"public", {
    testPrompt?: string | undefined;
}, Promise<{
    success: boolean;
    connectionTest: boolean;
    imageGeneration: boolean;
    imageUrl: string | undefined;
    testPrompt: string;
    model: string;
    testedAt: number;
    error?: undefined;
} | {
    success: boolean;
    connectionTest: boolean;
    imageGeneration: boolean;
    error: string;
    testPrompt: string | undefined;
    testedAt: number;
    imageUrl?: undefined;
    model?: undefined;
}>>;
