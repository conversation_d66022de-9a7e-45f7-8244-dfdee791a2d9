import { action } from "../_generated/server";
import { v } from "convex/values";
import { getOpenRouterClient } from "../lib/openrouter_client";
import { checkRateLimit } from "../lib/rate_limiter";
import { withAICache } from "../lib/aiResponseCache";
import { internal } from "../_generated/api";
/**
 * Analyze mention sentiment for financial/trading context
 */
export const analyzeMentionSentiment = action({
    args: {
        mentionContent: v.string(),
        mentionAuthor: v.string(),
        mentionAuthorHandle: v.string(),
        mentionType: v.string(),
        accountHandle: v.string(), // The account being mentioned
        engagement: v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
        }),
        priority: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            // Enhanced prompt for financial sentiment analysis
            const sentimentPrompt = `
You are an expert financial sentiment analyst specializing in crypto/trading discussions on social media.

Analyze this mention for financial sentiment:

Content: "${args.mentionContent}"
Author: @${args.mentionAuthorHandle} (${args.mentionAuthor})
Mention Type: ${args.mentionType}
About Account: @${args.accountHandle}
Engagement: ${args.engagement.likes} likes, ${args.engagement.retweets} retweets, ${args.engagement.replies} replies

CONTEXT: This is a mention about @${args.accountHandle} in a trading/crypto context. Focus on:
1. Financial sentiment (bullish = positive outlook, bearish = negative outlook)
2. Market psychology (fear, greed, FOMO, excitement)
3. Trading implications
4. Price movement sentiment
5. Community sentiment

Return analysis in this EXACT JSON format:
{
  "sentiment": "bullish" | "bearish" | "neutral",
  "sentimentScore": number (1-100, where 1-25=very bearish, 26-45=bearish, 46-55=neutral, 56-75=bullish, 76-100=very bullish),
  "confidence": number (0-1, how confident you are in this analysis),
  "marketSentiment": {
    "bullishScore": number (0-100),
    "bearishScore": number (0-100), 
    "neutralScore": number (0-100),
    "marketContext": ["price_action" | "fundamentals" | "technical_analysis" | "community" | "fomo" | "hype" | "fear" | "uncertainty"]
  },
  "emotions": {
    "excitement": number (0-100),
    "fear": number (0-100),
    "greed": number (0-100),
    "fomo": number (0-100),
    "panic": number (0-100)
  },
  "reasoning": "Brief explanation of why this sentiment was assigned",
  "keyWords": ["word1", "word2", "word3"] (max 5 words that influenced the sentiment)
}

IMPORTANT: Ensure bullishScore + bearishScore + neutralScore = 100`;
            // 🚀 AI COST OPTIMIZATION: Use caching to reduce costs by 60-80%
            const contentForCaching = `${args.mentionContent} | ${args.mentionAuthor} | ${args.accountHandle}`;
            const response = await withAICache(ctx, contentForCaching, "sentiment_analysis", async () => {
                console.log(`🔍 AI CACHE: Cache miss for sentiment analysis, calling AI model`);
                const client = getOpenRouterClient();
                return await client.generateCompletion(sentimentPrompt, {
                    model: "google/gemini-2.0-flash-exp:free",
                    systemPrompt: `You are a financial sentiment analysis expert. Always return valid JSON. Focus on trading/investment sentiment rather than general positivity/negativity.`,
                    maxTokens: 600,
                    temperature: 0.2, // Low temperature for consistent analysis
                });
            }, {
                model: "google/gemini-2.0-flash-exp:free",
                additionalContext: `${args.mentionType}_${args.accountHandle}`,
                ttl: 24 * 60 * 60 * 1000, // 24 hour cache
                tokensUsed: 600, // Estimated tokens
                cost: 0.001, // Estimated cost
            });
            let sentimentResult;
            try {
                const parsed = JSON.parse(response.content);
                // Validate and construct the result
                sentimentResult = {
                    sentiment: parsed.sentiment || "neutral",
                    sentimentScore: Math.max(1, Math.min(100, parsed.sentimentScore || 50)),
                    confidence: Math.max(0, Math.min(1, parsed.confidence || 0.7)),
                    marketSentiment: {
                        bullishScore: Math.max(0, Math.min(100, parsed.marketSentiment?.bullishScore || 0)),
                        bearishScore: Math.max(0, Math.min(100, parsed.marketSentiment?.bearishScore || 0)),
                        neutralScore: Math.max(0, Math.min(100, parsed.marketSentiment?.neutralScore || 100)),
                        marketContext: Array.isArray(parsed.marketSentiment?.marketContext)
                            ? parsed.marketSentiment.marketContext.slice(0, 5)
                            : ["community"],
                    },
                    emotions: parsed.emotions ? {
                        excitement: Math.max(0, Math.min(100, parsed.emotions.excitement || 0)),
                        fear: Math.max(0, Math.min(100, parsed.emotions.fear || 0)),
                        greed: Math.max(0, Math.min(100, parsed.emotions.greed || 0)),
                        fomo: Math.max(0, Math.min(100, parsed.emotions.fomo || 0)),
                        panic: Math.max(0, Math.min(100, parsed.emotions.panic || 0)),
                    } : undefined,
                    reasoning: parsed.reasoning || "Automated sentiment analysis",
                    keyWords: Array.isArray(parsed.keyWords)
                        ? parsed.keyWords.slice(0, 5)
                        : extractKeyWordsFromContent(args.mentionContent),
                    analysisModel: "google/gemini-2.0-flash-exp:free",
                    analyzedAt: Date.now(),
                };
                // Ensure market sentiment scores add up to 100
                const total = sentimentResult.marketSentiment.bullishScore +
                    sentimentResult.marketSentiment.bearishScore +
                    sentimentResult.marketSentiment.neutralScore;
                if (total !== 100 && total > 0) {
                    const factor = 100 / total;
                    sentimentResult.marketSentiment.bullishScore = Math.round(sentimentResult.marketSentiment.bullishScore * factor);
                    sentimentResult.marketSentiment.bearishScore = Math.round(sentimentResult.marketSentiment.bearishScore * factor);
                    sentimentResult.marketSentiment.neutralScore = 100 - sentimentResult.marketSentiment.bullishScore - sentimentResult.marketSentiment.bearishScore;
                }
            }
            catch (parseError) {
                console.error('Failed to parse sentiment analysis JSON:', response.content);
                // Fallback analysis using simple keyword matching
                sentimentResult = getFallbackSentimentAnalysis(args.mentionContent, args.accountHandle);
            }
            return sentimentResult;
        }
        catch (error) {
            console.error('Sentiment analysis failed:', error);
            // Return fallback analysis
            return getFallbackSentimentAnalysis(args.mentionContent, args.accountHandle);
        }
    },
});
/**
 * Batch analyze sentiment for multiple mentions
 */
export const batchAnalyzeSentiment = action({
    args: {
        mentions: v.array(v.object({
            id: v.string(),
            content: v.string(),
            author: v.string(),
            authorHandle: v.string(),
            accountHandle: v.string(),
            engagement: v.object({
                likes: v.number(),
                retweets: v.number(),
                replies: v.number(),
            }),
        })),
        maxConcurrent: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        // 🔐 SECURITY: Apply rate limiting for batch AI operations
        await checkRateLimit(ctx, 'batchAnalyzeSentiment', 'expensive');
        const maxConcurrent = args.maxConcurrent || 5;
        const results = [];
        // 🚀 PERFORMANCE FIX: Intelligent batch processing with single AI call per batch
        console.log(`🔍 AI OPTIMIZATION: Processing ${args.mentions.length} mentions with intelligent batching`);
        for (let i = 0; i < args.mentions.length; i += maxConcurrent) {
            const batch = args.mentions.slice(i, i + maxConcurrent);
            try {
                // 🚀 COST OPTIMIZATION: Process entire batch in single AI call
                const batchPrompt = `Analyze sentiment for these ${batch.length} financial/trading mentions. Return a JSON array with sentiment analysis for each mention in order:

${batch.map((mention, idx) => `${idx + 1}. Author: @${mention.authorHandle} | Content: "${mention.content}" | Engagement: ${mention.engagement.likes}L ${mention.engagement.retweets}RT ${mention.engagement.replies}R`).join('\n')}

Return JSON array format:
[
  {
    "sentiment": "bullish|bearish|neutral",
    "sentimentScore": 1-100,
    "confidence": 0.0-1.0,
    "marketSentiment": {
      "bullishScore": 0-100,
      "bearishScore": 0-100,
      "neutralScore": 0-100,
      "marketContext": ["price_action", "fundamentals", "technical_analysis"]
    },
    "emotions": {
      "excitement": 0-100,
      "fear": 0-100,
      "greed": 0-100,
      "fomo": 0-100,
      "panic": 0-100
    },
    "reasoning": "Brief explanation",
    "keyWords": ["word1", "word2", "word3"]
  }
]`;
                const client = getOpenRouterClient();
                const response = await client.generateCompletion(batchPrompt, {
                    model: "google/gemini-2.0-flash-exp:free",
                    systemPrompt: "You are a financial sentiment analysis expert. Always return valid JSON array. Focus on trading/investment sentiment.",
                    maxTokens: 2000,
                    temperature: 0.2,
                });
                const batchResults = JSON.parse(response.content);
                // Map results back to mentions
                for (let j = 0; j < batch.length; j++) {
                    const mention = batch[j];
                    const sentimentResult = batchResults[j] || null;
                    results.push({
                        id: mention.id,
                        sentiment: sentimentResult,
                        error: sentimentResult ? undefined : 'Failed to parse AI response'
                    });
                }
                console.log(`🚀 AI COST SAVINGS: Processed ${batch.length} mentions in 1 AI call instead of ${batch.length} separate calls`);
            }
            catch (error) {
                console.error(`Batch sentiment analysis failed for batch ${i}:`, error);
                // Fallback to individual processing for this batch
                const batchPromises = batch.map(async (mention) => {
                    try {
                        const sentiment = await ctx.runAction(internal.ai.sentimentAnalysis.analyzeMentionSentiment, {
                            mentionContent: mention.content,
                            mentionAuthor: mention.author,
                            mentionAuthorHandle: mention.authorHandle,
                            mentionType: "mention",
                            accountHandle: mention.accountHandle,
                            engagement: mention.engagement,
                        });
                        return { id: mention.id, sentiment, error: undefined };
                    }
                    catch (error) {
                        console.error(`Failed to analyze sentiment for mention ${mention.id}:`, error);
                        return {
                            id: mention.id,
                            sentiment: null,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        };
                    }
                });
                const batchResults = await Promise.allSettled(batchPromises);
                for (const result of batchResults) {
                    if (result.status === "fulfilled") {
                        results.push(result.value);
                    }
                    else {
                        results.push({
                            id: "unknown",
                            sentiment: null,
                            error: result.reason instanceof Error ? result.reason.message : 'Promise rejected'
                        });
                    }
                }
                // Small delay between batches
                if (i + maxConcurrent < args.mentions.length) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
        }
        return {
            total: args.mentions.length,
            successful: results.filter(r => r.sentiment !== null).length,
            failed: results.filter(r => r.sentiment === null).length,
            results,
        };
    },
});
/**
 * Fallback sentiment analysis using keyword matching
 */
function getFallbackSentimentAnalysis(content, _accountHandle) {
    const lowercaseContent = content.toLowerCase();
    // Financial sentiment keywords
    const bullishKeywords = [
        'bullish', 'moon', 'pump', 'rocket', 'gem', 'buy', 'long', 'hold', 'hodl',
        'diamond hands', 'to the moon', 'ath', 'breakout', 'rally', 'surge',
        'gains', 'profit', 'winning', 'strong', 'solid', 'promising'
    ];
    const bearishKeywords = [
        'bearish', 'dump', 'crash', 'sell', 'short', 'rekt', 'scam', 'rug',
        'paper hands', 'fud', 'dip', 'falling', 'dropping', 'loss', 'bad',
        'weak', 'failed', 'terrible', 'avoid', 'risky'
    ];
    const neutralKeywords = [
        'analysis', 'research', 'study', 'watching', 'monitoring', 'neutral',
        'sideways', 'consolidating', 'waiting', 'uncertain'
    ];
    let bullishScore = 0;
    let bearishScore = 0;
    let neutralScore = 0;
    const foundKeywords = [];
    // Count keyword matches
    bullishKeywords.forEach(keyword => {
        if (lowercaseContent.includes(keyword)) {
            bullishScore += 10;
            foundKeywords.push(keyword);
        }
    });
    bearishKeywords.forEach(keyword => {
        if (lowercaseContent.includes(keyword)) {
            bearishScore += 10;
            foundKeywords.push(keyword);
        }
    });
    neutralKeywords.forEach(keyword => {
        if (lowercaseContent.includes(keyword)) {
            neutralScore += 5;
            foundKeywords.push(keyword);
        }
    });
    // Determine primary sentiment
    let sentiment = "neutral";
    let sentimentScore = 50;
    if (bullishScore > bearishScore && bullishScore > neutralScore) {
        sentiment = "bullish";
        sentimentScore = Math.min(75, 50 + bullishScore);
    }
    else if (bearishScore > bullishScore && bearishScore > neutralScore) {
        sentiment = "bearish";
        sentimentScore = Math.max(25, 50 - bearishScore);
    }
    // Normalize market sentiment scores
    const total = bullishScore + bearishScore + neutralScore;
    if (total === 0) {
        neutralScore = 100;
    }
    else {
        bullishScore = Math.round((bullishScore / total) * 100);
        bearishScore = Math.round((bearishScore / total) * 100);
        neutralScore = 100 - bullishScore - bearishScore;
    }
    return {
        sentiment,
        sentimentScore,
        confidence: 0.6, // Lower confidence for fallback analysis
        marketSentiment: {
            bullishScore,
            bearishScore,
            neutralScore,
            marketContext: ["community"],
        },
        reasoning: "Keyword-based fallback analysis",
        keyWords: foundKeywords.slice(0, 5),
        analysisModel: "fallback-keyword-matching",
        analyzedAt: Date.now(),
    };
}
/**
 * Extract key sentiment words from content
 */
function extractKeyWordsFromContent(content) {
    const words = content.toLowerCase().split(/\s+/);
    const sentimentWords = [
        'bullish', 'bearish', 'moon', 'pump', 'dump', 'buy', 'sell', 'hold',
        'long', 'short', 'gem', 'scam', 'rocket', 'crash', 'gains', 'loss'
    ];
    return words.filter(word => sentimentWords.includes(word)).slice(0, 5);
}
