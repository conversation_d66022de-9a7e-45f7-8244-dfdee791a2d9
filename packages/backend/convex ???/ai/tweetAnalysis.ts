import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getOpenRouterClient } from "../lib/openrouter_client";
import { ANALYSIS_TEMPLATES, buildPromptContext } from "../lib/prompt_templates";

/**
 * Analyze if a tweet is worth responding to
 */
export const analyzeTweetWorthiness = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorDisplayName: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const promptContext = buildPromptContext(args.content, {
        authorInfo: {
          handle: args.authorHandle,
          displayName: args.authorDisplayName,
          isVerified: args.authorIsVerified,
          followerCount: args.authorFollowerCount,
        },
        tweetMetadata: {
          engagement: args.engagement,
          createdAt: Date.now(),
        },
        userContext: args.userContext,
      });

      const template = ANALYSIS_TEMPLATES.worthinessAnalysis;
      
      const response = await client.generateCompletion(
        template.userPrompt(promptContext),
        {
          systemPrompt: template.systemPrompt,
          maxTokens: template.maxTokens,
          temperature: template.temperature,
        }
      );

      // Parse JSON response
      let analysis;
      try {
        analysis = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse analysis JSON:', response.content);
        // Fallback analysis
        analysis = {
          shouldRespond: false,
          confidence: 0.1,
          priority: 'low',
          reasons: ['Failed to analyze tweet'],
          suggestedStrategy: 'skip',
          estimatedEngagement: { likes: 0, retweets: 0, replies: 0 },
        };
      }

      return {
        tweetId: args.tweetId,
        analysis,
        model: response.model,
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet worthiness analysis failed:', error);
      throw new Error(`Tweet analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Analyze tweet sentiment and emotional context
 */
export const analyzeTweetSentiment = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const promptContext = buildPromptContext(args.content, {
        authorInfo: {
          handle: args.authorHandle,
          displayName: args.authorHandle,
        },
      });

      const template = ANALYSIS_TEMPLATES.sentimentAnalysis;
      
      const response = await client.generateCompletion(
        template.userPrompt(promptContext),
        {
          systemPrompt: template.systemPrompt,
          maxTokens: template.maxTokens,
          temperature: template.temperature,
        }
      );

      // Parse JSON response
      let sentiment;
      try {
        sentiment = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse sentiment JSON:', response.content);
        // Fallback sentiment
        sentiment = {
          sentiment: 'neutral',
          confidence: 0.1,
          emotions: ['unknown'],
          topics: ['general'],
          urgency: 'low',
          responseTone: 'informative',
        };
      }

      return {
        tweetId: args.tweetId,
        sentiment,
        model: response.model,
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet sentiment analysis failed:', error);
      throw new Error(`Sentiment analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Extract topics and themes from a tweet
 */
export const extractTweetTopics = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const promptContext = buildPromptContext(args.content);
      const template = ANALYSIS_TEMPLATES.topicExtraction;
      
      const response = await client.generateCompletion(
        template.userPrompt(promptContext),
        {
          systemPrompt: template.systemPrompt,
          maxTokens: template.maxTokens,
          temperature: template.temperature,
        }
      );

      // Parse JSON response
      let topics;
      try {
        topics = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse topics JSON:', response.content);
        // Fallback topics
        topics = {
          mainTopics: ['general'],
          industries: ['social media'],
          contentType: 'general',
          suggestedHashtags: [],
          complexity: 'simple',
        };
      }

      return {
        tweetId: args.tweetId,
        topics,
        model: response.model,
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet topic extraction failed:', error);
      throw new Error(`Topic extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Classify tweet priority for response queue
 */
export const classifyTweetPriority = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
    }),
    recency: v.number(), // Hours since posted
  },
  handler: async (ctx, args) => {
    try {
      const client = getOpenRouterClient();

      // Build priority scoring prompt
      const priorityPrompt = `
Analyze this tweet and assign a priority score for response queue:

Tweet: "${args.content}"
Author: @${args.authorHandle} ${args.authorIsVerified ? '(Verified)' : ''}
Followers: ${args.authorFollowerCount || 0}
Engagement: ${args.engagement.likes} likes, ${args.engagement.retweets} retweets, ${args.engagement.replies} replies
Posted: ${args.recency} hours ago

Priority factors to consider:
1. Author influence (verification, follower count)
2. Current engagement (viral potential)
3. Content relevance and quality
4. Recency (time-sensitive vs evergreen)
5. Response opportunity (questions, mentions, discussions)

Provide analysis in this exact JSON format:
{
  "priority": "urgent" | "high" | "medium" | "low",
  "score": number (0-100),
  "factors": {
    "authorInfluence": number (0-25),
    "engagement": number (0-25),
    "contentQuality": number (0-25),
    "timeliness": number (0-25)
  },
  "reasoning": "explanation",
  "recommendedAction": "respond_immediately" | "respond_soon" | "respond_later" | "skip"
}`;

      const response = await client.generateCompletion(
        priorityPrompt,
        {
          systemPrompt: 'You are an expert social media strategist who prioritizes content for maximum engagement and brand impact.',
          maxTokens: 400,
          temperature: 0.3,
        }
      );

      // Parse JSON response
      let priority;
      try {
        priority = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse priority JSON:', response.content);
        // Calculate fallback priority based on basic metrics
        const authorScore = (args.authorFollowerCount || 0) > 10000 ? 20 : 10;
        const verifiedBonus = args.authorIsVerified ? 15 : 0;
        const engagementScore = Math.min(25, (args.engagement.likes + args.engagement.retweets) / 10);
        const timelinessScore = args.recency < 2 ? 25 : args.recency < 6 ? 15 : 5;
        
        const totalScore = authorScore + verifiedBonus + engagementScore + timelinessScore;
        
        priority = {
          priority: totalScore > 70 ? 'high' : totalScore > 40 ? 'medium' : 'low',
          score: totalScore,
          factors: {
            authorInfluence: authorScore + verifiedBonus,
            engagement: engagementScore,
            contentQuality: 15, // Default
            timeliness: timelinessScore,
          },
          reasoning: 'Fallback scoring due to analysis failure',
          recommendedAction: totalScore > 70 ? 'respond_soon' : 'respond_later',
        };
      }

      return {
        tweetId: args.tweetId,
        priority,
        model: response.model,
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet priority classification failed:', error);
      throw new Error(`Priority classification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Comprehensive tweet analysis combining all methods
 */
export const analyzeTweetComprehensive = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorDisplayName: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    createdAt: v.number(),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    try {
      const recency = (Date.now() - args.createdAt) / (1000 * 60 * 60); // Hours

      // Run all analyses in parallel for efficiency
      const [worthinessResult, sentimentResult, topicsResult, priorityResult] = await Promise.allSettled([
        ctx.runAction(api.ai.tweetAnalysis.analyzeTweetWorthiness, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
          authorDisplayName: args.authorDisplayName,
          authorIsVerified: args.authorIsVerified,
          authorFollowerCount: args.authorFollowerCount,
          engagement: args.engagement,
          userContext: args.userContext,
        }),
        ctx.runAction(api.ai.tweetAnalysis.analyzeTweetSentiment, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
        }),
        ctx.runAction(api.ai.tweetAnalysis.extractTweetTopics, {
          tweetId: args.tweetId,
          content: args.content,
        }),
        ctx.runAction(api.ai.tweetAnalysis.classifyTweetPriority, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
          authorIsVerified: args.authorIsVerified,
          authorFollowerCount: args.authorFollowerCount,
          engagement: args.engagement,
          recency,
        }),
      ]);

      return {
        tweetId: args.tweetId,
        analysis: {
          worthiness: worthinessResult.status === 'fulfilled' ? worthinessResult.value.analysis : null,
          sentiment: sentimentResult.status === 'fulfilled' ? sentimentResult.value.sentiment : null,
          topics: topicsResult.status === 'fulfilled' ? topicsResult.value.topics : null,
          priority: priorityResult.status === 'fulfilled' ? priorityResult.value.priority : null,
        },
        errors: [
          ...(worthinessResult.status === 'rejected' ? [{ type: 'worthiness', error: worthinessResult.reason }] : []),
          ...(sentimentResult.status === 'rejected' ? [{ type: 'sentiment', error: sentimentResult.reason }] : []),
          ...(topicsResult.status === 'rejected' ? [{ type: 'topics', error: topicsResult.reason }] : []),
          ...(priorityResult.status === 'rejected' ? [{ type: 'priority', error: priorityResult.reason }] : []),
        ],
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Comprehensive tweet analysis failed:', error);
      throw new Error(`Comprehensive analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

