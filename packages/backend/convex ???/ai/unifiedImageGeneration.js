import { action } from "../_generated/server";
import { v } from "convex/values";
import { generateImage, generateSocialMediaImage, generateImageVariations, testImageGeneration } from "../lib/unified_image_client";
/**
 * Unified image generation using best available provider (OpenAI + Fal.ai)
 */
export const generateUnifiedImage = action({
    args: {
        prompt: v.string(),
        provider: v.optional(v.union(v.literal("openai"), v.literal("fal"), v.literal("auto"))),
        strategy: v.optional(v.union(v.literal("ultra_fast"), v.literal("fast"), v.literal("quality"), v.literal("bulk"), v.literal("auto"))),
        size: v.optional(v.string()),
        quality: v.optional(v.union(v.literal("standard"), v.literal("hd"))),
        style: v.optional(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic"), v.literal("vivid"), v.literal("natural"))),
        aspectRatio: v.optional(v.union(v.literal("square"), v.literal("landscape"), v.literal("portrait"))),
        platform: v.optional(v.union(v.literal("twitter"), v.literal("instagram"), v.literal("linkedin"))),
        userId: v.optional(v.string()),
        // Provider-specific options
        openaiOptions: v.optional(v.object({
            model: v.optional(v.union(v.literal("dall-e-2"), v.literal("dall-e-3"))),
            responseFormat: v.optional(v.union(v.literal("url"), v.literal("b64_json"))),
        })),
        falOptions: v.optional(v.object({
            numInferenceSteps: v.optional(v.number()),
            guidanceScale: v.optional(v.number()),
            seed: v.optional(v.number()),
            expandPrompt: v.optional(v.boolean()),
            format: v.optional(v.union(v.literal("jpeg"), v.literal("png"))),
        })),
    },
    handler: async (ctx, args) => {
        try {
            const request = {
                prompt: args.prompt,
                provider: args.provider,
                strategy: args.strategy,
                size: args.size,
                quality: args.quality,
                style: args.style,
                aspectRatio: args.aspectRatio,
                platform: args.platform,
                openaiOptions: args.openaiOptions ? {
                    model: args.openaiOptions.model,
                    responseFormat: args.openaiOptions.responseFormat,
                    user: args.userId,
                } : undefined,
                falOptions: args.falOptions,
            };
            const response = await generateImage(request);
            return {
                ...response,
                requestId: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            };
        }
        catch (error) {
            console.error('Unified image generation failed:', error);
            throw new Error(`Unified image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate social media optimized images with intelligent provider selection
 */
export const generateSocialImage = action({
    args: {
        prompt: v.string(),
        platform: v.optional(v.union(v.literal("twitter"), v.literal("instagram"), v.literal("linkedin"))),
        style: v.optional(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic"))),
        provider: v.optional(v.union(v.literal("openai"), v.literal("fal"), v.literal("auto"))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const response = await generateSocialMediaImage(args.prompt, {
                platform: args.platform,
                style: args.style,
                provider: args.provider || 'auto',
            });
            // Generate social media optimization tips
            const tips = [
                args.platform === 'twitter' ? "Optimized for Twitter's 16:9 aspect ratio" : "Square format for maximum engagement",
                `${response.provider === 'fal' ? 'Fal.ai Flux Pro' : 'OpenAI DALL-E'} used for optimal ${args.style || 'professional'} style`,
                "High resolution suitable for both web and print use",
                args.platform === 'instagram' ? "Perfect for Instagram feed and stories" : "Professional quality for business use",
                "AI-generated content - ensure compliance with platform policies",
            ];
            return {
                ...response,
                platform: args.platform,
                optimizedPrompt: args.prompt,
                socialMediaTips: tips,
            };
        }
        catch (error) {
            console.error('Social media image generation failed:', error);
            throw new Error(`Social media image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate multiple image variations using optimal provider mix
 */
export const generateUnifiedImageVariations = action({
    args: {
        prompt: v.string(),
        count: v.optional(v.number()),
        strategy: v.optional(v.union(v.literal("ultra_fast"), v.literal("fast"), v.literal("quality"), v.literal("bulk"), v.literal("auto"))),
        styles: v.optional(v.array(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic")))),
        aspectRatio: v.optional(v.union(v.literal("square"), v.literal("landscape"), v.literal("portrait"))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const count = Math.min(args.count || 3, 6); // Limit to 6 variations max
            const styles = args.styles || ['professional', 'vibrant', 'artistic'];
            const variations = await generateImageVariations(args.prompt, count, {
                strategy: args.strategy,
                aspectRatio: args.aspectRatio,
            });
            // Add style metadata to variations
            const enhancedVariations = variations.map((variation, index) => ({
                ...variation,
                variationIndex: index,
                suggestedStyle: styles[index % styles.length],
                variationId: `var_${Date.now()}_${index}`,
            }));
            return {
                variations: enhancedVariations,
                originalPrompt: args.prompt,
                totalGenerated: enhancedVariations.length,
                requestedCount: count,
                providerMix: {
                    openai: enhancedVariations.filter(v => v.provider === 'openai').length,
                    fal: enhancedVariations.filter(v => v.provider === 'fal').length,
                },
                estimatedTotalCost: enhancedVariations.reduce((sum, v) => sum + v.estimatedCost, 0),
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Unified image variations failed:', error);
            throw new Error(`Unified image variations failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Advanced image generation for specific content types
 */
export const generateContentImage = action({
    args: {
        contentType: v.union(v.literal("tweet_response"), v.literal("blog_header"), v.literal("social_post"), v.literal("presentation"), v.literal("marketing")),
        prompt: v.string(),
        contentContext: v.optional(v.object({
            topic: v.optional(v.string()),
            targetAudience: v.optional(v.string()),
            brand: v.optional(v.string()),
            tone: v.optional(v.string()),
        })),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            // Content type specific optimization
            const contentConfig = {
                tweet_response: {
                    platform: 'twitter',
                    aspectRatio: 'landscape',
                    style: 'vibrant',
                    provider: 'auto',
                },
                blog_header: {
                    aspectRatio: 'landscape',
                    style: 'professional',
                    provider: 'fal', // Fal.ai better for artistic headers
                },
                social_post: {
                    platform: 'instagram',
                    aspectRatio: 'square',
                    style: 'vibrant',
                    provider: 'auto',
                },
                presentation: {
                    aspectRatio: 'landscape',
                    style: 'professional',
                    provider: 'openai', // OpenAI more consistent for business
                },
                marketing: {
                    style: 'artistic',
                    provider: 'fal', // Fal.ai better for creative marketing
                },
            };
            const config = contentConfig[args.contentType];
            // Enhance prompt with content context
            let enhancedPrompt = args.prompt;
            if (args.contentContext) {
                const { topic, targetAudience, brand, tone } = args.contentContext;
                if (topic)
                    enhancedPrompt += `, related to ${topic}`;
                if (targetAudience)
                    enhancedPrompt += `, targeting ${targetAudience}`;
                if (brand)
                    enhancedPrompt += `, ${brand} brand style`;
                if (tone)
                    enhancedPrompt += `, ${tone} tone`;
            }
            const response = await generateImage({
                prompt: enhancedPrompt,
                ...config,
                strategy: 'quality',
            });
            return {
                ...response,
                contentType: args.contentType,
                originalPrompt: args.prompt,
                enhancedPrompt,
                contentContext: args.contentContext,
                optimizationApplied: true,
                contentOptimizations: [
                    `Optimized for ${args.contentType.replace('_', ' ')} content`,
                    `Using ${response.provider} for best ${config.style} results`,
                    config.platform ? `${config.platform} platform optimization` : 'Universal format',
                    args.contentContext?.brand ? `${args.contentContext.brand} brand alignment` : 'Generic branding',
                ],
            };
        }
        catch (error) {
            console.error('Content image generation failed:', error);
            throw new Error(`Content image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Test all image generation providers and capabilities
 */
export const testUnifiedImageGeneration = action({
    args: {
        includeGenerationTest: v.optional(v.boolean()),
        testPrompt: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const testPrompt = args.testPrompt || "A simple test image of a colorful geometric pattern";
            // Test provider connectivity
            const providerTests = await testImageGeneration();
            let generationTests = null;
            // Optional full generation test
            if (args.includeGenerationTest && providerTests.overall) {
                try {
                    const testResponse = await generateImage({
                        prompt: testPrompt,
                        provider: 'auto',
                        strategy: 'fast',
                        size: '1024x1024',
                        style: 'minimal',
                    });
                    generationTests = {
                        success: true,
                        provider: testResponse.provider,
                        model: testResponse.model,
                        url: testResponse.url,
                        estimatedCost: testResponse.estimatedCost,
                        responseTime: Date.now() - testResponse.generatedAt,
                    };
                }
                catch (genError) {
                    generationTests = {
                        success: false,
                        error: genError instanceof Error ? genError.message : 'Unknown error',
                    };
                }
            }
            return {
                providerTests,
                generationTests,
                capabilities: {
                    providers: ['openai', 'fal'],
                    models: {
                        openai: ['dall-e-2', 'dall-e-3'],
                        fal: ['flux-pro'],
                    },
                    supportedFormats: ['url', 'b64_json'],
                    supportedSizes: ['256x256', '512x512', '1024x1024', '1792x1024', '1024x1792'],
                    supportedStyles: ['minimal', 'vibrant', 'professional', 'artistic', 'vivid', 'natural'],
                    supportedPlatforms: ['twitter', 'instagram', 'linkedin'],
                    intelligentSelection: true,
                    fallbackSupport: true,
                },
                testPrompt,
                testedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Unified image generation test failed:', error);
            return {
                providerTests: { openai: false, fal: false, overall: false },
                generationTests: null,
                error: error instanceof Error ? error.message : 'Unknown error',
                testPrompt: args.testPrompt,
                testedAt: Date.now(),
            };
        }
    },
});
/**
 * Get image generation analytics and costs
 */
export const getImageGenerationAnalytics = action({
    args: {
        timeRange: v.optional(v.union(v.literal("day"), v.literal("week"), v.literal("month"))),
        provider: v.optional(v.union(v.literal("openai"), v.literal("fal"), v.literal("all"))),
    },
    handler: async (ctx, args) => {
        // This would typically query a database for analytics
        // For now, return mock analytics structure
        const timeRange = args.timeRange || 'day';
        const provider = args.provider || 'all';
        return {
            timeRange,
            provider,
            totalImages: 0, // Would come from database
            totalCost: 0,
            providerBreakdown: {
                openai: { count: 0, cost: 0, avgCostPerImage: 0.040 },
                fal: { count: 0, cost: 0, avgCostPerImage: 0.055 },
            },
            styleBreakdown: {
                professional: 0,
                artistic: 0,
                vibrant: 0,
                minimal: 0,
            },
            platformBreakdown: {
                twitter: 0,
                instagram: 0,
                linkedin: 0,
                general: 0,
            },
            qualityMetrics: {
                averageGenerationTime: 0,
                successRate: 0,
                fallbackRate: 0,
            },
            recommendations: [
                "Consider using Fal.ai for artistic content to improve visual quality",
                "OpenAI DALL-E works best for professional and consistent imagery",
                "Twitter images perform better with landscape orientation",
                "Batch generation can reduce overall costs",
            ],
            generatedAt: Date.now(),
        };
    },
});
