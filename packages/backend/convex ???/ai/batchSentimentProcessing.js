import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";
/**
 * Start sentiment analysis for unprocessed mentions
 */
export const startSentimentAnalysisForUnprocessed = action({
    args: {
        maxBatchSize: v.optional(v.number()),
        accountId: v.optional(v.string()), // Optional: analyze only specific account's mentions
    },
    handler: async (ctx, args) => {
        const maxBatchSize = args.maxBatchSize || 50;
        try {
            console.log('🔍 Starting sentiment analysis for unprocessed mentions...');
            // Get unprocessed mentions (those without sentiment analysis)
            const unprocessedMentions = await ctx.runQuery(internal.mentions.mentionQueries.getUnprocessedMentionsForSentiment, {
                limit: maxBatchSize,
                accountId: args.accountId,
            });
            if (!unprocessedMentions || unprocessedMentions.length === 0) {
                return {
                    success: true,
                    message: "No unprocessed mentions found",
                    processed: 0,
                    total: 0,
                    results: [],
                };
            }
            console.log(`📊 Found ${unprocessedMentions.length} unprocessed mentions`);
            // Transform mentions for batch analysis
            const mentionsForAnalysis = unprocessedMentions.map(mention => ({
                id: mention._id,
                content: mention.mentionContent || mention.content || "",
                author: mention.mentionAuthor || mention.authorName || "Unknown",
                authorHandle: mention.mentionAuthorHandle || mention.authorHandle || "unknown",
                accountHandle: mention.monitoredAccount?.handle || "unknown",
                engagement: {
                    likes: mention.engagement?.likes || 0,
                    retweets: mention.engagement?.retweets || 0,
                    replies: mention.engagement?.replies || 0,
                },
            }));
            // Batch analyze sentiment
            const analysisResults = await ctx.runAction(internal.ai.sentimentAnalysis.batchAnalyzeSentiment, {
                mentions: mentionsForAnalysis,
                maxConcurrent: 5,
            });
            console.log(`✅ Sentiment analysis completed: ${analysisResults.successful}/${analysisResults.total} successful`);
            // Update mentions with sentiment analysis
            const updatePromises = analysisResults.results
                .filter(result => result.sentiment !== null)
                .map(async (result) => {
                try {
                    await ctx.runMutation(internal.mentions.sentimentMutations.updateMentionSentiment, {
                        mentionId: result.id,
                        sentimentAnalysis: result.sentiment,
                    });
                    return { id: result.id, success: true };
                }
                catch (error) {
                    console.error(`Failed to update sentiment for mention ${result.id}:`, error);
                    return { id: result.id, success: false, error: error instanceof Error ? error.message : "Unknown error" };
                }
            });
            const updateResults = await Promise.all(updatePromises);
            const successfulUpdates = updateResults.filter(r => r.success).length;
            return {
                success: true,
                message: `Sentiment analysis completed for ${successfulUpdates} mentions`,
                processed: successfulUpdates,
                total: unprocessedMentions.length,
                failed: updateResults.filter(r => !r.success).length,
                results: updateResults,
                analyticsResults: {
                    totalAnalyzed: analysisResults.successful,
                    totalFailed: analysisResults.failed,
                    processingTime: Date.now(),
                },
            };
        }
        catch (error) {
            console.error('Failed to start sentiment analysis:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : "Unknown error",
                processed: 0,
                total: 0,
                results: [],
            };
        }
    },
});
/**
 * Get statistics about unprocessed mentions for sentiment analysis
 */
export const getUnprocessedSentimentStats = action({
    args: {
        accountId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const unprocessedMentions = await ctx.runQuery(internal.mentions.mentionQueries.getUnprocessedMentionsForSentiment, {
                limit: 1000, // Get count of up to 1000 unprocessed
                accountId: args.accountId,
            });
            return {
                unprocessedCount: unprocessedMentions?.length || 0,
                canProcess: (unprocessedMentions?.length || 0) > 0,
            };
        }
        catch (error) {
            console.error('Failed to get unprocessed sentiment stats:', error);
            return {
                unprocessedCount: 0,
                canProcess: false,
            };
        }
    },
});
