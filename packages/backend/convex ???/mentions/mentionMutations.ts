import { mutation, action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getAIFallbackClient } from "../lib/ai_fallback_client";
import { getResponseTemplate, buildPromptContext } from "../lib/prompt_templates";
import { createTweetIOClient } from "../lib/twitter_client";
import { Id } from "../_generated/dataModel";
import { ActionCtx, MutationCtx } from "../_generated/server";
import type { TwitterUser as TwitterUserAPI } from "../types/twitter";
import { 
  resolveUsername, 
  sanitizeMentionData, 
  convertTwitterIOUserWithFallback,
  resolveAuthorWithApiLookup,
  batchResolveAuthors
} from "../lib/twitter_utils";

// Type Definitions for clarity and safety
type TwitterUser = TwitterUserAPI;

interface Tweet {
  id: string;
  text: string;
  author_id: string;
  in_reply_to_user_id?: string;
  referenced_tweets?: { type: string; id: string }[];
  conversation_id?: string;
  public_metrics?: {
    like_count: number;
    retweet_count: number;
    reply_count: number;
    impression_count?: number;
  };
  created_at: string;
}

interface TwitterAccountFromDB {
  _id: Id<"twitterAccounts">;
  handle: string;
  displayName?: string;
  followersCount?: number;
  verified?: boolean;
  isActive?: boolean;
  isMonitoringEnabled?: boolean;
  lastChecked?: number;
  priority?: number;
}

interface MentionMetrics {
  totalProcessingTime: number;
  accountsProcessed: number;
  mentionsFound: number;
  mentionsStored: number;
  duplicatesSkipped: number;
  errors: number;
  cacheHits: number;
  apiCalls: number;
}

type ProcessAccountMentionsContext = Pick<ActionCtx, "runQuery" | "runMutation" | "auth">;

export const updateMentionProcessingStatus = mutation({
  args: {
    mentionId: v.id("mentions"),
    isProcessed: v.boolean(),
    aiAnalysisResult: v.optional(v.object({
      shouldRespond: v.boolean(),
      responseStrategy: v.optional(v.string()),
      sentiment: v.optional(v.string()),
      topics: v.optional(v.array(v.string())),
      confidence: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const { mentionId, aiAnalysisResult, ...updates } = args;

    const updateData: Record<string, unknown> = {
      ...updates,
      processedAt: Date.now(),
    };

    if (aiAnalysisResult) {
      updateData.aiAnalysisResult = aiAnalysisResult;
    }

    await ctx.db.patch(mentionId, updateData);

    return { success: true };
  },
});

export const markNotificationSent = mutation({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      isNotificationSent: true,
      notificationSentAt: Date.now(),
    });

    return { success: true };
  },
});

/**
 * Simple action for users to fetch their latest mentions from Twitter
 * This function fetches real mentions using TwitterAPI.io
 */
export const fetchUserMentions = action({
  args: {
    accountHandle: v.optional(v.string()),
    hoursBack: v.optional(v.number()),
    maxMentions: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required");
      }

      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        throw new Error("User not found");
      }

      // Get user's Twitter accounts
      const twitterAccounts = await ctx.runQuery(api.users.getUserTwitterAccounts);
      let activeAccounts: TwitterAccountFromDB[] = twitterAccounts?.filter((account: TwitterAccountFromDB) => 
        account.isActive && account.isMonitoringEnabled !== false
      ) || [];

      // Filter by specific account if requested
      if (args.accountHandle) {
        activeAccounts = activeAccounts.filter((account) => 
          account.handle.toLowerCase() === (args.accountHandle as string).toLowerCase()
        );
      }

      if (activeAccounts.length === 0) {
        return {
          success: true,
          newMentions: 0,
          message: args.accountHandle 
            ? `No active Twitter account found for handle @${args.accountHandle}`
            : "No active Twitter accounts found. Please add Twitter accounts first.",
          accountsChecked: 0,
        };
      }

      // Initialize TwitterAPI.io client
      const { createTweetIOClient } = await import("../lib/twitter_client");
      const { getTweetIOConfig } = await import("../lib/config");
      
      const config = getTweetIOConfig();
      const twitterClient = createTweetIOClient(config.apiKey, ctx as any);

      const lookbackHours = args.hoursBack || 24;
      let totalNewMentions = 0;
      const accountResults = [];

      // Process each account
      for (const account of activeAccounts) {
        try {
          // Get recent mentions for this account
          const lastMention = await ctx.runQuery(api.mentions.mentionQueries.getLatestMentionForAccount, {
            accountId: account._id
          });

          // Calculate search window
          const baseTime = Date.now() - (lookbackHours * 60 * 60 * 1000);
          const searchStartTime = lastMention 
            ? new Date(Math.max(lastMention.discoveredAt - (30 * 60 * 1000), baseTime)).toISOString()
            : new Date(baseTime).toISOString();

          // Fetch mentions from TwitterAPI.io
          const mentionResults = await twitterClient.searchMentions(account.handle, {
            maxResults: args.maxMentions || 30,
            startTime: searchStartTime,
          });

          const { tweets: mentionTweets, users: mentionUsers }: { tweets: Tweet[], users: TwitterUserAPI[] } = mentionResults;
          let newMentionsForAccount = 0;

          // Process each mention
          for (const tweet of mentionTweets) {
            // Check if we already have this mention
            const existingMention = await ctx.runQuery(api.mentions.mentionQueries.getMentionByTweetId, {
              tweetId: tweet.id
            });

            if (existingMention) {
              continue;
            }

            // PHASE 1 FIX: Never skip mentions, always use fallback data
            let author = mentionUsers.find((u) => u.id === tweet.author_id);
            let authorUsername: string;
            let authorName: string;
            
            if (!author || !author.username) {
              console.warn(`⚠️ Missing author data for mention ${tweet.id}, using fallback resolution`, { 
                authorId: tweet.author_id, 
                author: author ? 'found but no username' : 'not found',
                authorData: author 
              });
              
              // Generate fallback author data instead of skipping
              authorUsername = resolveUsername({
                user: author,
                tweet: tweet as any, // Cast to TwitterTweet for compatibility
                authorId: tweet.author_id,
                displayName: author?.name
              });
              
              authorName = author?.name || `Unknown User (${tweet.author_id.substring(0, 8)})`;
              
              // Create fallback author object if none exists
              if (!author) {
                author = {
                  id: tweet.author_id,
                  name: authorName,
                  username: authorUsername,
                  followers_count: 0,
                  verified: false,
                } as TwitterUserAPI;
              } else {
                // Update existing author with resolved username
                author.username = authorUsername;
              }
            } else {
              authorUsername = author.username;
              authorName = author.name;
            }

            // Determine mention type
            let mentionType: "mention" | "reply" | "quote" | "retweet_with_comment" = "mention";
            if (tweet.in_reply_to_user_id) {
              mentionType = "reply";
            } else if (tweet.referenced_tweets?.some((ref) => ref.type === "quoted")) {
              mentionType = "quote";
            } else if (tweet.referenced_tweets?.some((ref) => ref.type === "retweeted")) {
              mentionType = "retweet_with_comment";
            }

            // Calculate priority
            const { calculateMentionPriority } = await import("../lib/twitter_client");
            const priority = calculateMentionPriority(author);

            // Store the mention with sanitized data
            const rawMentionData = {
              mentionTweetId: tweet.id,
              mentionContent: tweet.text,
              mentionAuthor: authorName,
              mentionAuthorHandle: authorUsername,
              mentionAuthorFollowers: author.followers_count || 0,
              mentionAuthorVerified: author.verified || false,
              monitoredAccountId: account._id,
              mentionType,
              originalTweetId: tweet.conversation_id && tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
              engagement: {
                likes: tweet.public_metrics?.like_count || 0,
                retweets: tweet.public_metrics?.retweet_count || 0,
                replies: tweet.public_metrics?.reply_count || 0,
                views: tweet.public_metrics?.impression_count || 0,
              },
              priority,
              createdAt: new Date(tweet.created_at).getTime(),
              url: `https://twitter.com/${authorUsername}/status/${tweet.id}`,
            };

            // PHASE 1 FIX: Sanitize and validate mention data before storage
            const { isValid, sanitized: mentionData, warnings } = sanitizeMentionData(rawMentionData);
            
            if (warnings.length > 0) {
              console.log(`📋 Mention ${tweet.id} data sanitized:`, warnings);
            }
            
            if (!isValid) {
              console.error(`❌ Failed to sanitize mention ${tweet.id}, skipping`, { rawMentionData });
              continue; // Only skip if data is fundamentally invalid
            }

            const result = await ctx.runMutation(api.mentions.mentionMutations.storeMentionWithCheck, mentionData);
            
            if (result.action === 'created') {
              newMentionsForAccount++;
              totalNewMentions++;
            }
          }

          accountResults.push({
            handle: account.handle,
            displayName: account.displayName,
            newMentions: newMentionsForAccount,
            totalChecked: mentionTweets.length,
            searchStartTime,
          });

        } catch (accountError) {
          console.error(`Error processing @${account.handle}:`, accountError);
          accountResults.push({
            handle: account.handle,
            displayName: account.displayName,
            newMentions: 0,
            totalChecked: 0,
            error: accountError instanceof Error ? accountError.message : String(accountError),
          });
        }
      }

      return {
        success: true,
        newMentions: totalNewMentions,
        message: totalNewMentions === 0 
          ? "No new mentions found!" 
          : `Found ${totalNewMentions} new mentions!`,
        accountsChecked: activeAccounts.length,
        accountResults,
        timestamp: Date.now(),
        lookbackHours,
      };

    } catch (error) {
      console.error('Error fetching user mentions:', error);
      return {
        success: false,
        newMentions: 0,
        message: `Failed to fetch mentions: ${error instanceof Error ? error.message : String(error)}`,
        accountsChecked: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

/**
 * OPTIMIZED: Refresh mentions for monitored accounts with real-time performance
 * Enhanced with intelligent caching, parallel processing, and smart batching
 */
export const refreshMentions = action({
  args: {
    forceRefresh: v.optional(v.boolean()), 
    lookbackHours: v.optional(v.number()),
    maxConcurrency: v.optional(v.number()), // Control parallel processing
    enableViralDetection: v.optional(v.boolean()), // Enable viral detection for new mentions
    priorityMode: v.optional(v.boolean()), // Process high-priority accounts first
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const metrics: MentionMetrics = {
      totalProcessingTime: 0,
      accountsProcessed: 0,
      mentionsFound: 0,
      mentionsStored: 0,
      duplicatesSkipped: 0,
      errors: 0,
      cacheHits: 0,
      apiCalls: 0,
    };

    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required");
      }

      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        throw new Error("User not found");
      }

      // Get user's active Twitter accounts with smart filtering
      const twitterAccounts = await ctx.runQuery(api.users.getUserTwitterAccounts);
      let activeAccounts: TwitterAccountFromDB[] = twitterAccounts?.filter((account: TwitterAccountFromDB) => 
        account.isActive && account.isMonitoringEnabled !== false
      ) || [];

      if (activeAccounts.length === 0) {
        return {
          success: true,
          newMentions: 0,
          message: "No active Twitter accounts to monitor. Please add and activate Twitter accounts first.",
          accountsChecked: 0,
          metrics
        };
      }

      // Priority sorting if enabled
      if (args.priorityMode) {
        activeAccounts = activeAccounts.sort((a, b) => {
          // Sort by follower count, verification, and recent activity
          const scoreA = (a.followersCount || 0) + (a.verified ? 10000 : 0);
          const scoreB = (b.followersCount || 0) + (b.verified ? 10000 : 0);
          return scoreB - scoreA;
        });
      }

      // Initialize TwitterAPI.io client with monitoring
      let twitterClient;
      try {
        const { createTweetIOClient } = await import("../lib/twitter_client");
        const { getTweetIOConfig } = await import("../lib/config");
        
        const config = getTweetIOConfig();
        twitterClient = createTweetIOClient(config.apiKey, ctx as any);
      } catch (clientError) {
        console.error('❌ Failed to initialize Twitter client:', clientError);
        throw new Error(`Failed to initialize Twitter client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
      }

      const lookbackHours = args.lookbackHours || 24;
      const maxConcurrency = args.maxConcurrency || 3; // Conservative default
      const enableViralDetection = args.enableViralDetection !== false;

      // Parallel processing with controlled concurrency
      const accountBatches = [];
      for (let i = 0; i < activeAccounts.length; i += maxConcurrency) {
        accountBatches.push(activeAccounts.slice(i, i + maxConcurrency));
      }

      let totalNewMentions = 0;
      const accountResults = [];

      // Process accounts in parallel batches
      for (const batch of accountBatches) {
        const batchPromises = batch.map((account) => 
          processAccountMentions(ctx, account, twitterClient, args, metrics, enableViralDetection)
        );

        const batchResults = await Promise.allSettled(batchPromises);
        
        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          const account = batch[i];
          
          if (result.status === 'fulfilled') {
            totalNewMentions += result.value.newMentions;
            accountResults.push(result.value);
            metrics.accountsProcessed++;
          } else {
            console.error(`❌ Account ${account.handle} failed:`, result.reason);
            metrics.errors++;
            accountResults.push({
              handle: account.handle,
              displayName: account.displayName,
              newMentions: 0,
              totalChecked: 0,
              error: result.reason instanceof Error ? result.reason.message : String(result.reason),
            });
          }
        }

        // Rate limiting between batches
        if (accountBatches.indexOf(batch) < accountBatches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      metrics.totalProcessingTime = Date.now() - startTime;
      metrics.mentionsStored = totalNewMentions;

      // Trigger post-processing for high-priority mentions
      if (enableViralDetection && totalNewMentions > 0) {
        // Fire and forget - don't wait for viral detection
        // TODO: Viral detection logic needs to be updated.
        // `batchViralDetection` is not a valid action, and `batchViralAnalysis` requires a different input structure.
        /*
        const mentionIdsToAnalyze = accountResults
          .filter((r): r is PromiseFulfilledResult<{ viralCandidates?: Id<"mentions">[] }> => r.status === 'fulfilled' && r.value.viralCandidates !== undefined)
          .flatMap(r => r.value.viralCandidates!)
          .slice(0, 20); // Limit to prevent overload

        if (mentionIdsToAnalyze.length > 0) {
          // This part needs to be reworked to fetch mention data and call batchViralAnalysis
          console.log("Viral detection candidates found:", mentionIdsToAnalyze);
        }
        */
      }

      const message = totalNewMentions === 0 
        ? "No new mentions found!" 
        : `Found ${totalNewMentions} new mentions across ${activeAccounts.length} monitored accounts.`;

      return {
        success: true,
        newMentions: totalNewMentions,
        message,
        accountsChecked: activeAccounts.length,
        accountResults,
        timestamp: Date.now(),
        lookbackHours,
        metrics,
        rateLimitInfo: twitterClient.getRateLimitStatus(),
        optimizations: {
          parallelProcessing: true,
          viralDetectionEnabled: enableViralDetection,
          priorityMode: args.priorityMode || false,
          maxConcurrency,
        }
      };

    } catch (error) {
      metrics.totalProcessingTime = Date.now() - startTime;
      metrics.errors++;
      
      console.error('❌ Error in optimized mention refresh:', error);
      return {
        success: false,
        newMentions: 0,
        message: `Failed to refresh mentions: ${error instanceof Error ? error.message : String(error)}`,
        accountsChecked: 0,
        metrics,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

/**
 * Process mentions for a single account with optimizations
 */
async function processAccountMentions(
  ctx: ProcessAccountMentionsContext,
  account: TwitterAccountFromDB,
  twitterClient: {
    searchMentions: (handle: string, options: any) => Promise<any>;
    getUserById?: (userId: string) => Promise<any>;
    getUsersByIds?: (userIds: string[]) => Promise<any>;
  },
  args: {
    forceRefresh?: boolean;
    lookbackHours?: number;
  },
  metrics: MentionMetrics,
  enableViralDetection: boolean
) {
  const accountStartTime = Date.now();
  
  try {
    // Smart cache check - get last mention with engagement tracking
    const lastMention = await ctx.runQuery(api.mentions.mentionQueries.getLatestMentionForAccount, {
      accountId: account._id
    });

    // Calculate intelligent search window
    const lookbackHours = args.lookbackHours || 24;
    const baseTime = Date.now() - (lookbackHours * 60 * 60 * 1000);
    
    let searchStartTime;
    if (args.forceRefresh) {
      searchStartTime = new Date(baseTime).toISOString();
    } else if (lastMention) {
      // Use last discovery time but add buffer for potential gaps
      const bufferTime = 30 * 60 * 1000; // 30 minute buffer
      searchStartTime = new Date(Math.max(lastMention.discoveredAt - bufferTime, baseTime)).toISOString();
      metrics.cacheHits++;
    } else {
      searchStartTime = new Date(baseTime).toISOString();
    }

    // Fetch mentions with optimized parameters
    metrics.apiCalls++;
    const mentionResults = await twitterClient.searchMentions(account.handle, {
      maxResults: 50,
      startTime: searchStartTime,
    });

    const { tweets: mentionTweets, users: mentionUsers }: { tweets: Tweet[], users: TwitterUserAPI[] } = mentionResults;
    
    metrics.mentionsFound += mentionTweets.length;

    let newMentionsForAccount = 0;
    const newMentionIds: Id<"mentions">[] = [];
    const viralCandidates: Id<"mentions">[] = [];

    // Process mentions with duplicate detection and priority scoring
    for (const tweet of mentionTweets) {
      try {
        // Optimized duplicate check
        const existingMention = await ctx.runQuery(api.mentions.mentionQueries.getMentionByTweetId, {
          tweetId: tweet.id
        });

        if (existingMention && !args.forceRefresh) {
          metrics.duplicatesSkipped++;
          continue;
        }

        // PHASE 1 FIX: Never skip mentions, always use fallback data
        let author = mentionUsers.find((u) => u.id === tweet.author_id);
        let authorUsername: string;
        let authorName: string;
        
        if (!author || !author.username) {
          console.warn(`⚠️ Missing author data for mention ${tweet.id} in refreshMentions, using fallback resolution`, { 
            authorId: tweet.author_id, 
            author: author ? 'found but no username' : 'not found',
            authorData: author 
          });
          
          // Generate fallback author data instead of skipping
          authorUsername = resolveUsername({
            user: author,
            tweet: tweet as any, // Cast to TwitterTweet for compatibility
            authorId: tweet.author_id,
            displayName: author?.name
          });
          
          authorName = author?.name || `Unknown User (${tweet.author_id.substring(0, 8)})`;
          
          // Create fallback author object if none exists
          if (!author) {
            author = {
              id: tweet.author_id,
              name: authorName,
              username: authorUsername,
              followers_count: 0,
              verified: false,
            } as TwitterUserAPI;
          } else {
            // Update existing author with resolved username
            author.username = authorUsername;
          }
        } else {
          authorUsername = author.username;
          authorName = author.name;
        }

        // Enhanced mention type detection
        let mentionType: "mention" | "reply" | "quote" | "retweet_with_comment" = "mention";
        if (tweet.in_reply_to_user_id) {
          mentionType = "reply";
        } else if (tweet.referenced_tweets?.some((ref) => ref.type === "quoted")) {
          mentionType = "quote";
        } else if (tweet.referenced_tweets?.some((ref) => ref.type === "retweeted")) {
          mentionType = "retweet_with_comment";
        }

        // Smart priority calculation with engagement velocity
        const { calculateMentionPriority } = await import("../lib/twitter_client");
        const priority = calculateMentionPriority(author);
        
        // Enhanced engagement metrics
        const engagement = {
          likes: tweet.public_metrics?.like_count || 0,
          retweets: tweet.public_metrics?.retweet_count || 0,
          replies: tweet.public_metrics?.reply_count || 0,
          views: tweet.public_metrics?.impression_count || 0,
        };

        // Calculate engagement velocity for viral detection
        const tweetAge = (Date.now() - new Date(tweet.created_at).getTime()) / (1000 * 60 * 60);
        const totalEngagement = engagement.likes + engagement.retweets + engagement.replies;
        const velocity = tweetAge > 0 ? totalEngagement / tweetAge : totalEngagement;

        // Prepare optimized mention data with sanitization
        const rawMentionData = {
          mentionTweetId: tweet.id,
          mentionContent: tweet.text,
          mentionAuthor: authorName,
          mentionAuthorHandle: authorUsername,
          mentionAuthorFollowers: author.followers_count || 0,
          mentionAuthorVerified: author.verified || false,
          monitoredAccountId: account._id,
          mentionType,
          originalTweetId: tweet.conversation_id && tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
          engagement,
          priority,
          createdAt: new Date(tweet.created_at).getTime(),
          url: `https://twitter.com/${authorUsername}/status/${tweet.id}`,
          forceUpdate: args.forceRefresh || false,
        };

        // PHASE 1 FIX: Sanitize and validate mention data before storage
        const { isValid, sanitized: mentionData, warnings } = sanitizeMentionData(rawMentionData);
        
        if (warnings.length > 0) {
          console.log(`📋 Mention ${tweet.id} data sanitized in refreshMentions:`, warnings);
        }
        
        if (!isValid) {
          console.error(`❌ Failed to sanitize mention ${tweet.id} in refreshMentions, skipping`, { rawMentionData });
          continue; // Only skip if data is fundamentally invalid
        }

        // Store with optimized checking
        const result = await ctx.runMutation(api.mentions.mentionMutations.storeMentionWithCheck, mentionData);
        
        if (result.action === 'created') {
          newMentionsForAccount++;
          newMentionIds.push(result.mentionId);
          
          // Flag for viral detection if high engagement velocity
          if (enableViralDetection && (velocity > 10 || priority === "high" || totalEngagement > 100)) {
            viralCandidates.push(result.mentionId);
          }
        }

      } catch (processingError) {
        console.error(`❌ Error processing mention ${tweet.id}:`, processingError);
        metrics.errors++;
      }
    }

    const processingTime = Date.now() - accountStartTime;

    return {
      handle: account.handle,
      displayName: account.displayName,
      newMentions: newMentionsForAccount,
      totalChecked: mentionTweets.length,
      processingTime,
      searchStartTime,
      newMentionIds,
      viralCandidates,
      metrics: {
        velocity: mentionTweets.length > 0 ? 
          mentionTweets.reduce((sum: number, t: Tweet) => {
            const age = (Date.now() - new Date(t.created_at).getTime()) / (1000 * 60 * 60);
            const eng = (t.public_metrics?.like_count || 0) + (t.public_metrics?.retweet_count || 0);
            return sum + (age > 0 ? eng / age : eng);
          }, 0) / mentionTweets.length : 0,
        avgEngagement: mentionTweets.length > 0 ?
          mentionTweets.reduce((sum: number, t: Tweet) => sum + ((t.public_metrics?.like_count || 0) + (t.public_metrics?.retweet_count || 0)), 0) / mentionTweets.length : 0,
      }
    };

  } catch (accountError) {
    console.error(`❌ Error processing account @${account.handle}:`, accountError);
    throw accountError;
  }
}

/**
 * PHASE 2: Enhanced mention processing with API lookup fallback
 * This function uses the new user lookup capabilities to resolve missing author data
 */
async function processAccountMentionsEnhanced(
  ctx: ProcessAccountMentionsContext,
  account: TwitterAccountFromDB,
  twitterClient: {
    searchMentions: (handle: string, options: any) => Promise<any>;
    getUserById?: (userId: string) => Promise<any>;
    getUsersByIds?: (userIds: string[]) => Promise<any>;
  },
  args: {
    forceRefresh?: boolean;
    lookbackHours?: number;
    enableApiLookup?: boolean;
  },
  metrics: MentionMetrics,
  enableViralDetection: boolean
) {
  const accountStartTime = Date.now();
  
  try {
    // Get last mention for intelligent search window
    const lastMention = await ctx.runQuery(api.mentions.mentionQueries.getLatestMentionForAccount, {
      accountId: account._id
    });

    const lookbackHours = args.lookbackHours || 24;
    const baseTime = Date.now() - (lookbackHours * 60 * 60 * 1000);
    
    let searchStartTime;
    if (args.forceRefresh) {
      searchStartTime = new Date(baseTime).toISOString();
    } else if (lastMention) {
      const bufferTime = 30 * 60 * 1000; // 30 minute buffer
      searchStartTime = new Date(Math.max(lastMention.discoveredAt - bufferTime, baseTime)).toISOString();
      metrics.cacheHits++;
    } else {
      searchStartTime = new Date(baseTime).toISOString();
    }

    // Fetch mentions
    metrics.apiCalls++;
    const mentionResults = await twitterClient.searchMentions(account.handle, {
      maxResults: 50,
      startTime: searchStartTime,
    });

    const { tweets: mentionTweets, users: mentionUsers }: { tweets: Tweet[], users: TwitterUserAPI[] } = mentionResults;
    metrics.mentionsFound += mentionTweets.length;

    // PHASE 2: Identify missing authors for batch resolution
    const missingAuthorIds: string[] = [];
    const authorMap = new Map<string, TwitterUserAPI>();
    
    // Build author map and identify missing ones
    mentionUsers.forEach(user => {
      if (user.id) {
        authorMap.set(user.id, user);
      }
    });
    
    mentionTweets.forEach(tweet => {
      const author = authorMap.get(tweet.author_id);
      if (!author || !author.username) {
        missingAuthorIds.push(tweet.author_id);
      }
    });

    // PHASE 2: Batch resolve missing authors using API lookup
    let resolvedAuthors = new Map<string, TwitterUserAPI>();
    if (args.enableApiLookup !== false && missingAuthorIds.length > 0) {
      console.log(`🔍 PHASE 2: Attempting to resolve ${missingAuthorIds.length} missing authors for @${account.handle}`);
      
      try {
        const resolved = await batchResolveAuthors({
          missingAuthorIds,
          twitterClient,
          maxBatchSize: 5 // Conservative batch size
        });
        
        resolved.forEach((user, userId) => {
          resolvedAuthors.set(userId, user);
          authorMap.set(userId, user); // Add to main author map
        });
        
        metrics.apiCalls += Math.ceil(missingAuthorIds.length / 5); // Track additional API calls
      } catch (batchError) {
        console.warn(`⚠️ Batch author resolution failed for @${account.handle}:`, batchError);
      }
    }

    let newMentionsForAccount = 0;
    const newMentionIds: Id<"mentions">[] = [];
    const viralCandidates: Id<"mentions">[] = [];

    // Process mentions with enhanced author resolution
    for (const tweet of mentionTweets) {
      try {
        // Check for duplicates
        const existingMention = await ctx.runQuery(api.mentions.mentionQueries.getMentionByTweetId, {
          tweetId: tweet.id
        });

        if (existingMention && !args.forceRefresh) {
          metrics.duplicatesSkipped++;
          continue;
        }

        // PHASE 2: Enhanced author resolution with API lookup
        const existingAuthor = authorMap.get(tweet.author_id);
        const { author, username: authorUsername, name: authorName, wasResolved } = await resolveAuthorWithApiLookup({
          authorId: tweet.author_id,
          existingAuthor,
          tweet: tweet as any,
          twitterClient: args.enableApiLookup !== false ? twitterClient : undefined
        });

        if (wasResolved) {
          console.log(`✅ PHASE 2: Successfully resolved author @${authorUsername} for mention ${tweet.id}`);
          metrics.apiCalls++; // Track individual resolution calls
        }

        // Enhanced mention type detection
        let mentionType: "mention" | "reply" | "quote" | "retweet_with_comment" = "mention";
        if (tweet.in_reply_to_user_id) {
          mentionType = "reply";
        } else if (tweet.referenced_tweets?.some((ref) => ref.type === "quoted")) {
          mentionType = "quote";
        } else if (tweet.referenced_tweets?.some((ref) => ref.type === "retweeted")) {
          mentionType = "retweet_with_comment";
        }

        // Calculate priority with enhanced metrics
        const { calculateMentionPriority } = await import("../lib/twitter_client");
        const priority = calculateMentionPriority(author);
        
        // Enhanced engagement metrics
        const engagement = {
          likes: tweet.public_metrics?.like_count || 0,
          retweets: tweet.public_metrics?.retweet_count || 0,
          replies: tweet.public_metrics?.reply_count || 0,
          views: tweet.public_metrics?.impression_count || 0,
        };

        // Calculate engagement velocity for viral detection
        const tweetAge = (Date.now() - new Date(tweet.created_at).getTime()) / (1000 * 60 * 60);
        const totalEngagement = engagement.likes + engagement.retweets + engagement.replies;
        const velocity = tweetAge > 0 ? totalEngagement / tweetAge : totalEngagement;

        // Prepare mention data with enhanced author information
        const rawMentionData = {
          mentionTweetId: tweet.id,
          mentionContent: tweet.text,
          mentionAuthor: authorName,
          mentionAuthorHandle: authorUsername,
          mentionAuthorFollowers: author.followers_count || 0,
          mentionAuthorVerified: author.verified || false,
          monitoredAccountId: account._id,
          mentionType,
          originalTweetId: tweet.conversation_id && tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
          engagement,
          priority,
          createdAt: new Date(tweet.created_at).getTime(),
          url: `https://twitter.com/${authorUsername}/status/${tweet.id}`,
          forceUpdate: args.forceRefresh || false,
        };

        // Sanitize and validate data
        const { isValid, sanitized: mentionData, warnings } = sanitizeMentionData(rawMentionData);
        
        if (warnings.length > 0) {
          console.log(`📋 Mention ${tweet.id} data sanitized (enhanced):`, warnings);
        }
        
        if (!isValid) {
          console.error(`❌ Failed to sanitize mention ${tweet.id} (enhanced), skipping`, { rawMentionData });
          continue;
        }

        // Store mention with enhanced tracking
        const result = await ctx.runMutation(api.mentions.mentionMutations.storeMentionWithCheck, mentionData);
        
        if (result.action === 'created') {
          newMentionsForAccount++;
          newMentionIds.push(result.mentionId);
          
          // Enhanced viral detection
          if (enableViralDetection && (velocity > 10 || priority === "high" || totalEngagement > 100)) {
            viralCandidates.push(result.mentionId);
          }
        }

      } catch (processingError) {
        console.error(`❌ Error processing mention ${tweet.id} (enhanced):`, processingError);
        metrics.errors++;
      }
    }

    const processingTime = Date.now() - accountStartTime;

    return {
      handle: account.handle,
      displayName: account.displayName,
      newMentions: newMentionsForAccount,
      totalChecked: mentionTweets.length,
      processingTime,
      searchStartTime,
      newMentionIds,
      viralCandidates,
      phase2Stats: {
        missingAuthorsFound: missingAuthorIds.length,
        authorsResolved: resolvedAuthors.size,
        apiLookupsPerformed: resolvedAuthors.size,
      },
      metrics: {
        velocity: mentionTweets.length > 0 ? 
          mentionTweets.reduce((sum: number, t: Tweet) => {
            const age = (Date.now() - new Date(t.created_at).getTime()) / (1000 * 60 * 60);
            const eng = (t.public_metrics?.like_count || 0) + (t.public_metrics?.retweet_count || 0);
            return sum + (age > 0 ? eng / age : eng);
          }, 0) / mentionTweets.length : 0,
        avgEngagement: mentionTweets.length > 0 ?
          mentionTweets.reduce((sum: number, t: Tweet) => sum + ((t.public_metrics?.like_count || 0) + (t.public_metrics?.retweet_count || 0)), 0) / mentionTweets.length : 0,
      }
    };

  } catch (accountError) {
    console.error(`❌ Error processing account @${account.handle} (enhanced):`, accountError);
    throw accountError;
  }
}

export const updateMentionPriority = mutation({
  args: {
    mentionId: v.id("mentions"),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      priority: args.priority,
    });

    return { success: true };
  },
});

export const updateMonitoringSettings = mutation({
  args: {
    twitterAccountId: v.id("twitterAccounts"),
    isMonitoringEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { twitterAccountId, ...updates } = args;

    await ctx.db.patch(twitterAccountId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const bulkUpdateMentionNotifications = mutation({
  args: {
    mentionIds: v.array(v.id("mentions")),
    isNotificationSent: v.boolean(),
  },
  handler: async (ctx, args) => {
    const timestamp = Date.now();

    for (const mentionId of args.mentionIds) {
      await ctx.db.patch(mentionId, {
        isNotificationSent: args.isNotificationSent,
        notificationSentAt: args.isNotificationSent ? timestamp : undefined,
      });
    }

    return { 
      success: true, 
      updated: args.mentionIds.length 
    };
  },
});

export const markAllMentionsAsRead = mutation({
  args: {
    monitoredAccountId: v.optional(v.id("twitterAccounts")),
  },
  handler: async (ctx, args) => {
    const timestamp = Date.now();

    // Get all unread mentions
    let query = ctx.db.query("mentions")
      .withIndex("by_notification", (q) => q.eq("isNotificationSent", false));

    let unreadMentions = await query.collect();

    // Filter by monitored account if specified
    if (args.monitoredAccountId) {
      unreadMentions = unreadMentions.filter(m => m.monitoredAccountId === args.monitoredAccountId);
    }

    // Mark all as read
    const updatePromises = unreadMentions.map(mention =>
      ctx.db.patch(mention._id, {
        isNotificationSent: true,
        notificationSentAt: timestamp,
      })
    );

    await Promise.all(updatePromises);

    return { 
      success: true, 
      updated: unreadMentions.length 
    };
  },
});

export const deleteMention = mutation({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.mentionId);
    return { success: true };
  },
});

export const storeMention = mutation({
  args: {
    mentionTweetId: v.string(),
    mentionContent: v.string(),
    mentionAuthor: v.string(),
    mentionAuthorHandle: v.string(),
    mentionAuthorFollowers: v.optional(v.number()),
    mentionAuthorVerified: v.optional(v.boolean()),
    monitoredAccountId: v.id("twitterAccounts"),
    mentionType: v.union(
      v.literal("mention"),
      v.literal("reply"),
      v.literal("quote"),
      v.literal("retweet_with_comment")
    ),
    originalTweetId: v.optional(v.string()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
    createdAt: v.number(),
    url: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existing = await ctx.db
      .query("mentions")
      .filter(q => q.eq(q.field("mentionTweetId"), args.mentionTweetId))
      .first();

    if (existing) {
      // Update engagement metrics
      await ctx.db.patch(existing._id, {
        engagement: args.engagement,
        discoveredAt: Date.now(), // Update discovery time
      });
      return existing._id;
    }

    // Create new mention
    const mentionId = await ctx.db.insert("mentions", {
      ...args,
      isProcessed: false,
      isNotificationSent: false,
      discoveredAt: Date.now(),
    });

    return mentionId;
  },
});

export const storeMentionWithCheck = mutation({
  args: {
    mentionTweetId: v.string(),
    mentionContent: v.string(),
    mentionAuthor: v.string(),
    mentionAuthorHandle: v.string(),
    mentionAuthorFollowers: v.optional(v.number()),
    mentionAuthorVerified: v.optional(v.boolean()),
    monitoredAccountId: v.id("twitterAccounts"),
    mentionType: v.union(
      v.literal("mention"),
      v.literal("reply"),
      v.literal("quote"),
      v.literal("retweet_with_comment")
    ),
    originalTweetId: v.optional(v.string()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
    createdAt: v.number(),
    url: v.optional(v.string()),
    forceUpdate: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existing = await ctx.db
      .query("mentions")
      .filter(q => q.eq(q.field("mentionTweetId"), args.mentionTweetId))
      .first();

    if (existing) {
      // If force update is enabled or engagement has changed significantly
      const engagementChanged = args.forceUpdate || 
        existing.engagement.likes !== args.engagement.likes ||
        existing.engagement.retweets !== args.engagement.retweets ||
        existing.engagement.replies !== args.engagement.replies;

      if (engagementChanged) {
        // Update engagement metrics and refresh discovery time
        await ctx.db.patch(existing._id, {
          engagement: args.engagement,
          discoveredAt: Date.now(),
          priority: args.priority, // Update priority as it may have changed
        });
        
        return { 
          mentionId: existing._id, 
          action: 'updated',
          changes: {
            engagement: true,
            priority: existing.priority !== args.priority,
          }
        };
      } else {
        // No significant changes, return existing
        return { 
          mentionId: existing._id, 
          action: 'exists',
          changes: {}
        };
      }
    }

    // Create new mention with comprehensive duplicate check
    const mentionId = await ctx.db.insert("mentions", {
      mentionTweetId: args.mentionTweetId,
      mentionContent: args.mentionContent,
      mentionAuthor: args.mentionAuthor,
      mentionAuthorHandle: args.mentionAuthorHandle,
      mentionAuthorFollowers: args.mentionAuthorFollowers,
      mentionAuthorVerified: args.mentionAuthorVerified,
      monitoredAccountId: args.monitoredAccountId,
      mentionType: args.mentionType,
      originalTweetId: args.originalTweetId,
      engagement: args.engagement,
      priority: args.priority,
      createdAt: args.createdAt,
      url: args.url,
      isProcessed: false,
      isNotificationSent: false,
      discoveredAt: Date.now(),
    });

    // Schedule sentiment analysis for the new mention
    ctx.scheduler.runAfter(0, api.mentions.mentionMutations.processMentionSentiment, {
      mentionId,
    });

    return { 
      mentionId, 
      action: 'created',
      changes: {
        created: true,
      }
    };
  },
});

export const updateMentionEngagement = mutation({
  args: {
    mentionId: v.id("mentions"),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      engagement: args.engagement,
      discoveredAt: Date.now(), // Update to mark as refreshed
    });

    return { success: true };
  },
});

export const processMentionWithAI = action({
  args: {
    mentionId: v.id("mentions"),
    skipIfProcessed: v.optional(v.boolean()),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    // Get the mention
    const mention = await ctx.runQuery(api.userQueries.getMentionById, {
      mentionId: args.mentionId,
    });
    if (!mention) {
      return { success: false, error: "Mention not found" };
    }

    if (args.skipIfProcessed && mention.isProcessed) {
      return { success: true, message: "Already processed, skipped" };
    }

    try {
      const client = getAIFallbackClient();

      // Analysis prompt for mentions
      const analysisPrompt = `
Analyze this mention for response appropriateness and strategy:

Mention: "${mention.mentionContent}"
Author: @${mention.mentionAuthorHandle} (${mention.mentionAuthor})
Type: ${mention.mentionType}
${mention.mentionAuthorVerified ? 'Verified account' : 'Unverified account'}
Followers: ${mention.mentionAuthorFollowers || 0}
Engagement: ${mention.engagement.likes} likes, ${mention.engagement.retweets} retweets

${args.userContext?.brand ? `Your brand: ${args.userContext.brand}` : ''}
${args.userContext?.expertise ? `Your expertise: ${args.userContext.expertise.join(', ')}` : ''}

Provide analysis in this exact JSON format:
{
  "shouldRespond": boolean,
  "responseStrategy": "engage" | "educate" | "support" | "promote" | "deflect",
  "sentiment": "positive" | "negative" | "neutral" | "mixed",
  "topics": ["topic1", "topic2"],
  "confidence": number (0-1),
  "urgency": "immediate" | "soon" | "low",
  "reasoning": "explanation for decision",
  "riskLevel": "low" | "medium" | "high",
  "suggestedTone": "professional" | "casual" | "supportive" | "humorous"
}`;

      const response = await client.generateResponse({
        prompt: analysisPrompt,
        systemPrompt: 'You are an expert social media manager who analyzes mentions to determine the best response strategy.',
        maxTokens: 400,
        temperature: 0.3,
      });

      // Parse JSON response
      let analysis;
      try {
        analysis = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse mention analysis JSON:', response.content);
        // Fallback analysis
        analysis = {
          shouldRespond: true,
          responseStrategy: 'engage',
          sentiment: 'neutral',
          topics: ['general'],
          confidence: 0.5,
          urgency: 'low',
          reasoning: 'Fallback analysis due to parsing failure',
          riskLevel: 'low',
          suggestedTone: 'professional',
        };
      }

      const aiAnalysisResult = {
        shouldRespond: analysis.shouldRespond,
        responseStrategy: analysis.responseStrategy,
        sentiment: analysis.sentiment,
        topics: analysis.topics,
        confidence: analysis.confidence,
      };

      // Update the mention
      await ctx.runMutation(api.mentions.mentionMutations.updateMentionProcessingStatus, {
        mentionId: args.mentionId,
        isProcessed: true,
        aiAnalysisResult,
      });

      return {
        success: true,
        analysis: {
          ...aiAnalysisResult,
          urgency: analysis.urgency,
          reasoning: analysis.reasoning,
          riskLevel: analysis.riskLevel,
          suggestedTone: analysis.suggestedTone,
        },
        model: response.model,
      };
    } catch (error) {
      console.error("Error processing mention with AI:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

export const bulkProcessMentions = action({
  args: {
    mentionIds: v.optional(v.array(v.id("mentions"))),
    unprocessedOnly: v.optional(v.boolean()),
    maxToProcess: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      let mentionsToProcess: Array<{ _id: string; isProcessed?: boolean }> = [];

      if (args.mentionIds) {
        // Process specific mentions
        for (const mentionId of args.mentionIds) {
          const mention = await ctx.runQuery(api.userQueries.getMentionById, {
            mentionId,
          });
          if (mention) {
            mentionsToProcess.push(mention);
          }
        }
      } else {
        // Get unprocessed mentions
        const mentions = await ctx.runQuery(api.userQueries.getUnprocessedMentions, {
          limit: args.maxToProcess || 50,
        });
        
        mentionsToProcess = mentions;
      }

      if (args.unprocessedOnly) {
        mentionsToProcess = mentionsToProcess.filter(m => !m.isProcessed);
      }

      const results = [];

      // Process each mention
      for (const mention of mentionsToProcess) {
        try {
          const result = await ctx.runAction(api.mentions.mentionMutations.processMentionWithAI, {
            mentionId: mention._id,
            skipIfProcessed: args.unprocessedOnly,
          });

          results.push({
            mentionId: mention._id,
            ...result,
          });

          // Add a small delay to avoid overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error(`Error processing mention ${mention._id}:`, error);
          results.push({
            mentionId: mention._id,
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      return {
        success: true,
        totalProcessed: results.length,
        successful,
        failed,
        results,
      };
    } catch (error) {
      console.error("Error in bulk mention processing:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        totalProcessed: 0,
        successful: 0,
        failed: 0,
        results: [],
      };
    }
  },
});

/**
 * Generate AI response to a mention
 */
export const generateMentionResponse = action({
  args: {
    mentionId: v.id("mentions"),
    responseStyle: v.optional(v.union(
      v.literal("professional"),
      v.literal("casual"),
      v.literal("humorous"),
      v.literal("technical"),
      v.literal("supportive")
    )),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      writingStyle: v.optional(v.string()),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    try {
      // Get mention details
      const mention = await ctx.runQuery(api.userQueries.getMentionById, {
        mentionId: args.mentionId,
      });
      if (!mention) {
        throw new Error("Mention not found");
      }

      const client = getAIFallbackClient();

      // Build context for response generation
      const promptContext = buildPromptContext(mention.mentionContent, {
        authorInfo: {
          handle: mention.mentionAuthorHandle,
          displayName: mention.mentionAuthor,
          isVerified: mention.mentionAuthorVerified,
          followerCount: mention.mentionAuthorFollowers,
        },
        tweetMetadata: {
          engagement: mention.engagement,
          createdAt: mention.createdAt,
        },
        userContext: args.userContext,
        responseStrategy: mention.aiAnalysisResult?.responseStrategy || 'engage',
      });

      const template = getResponseTemplate(
        'mention',
        args.responseStyle || 'casual'
      );

      const response = await client.generateResponse({
        prompt: template.userPrompt(promptContext),
        systemPrompt: template.systemPrompt,
        maxTokens: template.maxTokens,
        temperature: template.temperature,
      });

      // Clean up response
      const cleanContent = response.content
        .replace(/^(Response:|Reply:)\s*/i, '')
        .replace(/^["']|["']$/g, '')
        .trim();

      return {
        mentionId: args.mentionId,
        response: {
          content: cleanContent,
          style: args.responseStyle || 'casual',
          characterCount: cleanContent.length,
          model: response.model,
          provider: response.provider,
          isFallback: response.isFallback,
          confidence: response.confidence,
          strategy: mention.aiAnalysisResult?.responseStrategy || 'engage',
        },
        generatedAt: Date.now(),
      };
    } catch (error) {
      console.error('Mention response generation failed:', error);
      throw new Error(`Mention response generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Generate multiple response options for a mention
 */
export const generateMentionResponseOptions = action({
  args: {
    mentionId: v.id("mentions"),
    responseStyles: v.optional(v.array(v.string())),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      writingStyle: v.optional(v.string()),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    try {
      // Get mention details
      const mention = await ctx.runQuery(api.userQueries.getMentionById, {
        mentionId: args.mentionId,
      });
      if (!mention) {
        throw new Error("Mention not found");
      }

      const styles = args.responseStyles || ['professional', 'casual', 'supportive'];
      const responses = [];

      // Generate response for each style
      for (const style of styles) {
        try {
          const responseResult = await ctx.runAction(api.mentions.mentionMutations.generateMentionResponse, {
            mentionId: args.mentionId,
            responseStyle: style,
            userContext: args.userContext,
          });

          responses.push(responseResult.response);
        } catch (styleError) {
          console.error(`Failed to generate ${style} mention response:`, styleError);
          // Continue with other styles
        }
      }

      return {
        mentionId: args.mentionId,
        responses,
        generatedAt: Date.now(),
      };
    } catch (error) {
      console.error('Mention response options generation failed:', error);
      throw new Error(`Mention response options generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Automated mention monitoring action (for scheduled execution)
 * This can be called by cron jobs or scheduled tasks to continuously monitor mentions
 * Enhanced with intelligent scheduling and priority processing
 */
export const automatedMentionMonitoring = action({
  args: {
    batchSize: v.optional(v.number()), // How many accounts to process in one batch
    priorityOnly: v.optional(v.boolean()), // Process only high-priority accounts
    maxRuntimeMs: v.optional(v.number()), // Maximum runtime to prevent timeout
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const maxRuntime = args.maxRuntimeMs || 4 * 60 * 1000; // 4 minutes default

    try {
      // Get all active Twitter accounts from all users
      const allActiveAccounts: TwitterAccountFromDB[] = await ctx.runQuery(api.userQueries.getActiveTwitterAccounts);
      let accountsToMonitor = allActiveAccounts.filter(account => 
        account.isMonitoringEnabled !== false
      );

      // Apply priority filtering if requested
      if (args.priorityOnly) {
        accountsToMonitor = accountsToMonitor.filter(account => 
          account.verified || (account.followersCount || 0) > 1000
        );
      }

      // Smart sorting: prioritize accounts that haven't been checked recently
      const accountsWithLastChecked = await Promise.all(
        accountsToMonitor.map(async (account) => {
          const lastMention = await ctx.runQuery(api.mentions.mentionQueries.getLatestMentionForAccount, {
            accountId: account._id
          });
          
          return {
            ...account,
            lastChecked: lastMention?.discoveredAt || 0,
            priority: calculateAccountPriority(account, lastMention)
          };
        })
      );

      accountsWithLastChecked.sort((a, b) => {
        const priorityDiff = (b.priority || 0) - (a.priority || 0);
        if (priorityDiff !== 0) return priorityDiff;
        return (a.lastChecked || 0) - (b.lastChecked || 0); // Older checks first
      });

      if (accountsWithLastChecked.length === 0) {
        return {
          success: true,
          newMentions: 0,
          message: "No accounts configured for mention monitoring",
          accountsProcessed: 0
        };
      }

      // Initialize TwitterAPI.io client
      let twitterClient;
      try {
        const { createTweetIOClient } = await import("../lib/twitter_client");
        const { getTweetIOConfig } = await import("../lib/config");
        
        const config = getTweetIOConfig();
        twitterClient = createTweetIOClient(config.apiKey);
      } catch (clientError) {
        const errorMessage = clientError instanceof Error ? clientError.message : String(clientError);
        console.error('❌ Failed to initialize Twitter client for automated monitoring:', clientError);
        return {
          success: false,
          newMentions: 0,
          message: `Failed to initialize Twitter client: ${errorMessage}`,
          accountsProcessed: 0,
          error: errorMessage
        };
      }

      let totalNewMentions = 0;
      const batchSize = args.batchSize || 5; // Process 5 accounts at a time to respect rate limits
      const accountResults = [];

      // Process accounts in batches to manage rate limits and time constraints
      for (let i = 0; i < accountsWithLastChecked.length; i += batchSize) {
        // Check if we're running out of time
        if (Date.now() - startTime > maxRuntime * 0.9) {
          break; // Stop processing to avoid timeout
        }

        const batch = accountsWithLastChecked.slice(i, i + batchSize);

        for (const account of batch) {
          try {
            // Get the most recent mention to avoid duplicates
            const lastMention = await ctx.runQuery(api.mentions.mentionQueries.getLatestMentionForAccount, {
              accountId: account._id
            });

            // Look back 4 hours for automated monitoring (more frequent checks)
            const lookbackTime = Date.now() - (4 * 60 * 60 * 1000);
            const searchStartTime = lastMention ? 
              new Date(Math.max(lastMention.discoveredAt, lookbackTime)).toISOString() :
              new Date(lookbackTime).toISOString();

            // Search for mentions
            const mentionResults = await twitterClient.searchMentions(account.handle, {
              maxResults: 30, // Smaller batch for automated monitoring
              startTime: searchStartTime,
            });

            const { tweets: mentionTweets, users: mentionUsers }: { tweets: Tweet[], users: TwitterUserAPI[] } = mentionResults;

            let newMentionsForAccount = 0;

            for (const tweet of mentionTweets) {
              try {
                // Skip if we already have this mention
                const existingMention = await ctx.runQuery(api.mentions.mentionQueries.getMentionByTweetId, {
                  tweetId: tweet.id
                });

                if (existingMention) {
                  continue;
                }

                // PHASE 1 FIX: Never skip mentions, always use fallback data
                let author = mentionUsers.find((u) => u.id === tweet.author_id);
                let authorUsername: string;
                let authorName: string;
                
                if (!author || !author.username) {
                  console.warn(`⚠️ Missing author data for mention ${tweet.id} in automatedMentionMonitoring, using fallback resolution`, { 
                    authorId: tweet.author_id, 
                    author: author ? 'found but no username' : 'not found',
                    authorData: author 
                  });
                  
                  // Generate fallback author data instead of skipping
                  authorUsername = resolveUsername({
                    user: author,
                    tweet: tweet as any, // Cast to TwitterTweet for compatibility
                    authorId: tweet.author_id,
                    displayName: author?.name
                  });
                  
                  authorName = author?.name || `Unknown User (${tweet.author_id.substring(0, 8)})`;
                  
                  // Create fallback author object if none exists
                  if (!author) {
                    author = {
                      id: tweet.author_id,
                      name: authorName,
                      username: authorUsername,
                      followers_count: 0,
                      verified: false,
                    } as TwitterUserAPI;
                  } else {
                    // Update existing author with resolved username
                    author.username = authorUsername;
                  }
                } else {
                  authorUsername = author.username;
                  authorName = author.name;
                }

                // Determine mention type
                let mentionType: "mention" | "reply" | "quote" | "retweet_with_comment" = "mention";
                if (tweet.in_reply_to_user_id) {
                  mentionType = "reply";
                } else if (tweet.referenced_tweets?.some((ref) => ref.type === "quoted")) {
                  mentionType = "quote";
                } else if (tweet.referenced_tweets?.some((ref) => ref.type === "retweeted")) {
                  mentionType = "retweet_with_comment";
                }

                // Calculate priority based on author metrics
                const { calculateMentionPriority } = await import("../lib/twitter_client");
                const priority = calculateMentionPriority(author);

                // Prepare mention data with sanitization
                const rawMentionData = {
                  mentionTweetId: tweet.id,
                  mentionContent: tweet.text,
                  mentionAuthor: authorName,
                  mentionAuthorHandle: authorUsername,
                  mentionAuthorFollowers: author.followers_count || 0,
                  mentionAuthorVerified: author.verified || false,
                  monitoredAccountId: account._id,
                  mentionType,
                  originalTweetId: tweet.conversation_id && tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
                  engagement: {
                    likes: tweet.public_metrics?.like_count || 0,
                    retweets: tweet.public_metrics?.retweet_count || 0,
                    replies: tweet.public_metrics?.reply_count || 0,
                    views: tweet.public_metrics?.impression_count || 0,
                  },
                  priority,
                  createdAt: new Date(tweet.created_at).getTime(),
                  url: `https://twitter.com/${authorUsername}/status/${tweet.id}`,
                };

                // PHASE 1 FIX: Sanitize and validate mention data before storage
                const { isValid, sanitized: mentionData, warnings } = sanitizeMentionData(rawMentionData);
                
                if (warnings.length > 0) {
                  console.log(`📋 Mention ${tweet.id} data sanitized in automatedMentionMonitoring:`, warnings);
                }
                
                if (!isValid) {
                  console.error(`❌ Failed to sanitize mention ${tweet.id} in automatedMentionMonitoring, skipping`, { rawMentionData });
                  continue; // Only skip if data is fundamentally invalid
                }

                // Store the mention
                const result = await ctx.runMutation(api.mentions.mentionMutations.storeMentionWithCheck, mentionData);
                
                if (result.action === 'created') {
                  newMentionsForAccount++;
                  totalNewMentions++;
                }

                // Small delay to avoid overwhelming the system
                await new Promise(resolve => setTimeout(resolve, 50));

              } catch (processingError) {
                console.error(`❌ Error auto-processing mention ${tweet.id}:`, processingError);
                // Continue with other mentions
              }
            }

            accountResults.push({
              handle: account.handle,
              displayName: account.displayName,
              newMentions: newMentionsForAccount,
              totalChecked: mentionTweets.length,
              searchStartTime,
            });

            // Rate limiting: wait between accounts within batch
            if (batch.indexOf(account) < batch.length - 1) {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }

          } catch (accountError) {
            console.error(`❌ Error auto-monitoring @${account.handle}:`, accountError);
            
            accountResults.push({
              handle: account.handle,
              displayName: account.displayName,
              newMentions: 0,
              totalChecked: 0,
              error: accountError instanceof Error ? accountError.message : String(accountError),
            });
          }
        }

        // Wait between batches to respect rate limits
        if (i + batchSize < accountsWithLastChecked.length) {
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }

      const message = totalNewMentions === 0 
        ? "Automated monitoring complete - no new mentions found" 
        : `Automated monitoring found ${totalNewMentions} new mentions across ${accountsWithLastChecked.length} monitored accounts`;

      return {
        success: true,
        newMentions: totalNewMentions,
        message,
        accountsProcessed: accountsWithLastChecked.length,
        accountResults,
        timestamp: Date.now(),
        automated: true,
        rateLimitInfo: twitterClient.getRateLimitStatus(),
      };

    } catch (error) {
      console.error('❌ Error in automated mention monitoring:', error);
      return {
        success: false,
        newMentions: 0,
        message: `Automated mention monitoring failed: ${error instanceof Error ? error.message : String(error)}`,
        accountsProcessed: 0,
        error: error instanceof Error ? error.message : String(error),
        automated: true,
      };
    }
  },
});

/**
 * Calculate account priority for monitoring
 * Higher numbers = higher priority
 */
function calculateAccountPriority(
  account: { verified?: boolean; followersCount?: number }, 
  lastMention?: { discoveredAt: number } | null
): number {
  let priority = 1;
  
  // Verified accounts get higher priority
  if (account.verified) {
    priority += 5;
  }
  
  // Higher follower count = higher priority
  const followers = account.followersCount || 0;
  if (followers > 100000) priority += 4;
  else if (followers > 10000) priority += 3;
  else if (followers > 1000) priority += 2;
  else if (followers > 100) priority += 1;
  
  // Accounts with recent mentions get slight boost (active accounts)
  if (lastMention) {
    const hoursSinceLastMention = (Date.now() - lastMention.discoveredAt) / (1000 * 60 * 60);
    if (hoursSinceLastMention < 24) priority += 1;
  }
  
  return priority;
}

/**
 * PHASE 2: Enhanced mention refresh with API lookup capabilities
 * Uses the new user lookup functionality to resolve missing author data
 */
export const refreshMentionsEnhanced = action({
  args: {
    forceRefresh: v.optional(v.boolean()), 
    lookbackHours: v.optional(v.number()),
    maxConcurrency: v.optional(v.number()),
    enableViralDetection: v.optional(v.boolean()),
    priorityMode: v.optional(v.boolean()),
    enableApiLookup: v.optional(v.boolean()), // PHASE 2: Enable API lookups
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const metrics: MentionMetrics = {
      totalProcessingTime: 0,
      accountsProcessed: 0,
      mentionsFound: 0,
      mentionsStored: 0,
      duplicatesSkipped: 0,
      errors: 0,
      cacheHits: 0,
      apiCalls: 0,
    };

    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required");
      }

      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        throw new Error("User not found");
      }

      // Get user's active Twitter accounts
      const twitterAccounts = await ctx.runQuery(api.users.getUserTwitterAccounts);
      let activeAccounts: TwitterAccountFromDB[] = twitterAccounts?.filter((account: TwitterAccountFromDB) => 
        account.isActive && account.isMonitoringEnabled !== false
      ) || [];

      if (activeAccounts.length === 0) {
        return {
          success: true,
          newMentions: 0,
          message: "No active Twitter accounts to monitor.",
          accountsChecked: 0,
          metrics
        };
      }

      // Priority sorting if enabled
      if (args.priorityMode) {
        activeAccounts = activeAccounts.sort((a, b) => {
          const scoreA = (a.followersCount || 0) + (a.verified ? 10000 : 0);
          const scoreB = (b.followersCount || 0) + (b.verified ? 10000 : 0);
          return scoreB - scoreA;
        });
      }

      // Initialize TwitterAPI.io client
      let twitterClient;
      try {
        const { createTweetIOClient } = await import("../lib/twitter_client");
        const { getTweetIOConfig } = await import("../lib/config");
        
        const config = getTweetIOConfig();
        twitterClient = createTweetIOClient(config.apiKey, ctx as any);
      } catch (clientError) {
        console.error('❌ Failed to initialize Twitter client:', clientError);
        throw new Error(`Failed to initialize Twitter client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
      }

      const maxConcurrency = args.maxConcurrency || 3;
      const enableViralDetection = args.enableViralDetection !== false;
      const enableApiLookup = args.enableApiLookup !== false; // PHASE 2: Default to enabled

      // Process accounts in parallel batches
      const accountBatches = [];
      for (let i = 0; i < activeAccounts.length; i += maxConcurrency) {
        accountBatches.push(activeAccounts.slice(i, i + maxConcurrency));
      }

      let totalNewMentions = 0;
      const accountResults = [];
      let totalPhase2Resolutions = 0;

      for (const batch of accountBatches) {
        const batchPromises = batch.map((account) => 
          processAccountMentionsEnhanced(ctx, account, twitterClient, {
            ...args,
            enableApiLookup
          }, metrics, enableViralDetection)
        );

        const batchResults = await Promise.allSettled(batchPromises);
        
        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          const account = batch[i];
          
          if (result.status === 'fulfilled') {
            totalNewMentions += result.value.newMentions;
            totalPhase2Resolutions += result.value.phase2Stats?.authorsResolved || 0;
            accountResults.push({
              ...result.value,
              phase2Enabled: enableApiLookup
            });
            metrics.accountsProcessed++;
          } else {
            console.error(`❌ Account ${account.handle} failed:`, result.reason);
            metrics.errors++;
            accountResults.push({
              handle: account.handle,
              displayName: account.displayName,
              newMentions: 0,
              totalChecked: 0,
              error: result.reason instanceof Error ? result.reason.message : String(result.reason),
              phase2Enabled: enableApiLookup,
              phase2Stats: { missingAuthorsFound: 0, authorsResolved: 0, apiLookupsPerformed: 0 }
            });
          }
        }

        // Rate limiting between batches
        if (accountBatches.indexOf(batch) < accountBatches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      metrics.totalProcessingTime = Date.now() - startTime;
      metrics.mentionsStored = totalNewMentions;

      const message = totalNewMentions === 0 
        ? "No new mentions found!" 
        : `Found ${totalNewMentions} new mentions. ${enableApiLookup ? `PHASE 2: Resolved ${totalPhase2Resolutions} missing authors via API lookup.` : ''}`;

      return {
        success: true,
        newMentions: totalNewMentions,
        message,
        accountsChecked: activeAccounts.length,
        accountResults,
        timestamp: Date.now(),
        lookbackHours: args.lookbackHours || 24,
        metrics,
        rateLimitInfo: twitterClient.getRateLimitStatus(),
        phase2Stats: {
          apiLookupEnabled: enableApiLookup,
          totalAuthorsResolved: totalPhase2Resolutions,
          enhancedProcessing: true
        },
        optimizations: {
          parallelProcessing: true,
          viralDetectionEnabled: enableViralDetection,
          priorityMode: args.priorityMode || false,
          maxConcurrency,
          phase2ApiLookup: enableApiLookup
        }
      };

    } catch (error) {
      metrics.totalProcessingTime = Date.now() - startTime;
      metrics.errors++;
      
      console.error('❌ Error in enhanced mention refresh:', error);
      return {
        success: false,
        newMentions: 0,
        message: `Failed to refresh mentions: ${error instanceof Error ? error.message : String(error)}`,
        accountsChecked: 0,
        metrics,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

/**
 * Simple one-click mention refresh for users
 * Optimized for quick frontend calls with immediate feedback
 */
export const quickMentionRefresh = action({
  args: {
    accountHandle: v.optional(v.string()), // Optional: refresh specific account
  },
  handler: async (ctx, args) => {
    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        return {
          success: false,
          message: "Please log in to refresh mentions",
          newMentions: 0
        };
      }

      // 🚀 SMART RATE LIMITING: Check if we refreshed recently
      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        return {
          success: false,
          message: "User not found",
          newMentions: 0
        };
      }

      // Get user's last refresh time from database
      const lastRefresh = user.lastMentionRefresh || 0;
      const timeSinceLastRefresh = Date.now() - lastRefresh;
      const minRefreshInterval = 2 * 60 * 1000; // 2 minutes minimum between refreshes

      if (timeSinceLastRefresh < minRefreshInterval) {
        const waitTime = Math.ceil((minRefreshInterval - timeSinceLastRefresh) / 1000);
        return {
          success: false,
          message: `Please wait ${waitTime} seconds before refreshing again`,
          newMentions: 0,
          rateLimited: true,
          waitTime
        };
      }

      // 🎯 OPTIMIZED FOR LARGE BACKLOGS: Limit processing scope
      const result = await ctx.runAction(api.mentions.mentionMutations.fetchUserMentions, {
        accountHandle: args.accountHandle,
        hoursBack: 2, // Only check last 2 hours for quick refresh
        maxMentions: 10, // Much smaller limit to prevent overload
      });

      // Update user's last refresh timestamp
      await ctx.runMutation(api.users.updateUser, {
        userId: user._id,
        updates: { lastMentionRefresh: Date.now() }
      });

      return {
        success: result.success,
        message: result.success 
          ? `Quick refresh complete: ${result.newMentions} new mentions found`
          : result.message,
        newMentions: result.newMentions,
        accountsChecked: result.accountsChecked,
        timestamp: Date.now(),
        quickRefresh: true,
        optimizedForLargeBacklog: true,
      };

    } catch (error) {
      console.error('Error in quick mention refresh:', error);
      return {
        success: false,
        message: `Refresh failed: ${error instanceof Error ? error.message : String(error)}`,
        newMentions: 0,
        quickRefresh: true,
      };
    }
  },
});

/**
 * Process sentiment analysis for a mention
 * Automatically called when new mentions are created
 */
export const processMentionSentiment = action({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the mention data
      const mention = await ctx.runQuery(api.mentions.mentionQueries.getMentionById, {
        mentionId: args.mentionId,
      });

      if (!mention) {
        console.error("Mention not found for sentiment analysis:", args.mentionId);
        return { success: false, error: "Mention not found" };
      }

      // Skip if already analyzed
      if (mention.sentimentAnalysis) {
        console.log("Sentiment already analyzed for mention:", args.mentionId);
        return { success: true, skipped: true };
      }

      // Get the monitored account info to get the handle
      const account = await ctx.runQuery(api.mentions.mentionQueries.getTwitterAccountById, {
        accountId: mention.monitoredAccountId,
      });
      const accountHandle = account?.handle || "unknown";

      console.log("🎯 Starting sentiment analysis for mention:", args.mentionId);

      // Run sentiment analysis (exclude views from engagement to match validation)
      const sentimentResult = await ctx.runAction(api.ai.sentimentAnalysis.analyzeMentionSentiment, {
        mentionContent: mention.mentionContent,
        mentionAuthor: mention.mentionAuthor,
        mentionAuthorHandle: mention.mentionAuthorHandle,
        mentionType: mention.mentionType,
        accountHandle: accountHandle,
        engagement: {
          likes: mention.engagement.likes,
          retweets: mention.engagement.retweets,
          replies: mention.engagement.replies,
        },
        priority: mention.priority,
      });

      console.log("✅ Sentiment analysis completed for mention:", args.mentionId, sentimentResult);

      // Store the sentiment analysis results
      await ctx.runMutation(api.mentions.sentimentMutations.updateMentionSentiment, {
        mentionId: args.mentionId,
        sentimentAnalysis: sentimentResult,
      });

      console.log("💾 Sentiment analysis saved for mention:", args.mentionId);

      return { 
        success: true, 
        sentimentResult,
        sentiment: sentimentResult.sentiment,
        score: sentimentResult.sentimentScore,
      };

    } catch (error) {
      console.error("❌ Error processing sentiment for mention:", args.mentionId, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

/**
 * Batch process sentiment analysis for all unanalyzed mentions
 * Used for retroactive sentiment analysis
 */
export const batchProcessSentimentAnalysis = action({
  args: {
    limit: v.optional(v.number()),
    accountId: v.optional(v.id("twitterAccounts")),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    const startTime = Date.now();
    
    try {
      console.log("🚀 Starting batch sentiment analysis...");

      // Get mentions without sentiment analysis directly from database
      const allMentions = await ctx.runQuery(api.mentions.mentionQueries.getMentionsForSentimentAnalysis, {
        limit: limit * 2, // Get more to filter
      });
      
      // Filter to mentions without sentiment analysis
      const unanalyzedMentions = allMentions.filter(mention => !mention.sentimentAnalysis);
      
      if (unanalyzedMentions.length === 0) {
        console.log("✅ All mentions already have sentiment analysis");
        return {
          success: true,
          message: "All mentions already analyzed",
          processed: 0,
          skipped: 0,
          errors: 0,
        };
      }

      const mentionsToProcess = unanalyzedMentions.slice(0, limit);
      console.log(`📊 Found ${unanalyzedMentions.length} unanalyzed mentions, processing ${mentionsToProcess.length}`);

      let processed = 0;
      let errors = 0;
      const results = [];

      // Process mentions in batches of 5 to avoid overwhelming the AI API
      for (let i = 0; i < mentionsToProcess.length; i += 5) {
        const batch = mentionsToProcess.slice(i, i + 5);
        console.log(`Processing batch ${Math.floor(i/5) + 1}/${Math.ceil(mentionsToProcess.length/5)}`);

        // Process batch in parallel
        const batchPromises = batch.map(async (mention) => {
          try {
            const result = await ctx.runAction(api.mentions.mentionMutations.processMentionSentiment, {
              mentionId: mention._id,
            });
            
            if (result.success && !result.skipped) {
              processed++;
              console.log(`✅ Processed sentiment for mention ${mention._id}: ${result.sentiment} (${result.score}/100)`);
            }
            
            return result;
          } catch (error) {
            errors++;
            console.error(`❌ Error processing mention ${mention._id}:`, error);
            return { success: false, error: error instanceof Error ? error.message : String(error) };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Add delay between batches to respect rate limits
        if (i + 5 < mentionsToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay between batches
        }
      }

      const totalTime = Date.now() - startTime;
      console.log(`🎉 Batch sentiment analysis completed in ${totalTime}ms`);
      console.log(`📈 Results: ${processed} processed, ${errors} errors`);

      return {
        success: true,
        message: `Processed ${processed} mentions with ${errors} errors`,
        processed,
        skipped: unanalyzedMentions.length - processed - errors,
        errors,
        totalTime,
        results: results.slice(0, 10), // Return first 10 results for debugging
      };

    } catch (error) {
      console.error("❌ Error in batch sentiment processing:", error);
      return {
        success: false,
        message: `Batch processing failed: ${error instanceof Error ? error.message : String(error)}`,
        processed: 0,
        skipped: 0,
        errors: 1,
      };
    }
  },
});