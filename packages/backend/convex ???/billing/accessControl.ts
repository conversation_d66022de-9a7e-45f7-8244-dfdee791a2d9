import { query } from "../_generated/server";
import { v } from "convex/values";

/**
 * Check if user has access to a specific feature
 */
export const hasFeatureAccess = query({
  args: {
    feature: v.union(
      v.literal("basicMonitoring"),
      v.literal("premiumAi"),
      v.literal("imageGeneration"),
      v.literal("bulkProcessing"),
      v.literal("advancedAnalytics"),
      v.literal("prioritySupport"),
      v.literal("customIntegrations"),
      v.literal("whiteLabel")
    ),
  },
  handler: async (ctx, args) => {
    console.log(`🔐 Checking feature access: ${args.feature}`);
    
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      console.log("❌ No user identity found");
      return { hasAccess: false, reason: "Not authenticated" };
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      console.log("❌ User not found");
      return { hasAccess: false, reason: "User not found" };
    }

    // Get user's feature access
    const featureAccess = await ctx.db
      .query("featureAccess")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();

    if (!featureAccess) {
      console.log("📝 No feature access found, checking default starter access");
      // Default to starter plan features
      const starterFeatures = {
        basicMonitoring: true,
        premiumAi: false,
        imageGeneration: false,
        bulkProcessing: false,
        advancedAnalytics: false,
        prioritySupport: false,
        customIntegrations: false,
        whiteLabel: false,
      };
      
      const hasAccess = starterFeatures[args.feature];
      console.log(`✅ Default access for ${args.feature}: ${hasAccess}`);
      
      return { 
        hasAccess, 
        planId: "starter",
        reason: hasAccess ? null : "Feature not available in starter plan" 
      };
    }

    const hasAccess = featureAccess.features[args.feature];
    console.log(`✅ Feature access for ${args.feature}: ${hasAccess} (${featureAccess.planId} plan)`);
    
    return { 
      hasAccess, 
      planId: featureAccess.planId,
      reason: hasAccess ? null : `Feature not available in ${featureAccess.planId} plan` 
    };
  },
});

/**
 * Check if user has access to a specific plan
 */
export const hasPlanAccess = query({
  args: {
    planId: v.union(v.literal("starter"), v.literal("pro"), v.literal("enterprise")),
  },
  handler: async (ctx, args) => {
    console.log(`🎯 Checking plan access: ${args.planId}`);
    
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { hasAccess: false, reason: "Not authenticated" };
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return { hasAccess: false, reason: "User not found" };
    }

    // Get user's subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    const userPlanId = subscription?.planId || "starter";
    
    // Define plan hierarchy
    const planHierarchy = {
      starter: 1,
      pro: 2,
      enterprise: 3,
    };

    const userPlanLevel = planHierarchy[userPlanId];
    const requiredPlanLevel = planHierarchy[args.planId];
    
    const hasAccess = userPlanLevel >= requiredPlanLevel;
    
    console.log(`✅ Plan access check: User has ${userPlanId} (level ${userPlanLevel}), requires ${args.planId} (level ${requiredPlanLevel}) = ${hasAccess}`);
    
    return { 
      hasAccess, 
      userPlanId,
      requiredPlanId: args.planId,
      reason: hasAccess ? null : `Requires ${args.planId} plan or higher` 
    };
  },
});

/**
 * Get all user permissions and limits
 */
export const getUserPermissions = query({
  args: {},
  handler: async (ctx) => {
    console.log("🔍 Getting user permissions...");
    
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Get subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    // Get feature access
    const featureAccess = await ctx.db
      .query("featureAccess")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();

    // Get current usage
    const today = new Date().toISOString().split('T')[0];
    const usage = await ctx.db
      .query("usageTracking")
      .withIndex("by_user_date", (q) => q.eq("userId", user._id).eq("date", today))
      .first();

    const planId = subscription?.planId || "starter";
    
    // Default permissions for starter plan
    const defaultPermissions = {
      planId: "starter",
      subscription: {
        status: "inactive",
        isActive: false,
        currentPeriodEnd: null,
      },
      features: {
        basicMonitoring: true,
        premiumAi: false,
        imageGeneration: false,
        bulkProcessing: false,
        advancedAnalytics: false,
        prioritySupport: false,
        customIntegrations: false,
        whiteLabel: false,
      },
      limits: {
        maxAccounts: 3,
        maxAiResponses: 100,
        maxImageGenerations: 0,
        maxApiRequests: 1000,
        maxBulkOperations: 0,
      },
      usage: {
        aiResponses: 0,
        imageGenerations: 0,
        apiRequests: 0,
        bulkOperations: 0,
        premiumAiCalls: 0,
        analyticsQueries: 0,
      },
    };

    if (!subscription && !featureAccess) {
      console.log("📝 No subscription or feature access found, returning defaults");
      return defaultPermissions;
    }

    const result = {
      planId,
      subscription: subscription ? {
        status: subscription.status,
        isActive: subscription.status === "active",
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        trialEnd: subscription.trialEnd,
      } : defaultPermissions.subscription,
      features: featureAccess?.features || defaultPermissions.features,
      limits: featureAccess?.limits || defaultPermissions.limits,
      usage: usage?.usage || defaultPermissions.usage,
    };

    console.log(`✅ User permissions retrieved for ${planId} plan`);
    return result;
  },
});

/**
 * Utility function to get plan comparison data
 */
export const getPlanComparison = query({
  args: {},
  handler: async (ctx) => {
    return {
      starter: {
        name: "Starter",
        price: "$19/month",
        features: {
          basicMonitoring: true,
          premiumAi: false,
          imageGeneration: false,
          bulkProcessing: false,
          advancedAnalytics: false,
          prioritySupport: false,
          customIntegrations: false,
          whiteLabel: false,
        },
        limits: {
          maxAccounts: 3,
          maxAiResponses: 100,
          maxImageGenerations: 0,
          maxApiRequests: 1000,
          maxBulkOperations: 0,
        },
        description: "Perfect for individuals getting started with AI-powered Twitter engagement",
      },
      pro: {
        name: "Pro",
        price: "$49/month",
        features: {
          basicMonitoring: true,
          premiumAi: true,
          imageGeneration: true,
          bulkProcessing: false,
          advancedAnalytics: true,
          prioritySupport: true,
          customIntegrations: false,
          whiteLabel: false,
        },
        limits: {
          maxAccounts: 10,
          maxAiResponses: 500,
          maxImageGenerations: 50,
          maxApiRequests: 5000,
          maxBulkOperations: 10,
        },
        description: "Ideal for professionals and small teams who need advanced AI features",
      },
      enterprise: {
        name: "Enterprise",
        price: "$99/month",
        features: {
          basicMonitoring: true,
          premiumAi: true,
          imageGeneration: true,
          bulkProcessing: true,
          advancedAnalytics: true,
          prioritySupport: true,
          customIntegrations: true,
          whiteLabel: true,
        },
        limits: {
          maxAccounts: -1, // unlimited
          maxAiResponses: -1, // unlimited
          maxImageGenerations: -1, // unlimited
          maxApiRequests: 25000,
          maxBulkOperations: -1, // unlimited
        },
        description: "Complete solution for agencies and large organizations",
      },
    };
  },
});
