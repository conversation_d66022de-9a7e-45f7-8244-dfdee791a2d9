import { action, mutation, query } from "../_generated/server";
import { v } from "convex/values";
import { getOpenRouterClient } from "../lib/openrouter_client";
/**
 * Generate embeddings for tweet content
 */
export const generateTweetEmbedding = action({
    args: {
        tweetId: v.id("tweets"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            // Preprocess content for better embeddings
            const processedContent = preprocessTextForEmbedding(args.content);
            const embeddingResponse = await client.generateEmbedding(processedContent, args.model || 'text-embedding-3-small');
            // Store embedding in database
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingGeneration.storeTweetEmbedding, {
                tweetId: args.tweetId,
                embedding: embeddingResponse.embedding,
                model: embeddingResponse.model,
            });
            return {
                embeddingId,
                tweetId: args.tweetId,
                model: embeddingResponse.model,
                dimensions: embeddingResponse.embedding.length,
                usage: embeddingResponse.usage,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Tweet embedding generation failed:', error);
            throw new Error(`Tweet embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate embeddings for mention content
 */
export const generateMentionEmbedding = action({
    args: {
        mentionId: v.id("mentions"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const processedContent = preprocessTextForEmbedding(args.content);
            const embeddingResponse = await client.generateEmbedding(processedContent, args.model || 'text-embedding-3-small');
            // Store embedding in database
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingGeneration.storeMentionEmbedding, {
                mentionId: args.mentionId,
                embedding: embeddingResponse.embedding,
                model: embeddingResponse.model,
            });
            return {
                embeddingId,
                mentionId: args.mentionId,
                model: embeddingResponse.model,
                dimensions: embeddingResponse.embedding.length,
                usage: embeddingResponse.usage,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Mention embedding generation failed:', error);
            throw new Error(`Mention embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate embeddings for response content
 */
export const generateResponseEmbedding = action({
    args: {
        responseId: v.id("responses"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const processedContent = preprocessTextForEmbedding(args.content);
            const embeddingResponse = await client.generateEmbedding(processedContent, args.model || 'text-embedding-3-small');
            // Store embedding in database
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingGeneration.storeResponseEmbedding, {
                responseId: args.responseId,
                embedding: embeddingResponse.embedding,
                model: embeddingResponse.model,
            });
            return {
                embeddingId,
                responseId: args.responseId,
                model: embeddingResponse.model,
                dimensions: embeddingResponse.embedding.length,
                usage: embeddingResponse.usage,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Response embedding generation failed:', error);
            throw new Error(`Response embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate embeddings for user context
 */
export const generateUserContextEmbedding = action({
    args: {
        userId: v.id("users"),
        contextType: v.string(),
        content: v.string(),
        weight: v.optional(v.number()),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const processedContent = preprocessTextForEmbedding(args.content);
            const embeddingResponse = await client.generateEmbedding(processedContent, args.model || 'text-embedding-3-small');
            // Store embedding in database
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingGeneration.storeUserContextEmbedding, {
                userId: args.userId,
                contextType: args.contextType,
                content: args.content,
                embedding: embeddingResponse.embedding,
                model: embeddingResponse.model,
                weight: args.weight || 1.0,
            });
            return {
                embeddingId,
                userId: args.userId,
                contextType: args.contextType,
                model: embeddingResponse.model,
                dimensions: embeddingResponse.embedding.length,
                usage: embeddingResponse.usage,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('User context embedding generation failed:', error);
            throw new Error(`User context embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Batch generate embeddings for multiple tweets
 */
export const generateTweetEmbeddingsBatch = action({
    args: {
        tweets: v.array(v.object({
            tweetId: v.id("tweets"),
            content: v.string(),
        })),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            // Preprocess all content
            const processedTexts = args.tweets.map(tweet => preprocessTextForEmbedding(tweet.content));
            const embeddingResponses = await client.generateEmbeddingsBatch(processedTexts, args.model || 'text-embedding-3-small');
            // Store all embeddings
            const results = [];
            for (let i = 0; i < args.tweets.length; i++) {
                try {
                    const embeddingId = await ctx.runMutation(api.embeddings.embeddingGeneration.storeTweetEmbedding, {
                        tweetId: args.tweets[i].tweetId,
                        embedding: embeddingResponses[i].embedding,
                        model: embeddingResponses[i].model,
                    });
                    results.push({
                        tweetId: args.tweets[i].tweetId,
                        embeddingId,
                        success: true,
                    });
                }
                catch (storeError) {
                    console.error(`Failed to store embedding for tweet ${args.tweets[i].tweetId}:`, storeError);
                    results.push({
                        tweetId: args.tweets[i].tweetId,
                        embeddingId: null,
                        success: false,
                        error: storeError instanceof Error ? storeError.message : 'Unknown error',
                    });
                }
            }
            return {
                results,
                totalProcessed: args.tweets.length,
                successCount: results.filter(r => r.success).length,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Batch tweet embedding generation failed:', error);
            throw new Error(`Batch embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate embeddings for semantic search query
 */
export const generateSearchEmbedding = action({
    args: {
        query: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const processedQuery = preprocessTextForEmbedding(args.query);
            const embeddingResponse = await client.generateEmbedding(processedQuery, args.model || 'text-embedding-3-small');
            return {
                query: args.query,
                embedding: embeddingResponse.embedding,
                model: embeddingResponse.model,
                dimensions: embeddingResponse.embedding.length,
                usage: embeddingResponse.usage,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Search embedding generation failed:', error);
            throw new Error(`Search embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Database mutations for storing embeddings
 */
export const storeTweetEmbedding = mutation({
    args: {
        tweetId: v.id("tweets"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        return await ctx.db.insert("tweetEmbeddings", {
            tweetId: args.tweetId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
    },
});
export const storeMentionEmbedding = mutation({
    args: {
        mentionId: v.id("mentions"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        return await ctx.db.insert("mentionEmbeddings", {
            mentionId: args.mentionId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
    },
});
export const storeResponseEmbedding = mutation({
    args: {
        responseId: v.id("responses"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        return await ctx.db.insert("responseEmbeddings", {
            responseId: args.responseId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
    },
});
export const storeUserContextEmbedding = mutation({
    args: {
        userId: v.id("users"),
        contextType: v.string(),
        content: v.string(),
        embedding: v.array(v.number()),
        model: v.string(),
        weight: v.number(),
    },
    handler: async (ctx, args) => {
        return await ctx.db.insert("userContextEmbeddings", {
            userId: args.userId,
            contextType: args.contextType,
            content: args.content,
            embedding: args.embedding,
            model: args.model,
            weight: args.weight,
            createdAt: Date.now(),
            updatedAt: Date.now(),
        });
    },
});
/**
 * Query functions for vector search
 */
export const findSimilarTweets = query({
    args: {
        embedding: v.array(v.number()),
        limit: v.optional(v.number()),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        // Fallback to regular query since vectorSearch is not available
        const results = await ctx.db
            .query("tweetEmbeddings")
            .filter((q) => args.model ? q.eq(q.field("model"), args.model) : q.neq(q.field("_id"), null))
            .order("desc")
            .take(args.limit || 10);
        return results;
    },
});
export const findSimilarMentions = query({
    args: {
        embedding: v.array(v.number()),
        limit: v.optional(v.number()),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const results = await ctx.db
            .query("mentionEmbeddings")
            .filter((q) => args.model ? q.eq(q.field("model"), args.model) : q.neq(q.field("_id"), null))
            .order("desc")
            .take(args.limit || 10);
        return results;
    },
});
export const findSimilarResponses = query({
    args: {
        embedding: v.array(v.number()),
        limit: v.optional(v.number()),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const results = await ctx.db
            .query("responseEmbeddings")
            .filter((q) => args.model ? q.eq(q.field("model"), args.model) : q.neq(q.field("_id"), null))
            .order("desc")
            .take(args.limit || 10);
        return results;
    },
});
export const findSimilarUserContext = query({
    args: {
        userId: v.id("users"),
        embedding: v.array(v.number()),
        contextType: v.optional(v.string()),
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const results = await ctx.db
            .query("userContextEmbeddings")
            .filter((q) => {
            let filter = q.eq(q.field("userId"), args.userId);
            if (args.contextType) {
                filter = q.and(filter, q.eq(q.field("contextType"), args.contextType));
            }
            return filter;
        })
            .order("desc")
            .take(args.limit || 5);
        return results;
    },
});
/**
 * Helper functions
 */
function preprocessTextForEmbedding(text) {
    // Clean and normalize text for better embeddings
    return text
        // Remove URLs
        .replace(/https?:\/\/[^\s]+/g, '')
        // Remove @mentions
        .replace(/@\w+/g, '')
        // Remove hashtags # but keep the word
        .replace(/#(\w+)/g, '$1')
        // Remove extra whitespace
        .replace(/\s+/g, ' ')
        // Trim
        .trim()
        // Convert to lowercase for consistency
        .toLowerCase();
}
// Note: Import will be available after Convex generates the API
const api = globalThis.api;
