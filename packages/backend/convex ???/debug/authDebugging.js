import { query, action } from "../_generated/server";
import { v } from "convex/values";
import { debugLog, debugTimer, isDebugEnabled } from "../lib/debugConfig";
/**
 * 🔍 COMPREHENSIVE AUTHENTICATION DEBUGGING SUITE
 *
 * This file contains detailed debugging functions to diagnose
 * JWT token validation and user identity issues between Clerk and Convex.
 *
 * NOTE: These functions are only active when DEBUG_AUTH is enabled via environment variables.
 */
/**
 * Test basic authentication context and user identity
 */
export const testAuthContext = query({
    args: {},
    handler: async (ctx) => {
        // Early return if auth debugging is disabled
        if (!isDebugEnabled('auth')) {
            return {
                error: "Auth debugging is disabled. Set DEBUG_AUTH=true to enable.",
                debugEnabled: false
            };
        }
        debugTimer.start('testAuthContext');
        debugLog.debug('Auth', 'Starting auth context test');
        try {
            // Test 1: Check if auth object exists
            const hasAuth = !!ctx.auth;
            debugLog.debug('Auth', `Auth object exists: ${hasAuth}`);
            // Test 2: Try to get user identity
            let identity = null;
            let identityError = null;
            try {
                identity = await ctx.auth.getUserIdentity();
                debugLog.debug('Auth', 'Successfully retrieved user identity', {
                    subject: identity?.subject,
                    issuer: identity?.issuer
                });
            }
            catch (error) {
                identityError = error instanceof Error ? error.message : String(error);
                debugLog.error('Auth', 'Failed to get user identity', { error: identityError });
            }
            // Test 3: Check auth state
            const authState = {
                hasAuth,
                identity: identity ? {
                    issuer: identity.issuer,
                    subject: identity.subject,
                    tokenIdentifier: identity.tokenIdentifier,
                    name: identity.name,
                    email: identity.email,
                    emailVerified: identity.emailVerified,
                    // Don't log the full JWT token for security
                } : null,
                identityError,
            };
            // Test 4: Try to find user in database if identity exists
            let userRecord = null;
            let userError = null;
            if (identity) {
                try {
                    userRecord = await ctx.db
                        .query("users")
                        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
                        .first();
                }
                catch (error) {
                    userError = error instanceof Error ? error.message : String(error);
                }
            }
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                timestamp: new Date().toISOString(),
                executionTime,
                authState,
                userRecord: userRecord ? {
                    _id: userRecord._id,
                    name: userRecord.name,
                    email: userRecord.email,
                    clerkId: userRecord.clerkId,
                    _creationTime: userRecord._creationTime,
                } : null,
                userError,
                diagnostics: {
                    authContextExists: hasAuth,
                    identityExists: !!identity,
                    userRecordExists: !!userRecord,
                    fullAuthFlow: hasAuth && !!identity && !!userRecord,
                }
            };
        }
        catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
            };
        }
    },
});
/**
 * Test JWT token validation process
 */
export const testJWTValidation = query({
    args: {},
    handler: async (ctx) => {
        const startTime = Date.now();
        try {
            // Get authentication headers and context
            const identity = await ctx.auth.getUserIdentity();
            if (!identity) {
                return {
                    success: false,
                    timestamp: new Date().toISOString(),
                    executionTime: Date.now() - startTime,
                    error: "No JWT token found in request",
                    suggestion: "Make sure user is logged in and JWT token is being sent",
                };
            }
            // Analyze JWT token structure
            const tokenAnalysis = {
                issuer: identity.issuer,
                subject: identity.subject,
                tokenIdentifier: identity.tokenIdentifier,
                issuedAt: identity.iat,
                expiresAt: identity.exp,
                audience: identity.aud,
                name: identity.name,
                email: identity.email,
                emailVerified: identity.emailVerified,
            };
            // Validate expected values
            const expectedIssuer = "https://ethical-redbird-87.clerk.accounts.dev";
            const issuerMatches = identity.issuer === expectedIssuer;
            const currentTime = Math.floor(Date.now() / 1000);
            const tokenExpired = identity.exp && identity.exp < currentTime;
            const tokenNotYetValid = identity.iat && identity.iat > currentTime;
            return {
                success: true,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                tokenAnalysis,
                validation: {
                    issuerMatches,
                    expectedIssuer,
                    actualIssuer: identity.issuer,
                    tokenExpired,
                    tokenNotYetValid,
                    timeUntilExpiry: identity.exp ? identity.exp - currentTime : null,
                },
                recommendations: [
                    !issuerMatches ? `❌ Issuer mismatch. Expected: ${expectedIssuer}, Got: ${identity.issuer}` : "✅ Issuer matches",
                    tokenExpired ? "❌ Token is expired" : "✅ Token is not expired",
                    tokenNotYetValid ? "❌ Token is not yet valid" : "✅ Token timing is valid",
                ]
            };
        }
        catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                suggestion: "Check JWT token format and Clerk configuration",
            };
        }
    },
});
/**
 * Test user creation and synchronization
 */
export const testUserSync = action({
    args: {
        testMode: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const startTime = Date.now();
        try {
            // Get user identity
            const identity = await ctx.auth.getUserIdentity();
            if (!identity) {
                return {
                    success: false,
                    timestamp: new Date().toISOString(),
                    executionTime: Date.now() - startTime,
                    error: "No authentication identity found",
                    stage: "identity_check",
                };
            }
            // Check if user exists
            const existingUser = await ctx.db
                .query("users")
                .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
                .first();
            let syncResult = null;
            if (!existingUser && !args.testMode) {
                // Create user if doesn't exist (only if not in test mode)
                try {
                    const newUserId = await ctx.db.insert("users", {
                        name: identity.name || "Unknown User",
                        email: identity.email || "",
                        clerkId: identity.subject,
                        image: null, // Would be provided by Clerk in real scenario
                    });
                    syncResult = {
                        action: "created",
                        userId: newUserId,
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        timestamp: new Date().toISOString(),
                        executionTime: Date.now() - startTime,
                        error: error instanceof Error ? error.message : String(error),
                        stage: "user_creation",
                    };
                }
            }
            else {
                syncResult = {
                    action: existingUser ? "found_existing" : "test_mode_skipped",
                    userId: existingUser?._id,
                };
            }
            // Test Twitter accounts association
            const twitterAccounts = existingUser ? await ctx.db
                .query("twitterAccounts")
                .withIndex("by_user", (q) => q.eq("userId", existingUser._id))
                .collect() : [];
            return {
                success: true,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                identity: {
                    subject: identity.subject,
                    name: identity.name,
                    email: identity.email,
                },
                syncResult,
                existingUser: existingUser ? {
                    _id: existingUser._id,
                    name: existingUser.name,
                    email: existingUser.email,
                    clerkId: existingUser.clerkId,
                } : null,
                twitterAccounts: twitterAccounts.map(acc => ({
                    _id: acc._id,
                    handle: acc.handle,
                    isActive: acc.isActive,
                })),
                diagnostics: {
                    userExists: !!existingUser,
                    hasTwitterAccounts: twitterAccounts.length > 0,
                    accountCount: twitterAccounts.length,
                }
            };
        }
        catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                stage: "general_error",
            };
        }
    },
});
/**
 * Test mention queries with authentication
 */
export const testAuthenticatedQueries = query({
    args: {},
    handler: async (ctx) => {
        const startTime = Date.now();
        try {
            // Test authentication
            const identity = await ctx.auth.getUserIdentity();
            if (!identity) {
                return {
                    success: false,
                    timestamp: new Date().toISOString(),
                    executionTime: Date.now() - startTime,
                    error: "Authentication required for this test",
                    stage: "authentication",
                };
            }
            // Get user record
            const user = await ctx.db
                .query("users")
                .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
                .first();
            if (!user) {
                return {
                    success: false,
                    timestamp: new Date().toISOString(),
                    executionTime: Date.now() - startTime,
                    error: "User record not found in database",
                    stage: "user_lookup",
                };
            }
            // Test user's Twitter accounts
            const accounts = await ctx.db
                .query("twitterAccounts")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .take(10);
            // Test mentions for each account
            const mentionTests = [];
            for (const account of accounts) {
                const mentions = await ctx.db
                    .query("mentions")
                    .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", account._id))
                    .take(5);
                mentionTests.push({
                    accountId: account._id,
                    handle: account.handle,
                    mentionCount: mentions.length,
                    recentMentions: mentions.map(m => ({
                        _id: m._id,
                        content: m.mentionContent.substring(0, 100),
                        createdAt: m.createdAt,
                        priority: m.priority,
                    }))
                });
            }
            return {
                success: true,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                user: {
                    _id: user._id,
                    name: user.name,
                    email: user.email,
                    clerkId: user.clerkId,
                },
                accounts: accounts.map(acc => ({
                    _id: acc._id,
                    handle: acc.handle,
                    isActive: acc.isActive,
                })),
                mentionTests,
                summary: {
                    totalAccounts: accounts.length,
                    totalMentionsFound: mentionTests.reduce((sum, test) => sum + test.mentionCount, 0),
                }
            };
        }
        catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                executionTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                stage: "query_execution",
            };
        }
    },
});
/**
 * Complete authentication health check
 */
export const authHealthCheck = query({
    args: {},
    handler: async (ctx) => {
        const startTime = Date.now();
        const checks = [];
        // Check 1: Auth context
        const hasAuth = !!ctx.auth;
        checks.push({
            name: "Auth Context",
            status: hasAuth ? "✅ PASS" : "❌ FAIL",
            details: hasAuth ? "Authentication context available" : "No authentication context",
        });
        // Check 2: User identity
        let identity = null;
        try {
            identity = await ctx.auth.getUserIdentity();
            checks.push({
                name: "User Identity",
                status: identity ? "✅ PASS" : "❌ FAIL",
                details: identity ? `User: ${identity.subject}` : "No user identity found",
            });
        }
        catch (error) {
            checks.push({
                name: "User Identity",
                status: "❌ ERROR",
                details: error instanceof Error ? error.message : String(error),
            });
        }
        // Check 3: User record
        let userRecord = null;
        if (identity) {
            try {
                userRecord = await ctx.db
                    .query("users")
                    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
                    .first();
                checks.push({
                    name: "User Record",
                    status: userRecord ? "✅ PASS" : "⚠️ MISSING",
                    details: userRecord ? `Found user: ${userRecord.name}` : "User not found in database",
                });
            }
            catch (error) {
                checks.push({
                    name: "User Record",
                    status: "❌ ERROR",
                    details: error instanceof Error ? error.message : String(error),
                });
            }
        }
        else {
            checks.push({
                name: "User Record",
                status: "⏭️ SKIPPED",
                details: "No identity to check",
            });
        }
        // Check 4: Twitter accounts
        if (userRecord) {
            try {
                const accounts = await ctx.db
                    .query("twitterAccounts")
                    .withIndex("by_user", (q) => q.eq("userId", userRecord._id))
                    .take(5);
                checks.push({
                    name: "Twitter Accounts",
                    status: accounts.length > 0 ? "✅ PASS" : "⚠️ NONE",
                    details: `Found ${accounts.length} Twitter accounts`,
                });
            }
            catch (error) {
                checks.push({
                    name: "Twitter Accounts",
                    status: "❌ ERROR",
                    details: error instanceof Error ? error.message : String(error),
                });
            }
        }
        else {
            checks.push({
                name: "Twitter Accounts",
                status: "⏭️ SKIPPED",
                details: "No user record to check",
            });
        }
        const overallStatus = checks.every(check => check.status.includes("PASS")) ? "✅ HEALTHY" :
            checks.some(check => check.status.includes("ERROR")) ? "❌ UNHEALTHY" : "⚠️ PARTIAL";
        return {
            success: true,
            timestamp: new Date().toISOString(),
            executionTime: Date.now() - startTime,
            overallStatus,
            checks,
            summary: {
                total: checks.length,
                passed: checks.filter(c => c.status.includes("PASS")).length,
                failed: checks.filter(c => c.status.includes("FAIL") || c.status.includes("ERROR")).length,
                warnings: checks.filter(c => c.status.includes("MISSING") || c.status.includes("NONE")).length,
            }
        };
    },
});
