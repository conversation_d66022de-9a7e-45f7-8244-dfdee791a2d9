import { query } from "./_generated/server";
import { v } from "convex/values";
/**
 * Get all responses for the current user
 */
export const getUserResponses = query({
    args: {
        status: v.optional(v.union(v.literal("draft"), v.literal("approved"), v.literal("declined"), v.literal("posted"), v.literal("failed"))),
        targetType: v.optional(v.union(v.literal("tweet"), v.literal("mention"))),
        limit: v.optional(v.number()),
        userId: v.optional(v.id("users")),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        let query = ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", user._id));
        let responses = await query.order("desc").take(args.limit || 50);
        // Apply filters
        if (args.status) {
            responses = responses.filter(r => r.status === args.status);
        }
        if (args.targetType) {
            responses = responses.filter(r => r.targetType === args.targetType);
        }
        // Enrich with target information
        const enrichedResponses = await Promise.all(responses.map(async (response) => {
            const target = await ctx.db.get(response.targetId);
            return {
                ...response,
                target: target ? {
                    id: target._id,
                    content: 'content' in target ? target.content : target.mentionContent,
                    author: 'author' in target ? target.author : target.mentionAuthor,
                    authorHandle: 'authorHandle' in target ? target.authorHandle : target.mentionAuthorHandle,
                    url: target.url,
                } : null,
            };
        }));
        return enrichedResponses;
    },
});
/**
 * Get responses for a specific tweet or mention
 */
export const getResponsesForTarget = query({
    args: {
        targetType: v.union(v.literal("tweet"), v.literal("mention")),
        targetId: v.union(v.id("tweets"), v.id("mentions")),
    },
    handler: async (ctx, args) => {
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_target", (q) => q.eq("targetType", args.targetType).eq("targetId", args.targetId))
            .order("desc")
            .collect();
        return responses;
    },
});
/**
 * Get pending responses (drafts awaiting approval)
 */
export const getPendingResponses = query({
    args: {
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_status", (q) => q.eq("status", "draft"))
            .filter((q) => q.eq(q.field("userId"), user._id))
            .order("desc")
            .take(args.limit || 20);
        // Enrich with target information
        const enrichedResponses = await Promise.all(responses.map(async (response) => {
            const target = await ctx.db.get(response.targetId);
            return {
                ...response,
                target: target ? {
                    id: target._id,
                    content: 'content' in target ? target.content : target.mentionContent,
                    author: 'author' in target ? target.author : target.mentionAuthor,
                    authorHandle: 'authorHandle' in target ? target.authorHandle : target.mentionAuthorHandle,
                    url: target.url,
                    createdAt: 'createdAt' in target ? target.createdAt : target.createdAt,
                } : null,
            };
        }));
        return enrichedResponses;
    },
});
/**
 * Get response performance analytics
 */
export const getResponseAnalytics = query({
    args: {
        timeframe: v.optional(v.union(v.literal("7d"), v.literal("30d"), v.literal("90d"))),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const timeframe = args.timeframe || "30d";
        const daysBack = timeframe === "7d" ? 7 : timeframe === "30d" ? 30 : 90;
        const timeAgo = Date.now() - daysBack * 24 * 60 * 60 * 1000;
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .filter((q) => q.gte(q.field("createdAt"), timeAgo))
            .collect();
        const posted = responses.filter(r => r.status === "posted");
        const totalActualEngagement = posted.reduce((sum, r) => {
            if (r.actualEngagement) {
                return sum + r.actualEngagement.likes + r.actualEngagement.retweets + r.actualEngagement.replies;
            }
            return sum;
        }, 0);
        const totalEstimatedEngagement = posted.reduce((sum, r) => {
            if (r.estimatedEngagement) {
                return sum + r.estimatedEngagement.likes + r.estimatedEngagement.retweets + r.estimatedEngagement.replies;
            }
            return sum;
        }, 0);
        // Performance by style
        const stylePerformance = {};
        responses.forEach(response => {
            if (!stylePerformance[response.style]) {
                stylePerformance[response.style] = {
                    count: 0,
                    avgConfidence: 0,
                    avgEngagement: 0,
                    posted: 0,
                };
            }
            const style = stylePerformance[response.style];
            style.count++;
            style.avgConfidence += response.confidence;
            if (response.status === "posted") {
                style.posted++;
                if (response.actualEngagement) {
                    style.avgEngagement += response.actualEngagement.likes +
                        response.actualEngagement.retweets + response.actualEngagement.replies;
                }
            }
        });
        // Calculate averages
        Object.values(stylePerformance).forEach(style => {
            style.avgConfidence = style.avgConfidence / style.count;
            style.avgEngagement = style.posted > 0 ? style.avgEngagement / style.posted : 0;
        });
        return {
            timeframe,
            totalResponses: responses.length,
            byStatus: {
                draft: responses.filter(r => r.status === "draft").length,
                approved: responses.filter(r => r.status === "approved").length,
                posted: posted.length,
                declined: responses.filter(r => r.status === "declined").length,
                failed: responses.filter(r => r.status === "failed").length,
            },
            engagement: {
                totalActual: totalActualEngagement,
                totalEstimated: totalEstimatedEngagement,
                accuracy: totalEstimatedEngagement > 0
                    ? Math.round((totalActualEngagement / totalEstimatedEngagement) * 100)
                    : 0,
                averagePerPost: posted.length > 0 ? Math.round(totalActualEngagement / posted.length) : 0,
            },
            stylePerformance,
            topPerforming: posted
                .filter(r => r.actualEngagement)
                .sort((a, b) => {
                const aEng = a.actualEngagement.likes + a.actualEngagement.retweets + a.actualEngagement.replies;
                const bEng = b.actualEngagement.likes + b.actualEngagement.retweets + b.actualEngagement.replies;
                return bEng - aEng;
            })
                .slice(0, 5)
                .map(r => ({
                id: r._id,
                content: r.content.substring(0, 100) + "...",
                style: r.style,
                engagement: r.actualEngagement,
                confidence: r.confidence,
            })),
            generatedAt: Date.now(),
        };
    },
});
/**
 * Get response drafts/versions for a response
 */
export const getResponseDrafts = query({
    args: {
        responseId: v.id("responses"),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        // Verify ownership
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user || response.userId !== user._id) {
            throw new Error("Not authorized to view these drafts");
        }
        const drafts = await ctx.db
            .query("responseDrafts")
            .withIndex("by_response", (q) => q.eq("responseId", args.responseId))
            .order("desc")
            .collect();
        return drafts;
    },
});
/**
 * Search responses by content
 */
export const searchResponses = query({
    args: {
        searchQuery: v.string(),
        status: v.optional(v.union(v.literal("draft"), v.literal("approved"), v.literal("declined"), v.literal("posted"), v.literal("failed"))),
        style: v.optional(v.string()),
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        let query = ctx.db
            .query("responses")
            .withSearchIndex("search_responses", (q) => q.search("content", args.searchQuery));
        // Add filters
        if (args.status) {
            query = query.filter((q) => q.eq(q.field("status"), args.status));
        }
        if (args.style) {
            query = query.filter((q) => q.eq(q.field("style"), args.style));
        }
        query = query.filter((q) => q.eq(q.field("userId"), user._id));
        const responses = await query.take(args.limit || 20);
        return responses;
    },
});
/**
 * Get responses pending review (alias for getPendingResponses)
 */
export const getResponsesPendingReview = query({
    args: {
        limit: v.optional(v.number()),
        userId: v.optional(v.id("users")),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_status", (q) => q.eq("status", "draft"))
            .filter((q) => q.eq(q.field("userId"), user._id))
            .order("desc")
            .take(args.limit || 20);
        // Enrich with target information
        const enrichedResponses = await Promise.all(responses.map(async (response) => {
            const target = await ctx.db.get(response.targetId);
            return {
                ...response,
                target: target ? {
                    id: target._id,
                    content: 'content' in target ? target.content : target.mentionContent,
                    author: 'author' in target ? target.author : target.mentionAuthor,
                    authorHandle: 'authorHandle' in target ? target.authorHandle : target.mentionAuthorHandle,
                    url: target.url,
                    createdAt: 'createdAt' in target ? target.createdAt : target.createdAt,
                } : null,
            };
        }));
        return enrichedResponses;
    },
});
/**
 * Get response statistics by style
 */
export const getResponseStatsByStyle = query({
    args: {
        style: v.optional(v.string()),
        timeframe: v.optional(v.union(v.literal("7d"), v.literal("30d"), v.literal("90d"))),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return {
                styles: [],
                totalResponses: 0,
                generatedAt: Date.now(),
            };
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            return {
                styles: [],
                totalResponses: 0,
                generatedAt: Date.now(),
            };
        }
        const timeframe = args.timeframe || "30d";
        const daysBack = timeframe === "7d" ? 7 : timeframe === "30d" ? 30 : 90;
        const timeAgo = Date.now() - daysBack * 24 * 60 * 60 * 1000;
        let query = ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .filter((q) => q.gte(q.field("createdAt"), timeAgo));
        const responses = await query.collect();
        // Filter by style if specified
        const filteredResponses = args.style
            ? responses.filter(r => r.style === args.style)
            : responses;
        // Group by style
        const styleStats = {};
        filteredResponses.forEach(response => {
            if (!styleStats[response.style]) {
                styleStats[response.style] = {
                    count: 0,
                    avgConfidence: 0,
                    avgEngagement: 0,
                    posted: 0,
                    success_rate: 0,
                };
            }
            const style = styleStats[response.style];
            style.count++;
            style.avgConfidence += response.confidence;
            if (response.status === "posted") {
                style.posted++;
                if (response.actualEngagement) {
                    style.avgEngagement += response.actualEngagement.likes +
                        response.actualEngagement.retweets + response.actualEngagement.replies;
                }
            }
        });
        // Calculate averages and success rates
        const styleArray = Object.entries(styleStats).map(([style, stats]) => ({
            style,
            count: stats.count,
            avgConfidence: stats.count > 0 ? Math.round((stats.avgConfidence / stats.count) * 100) : 0,
            avgEngagement: stats.posted > 0 ? Math.round(stats.avgEngagement / stats.posted) : 0,
            posted: stats.posted,
            success_rate: stats.count > 0 ? Math.round((stats.posted / stats.count) * 100) : 0,
        }));
        return {
            styles: styleArray.sort((a, b) => b.count - a.count),
            totalResponses: filteredResponses.length,
            timeframe,
            generatedAt: Date.now(),
        };
    },
});
/**
 * Get response statistics summary
 */
export const getResponseStats = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return {
                total: 0,
                today: 0,
                thisWeek: 0,
                byStatus: {
                    draft: 0,
                    approved: 0,
                    posted: 0,
                    declined: 0,
                    failed: 0,
                },
                engagement: {
                    total: 0,
                    average: 0,
                },
                averageConfidence: 0,
            };
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            return {
                total: 0,
                today: 0,
                thisWeek: 0,
                byStatus: {
                    draft: 0,
                    approved: 0,
                    posted: 0,
                    declined: 0,
                    failed: 0,
                },
                engagement: {
                    total: 0,
                    average: 0,
                },
                averageConfidence: 0,
            };
        }
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .collect();
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
        const recentResponses = responses.filter(r => r.createdAt >= oneWeekAgo);
        const todayResponses = responses.filter(r => r.createdAt >= oneDayAgo);
        const posted = responses.filter(r => r.status === "posted");
        const totalEngagement = posted.reduce((sum, r) => {
            if (r.actualEngagement) {
                return sum + r.actualEngagement.likes + r.actualEngagement.retweets + r.actualEngagement.replies;
            }
            return sum;
        }, 0);
        return {
            total: responses.length,
            today: todayResponses.length,
            thisWeek: recentResponses.length,
            byStatus: {
                draft: responses.filter(r => r.status === "draft").length,
                approved: responses.filter(r => r.status === "approved").length,
                posted: posted.length,
                declined: responses.filter(r => r.status === "declined").length,
                failed: responses.filter(r => r.status === "failed").length,
            },
            engagement: {
                total: totalEngagement,
                average: posted.length > 0 ? Math.round(totalEngagement / posted.length) : 0,
            },
            averageConfidence: responses.length > 0
                ? Math.round((responses.reduce((sum, r) => sum + r.confidence, 0) / responses.length) * 100)
                : 0,
        };
    },
});
/**
 * Get user response statistics for dashboard (alias for getResponseStats)
 */
export const getUserResponseStats = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return {
                totalResponses: 0,
                todayResponses: 0,
                weeklyResponses: 0,
                averageConfidence: 0,
                total: 0,
                today: 0,
                thisWeek: 0,
                byStatus: {
                    draft: 0,
                    approved: 0,
                    posted: 0,
                    declined: 0,
                    failed: 0,
                },
                engagement: {
                    total: 0,
                    average: 0,
                },
            };
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            return {
                totalResponses: 0,
                todayResponses: 0,
                weeklyResponses: 0,
                averageConfidence: 0,
                total: 0,
                today: 0,
                thisWeek: 0,
                byStatus: {
                    draft: 0,
                    approved: 0,
                    posted: 0,
                    declined: 0,
                    failed: 0,
                },
                engagement: {
                    total: 0,
                    average: 0,
                },
            };
        }
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .collect();
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
        const recentResponses = responses.filter(r => r.createdAt >= oneWeekAgo);
        const todayResponses = responses.filter(r => r.createdAt >= oneDayAgo);
        const posted = responses.filter(r => r.status === "posted");
        const totalEngagement = posted.reduce((sum, r) => {
            if (r.actualEngagement) {
                return sum + r.actualEngagement.likes + r.actualEngagement.retweets + r.actualEngagement.replies;
            }
            return sum;
        }, 0);
        const avgConfidence = responses.length > 0
            ? Math.round((responses.reduce((sum, r) => sum + r.confidence, 0) / responses.length) * 100)
            : 0;
        return {
            totalResponses: responses.length,
            todayResponses: todayResponses.length,
            weeklyResponses: recentResponses.length,
            averageConfidence: avgConfidence,
            total: responses.length,
            today: todayResponses.length,
            thisWeek: recentResponses.length,
            byStatus: {
                draft: responses.filter(r => r.status === "draft").length,
                approved: responses.filter(r => r.status === "approved").length,
                posted: posted.length,
                declined: responses.filter(r => r.status === "declined").length,
                failed: responses.filter(r => r.status === "failed").length,
            },
            engagement: {
                total: totalEngagement,
                average: posted.length > 0 ? Math.round(totalEngagement / posted.length) : 0,
            },
        };
    },
});
/**
 * Get style performance analytics for ResponseStyleSelector
 */
export const getStylePerformanceAnalytics = query({
    args: {
        userId: v.id("users"),
    },
    handler: async (ctx, args) => {
        const responses = await ctx.db
            .query("responses")
            .withIndex("by_user", (q) => q.eq("userId", args.userId))
            .collect();
        // Group by style
        const styleStats = {};
        responses.forEach(response => {
            if (!styleStats[response.style]) {
                styleStats[response.style] = {
                    style: response.style,
                    totalResponses: 0,
                    averageQuality: 0,
                    averageEngagement: 0,
                    approvalRate: 0,
                    posted: 0,
                    approved: 0,
                };
            }
            const stats = styleStats[response.style];
            stats.totalResponses++;
            stats.averageQuality += response.confidence || 0;
            if (response.status === "approved" || response.status === "posted") {
                stats.approved++;
            }
            if (response.status === "posted") {
                stats.posted++;
                if (response.actualEngagement) {
                    stats.averageEngagement += response.actualEngagement.likes +
                        response.actualEngagement.retweets + response.actualEngagement.replies;
                }
            }
        });
        // Calculate averages and rates
        const result = Object.values(styleStats).map(stats => ({
            style: stats.style,
            totalResponses: stats.totalResponses,
            averageQuality: stats.totalResponses > 0 ? stats.averageQuality / stats.totalResponses : 0,
            averageEngagement: stats.posted > 0 ? stats.averageEngagement / stats.posted : 0,
            approvalRate: stats.totalResponses > 0 ? stats.approved / stats.totalResponses : 0,
            posted: stats.posted,
            approved: stats.approved,
        }));
        return result;
    },
});
