/**
 * Common Convex type definitions and utilities
 */

import { v } from "convex/values";
import { Id } from "../_generated/dataModel";

// Common validation schemas
export const jobStatusValidator = v.union(
  v.literal("pending"),
  v.literal("running"), 
  v.literal("completed"),
  v.literal("failed"),
  v.literal("cancelled")
);

export const priorityValidator = v.union(
  v.literal("high"),
  v.literal("medium"),
  v.literal("low")
);

export const mentionTypeValidator = v.union(
  v.literal("mention"),
  v.literal("reply"),
  v.literal("quote")
);

export const searchTypeValidator = v.union(
  v.literal("xai"),
  v.literal("tweetio"),
  v.literal("hybrid")
);

// Common object schemas
export const engagementValidator = v.object({
  likes: v.number(),
  retweets: v.number(),
  replies: v.number(),
  views: v.optional(v.number()),
});

export const jobMetadataValidator = v.object({
  description: v.optional(v.string()),
  startedAt: v.optional(v.number()),
  estimatedCompletion: v.optional(v.number()),
  errorCount: v.optional(v.number()),
  lastErrorMessage: v.optional(v.string()),
});

export const jobResultsValidator = v.object({
  successCount: v.number(),
  errorCount: v.number(),
  outputData: v.optional(v.array(v.string())),
});

export const searchMetadataValidator = v.object({
  responseTime: v.optional(v.number()),
  success: v.optional(v.boolean()),
  insights: v.optional(v.array(v.string())),
  tokensUsed: v.optional(v.number()),
  model: v.optional(v.string()),
});

// Type definitions for common interfaces
export interface JobStatus {
  jobType: string;
  status: "pending" | "running" | "completed" | "failed" | "cancelled";
  userId?: Id<"users">;
  progress: number;
  totalItems?: number;
  processedItems?: number;
  metadata?: {
    description?: string;
    startedAt?: number;
    estimatedCompletion?: number;
    errorCount?: number;
    lastErrorMessage?: string;
  };
  results?: {
    successCount: number;
    errorCount: number;
    outputData?: string[];
  };
  createdAt: number;
  updatedAt: number;
  completedAt?: number;
}

export interface SearchResult {
  query: string;
  searchType: "xai" | "tweetio" | "hybrid";
  content: string;
  citations: string[];
  metadata?: {
    responseTime?: number;
    success?: boolean;
    insights?: string[];
    tokensUsed?: number;
    model?: string;
  };
  userId?: Id<"users">;
  createdAt: number;
}

export interface MentionData {
  mentionTweetId: string;
  mentionContent: string;
  mentionAuthor: string;
  mentionAuthorHandle: string;
  mentionAuthorFollowers?: number;
  mentionAuthorVerified?: boolean;
  monitoredAccountId: Id<"twitterAccounts">;
  mentionType: "mention" | "reply" | "quote";
  originalTweetId?: string;
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  priority: "high" | "medium" | "low";
  url: string;
  createdAt: number;
}

// Utility type for auth context
export interface AuthContext {
  auth: {
    getUserIdentity(): Promise<{ subject: string } | null>;
  };
  db: {
    query(table: string): any;
    insert(table: string, document: any): Promise<Id<any>>;
    patch(id: Id<any>, fields: any): Promise<void>;
  };
}

// Helper function types
export type UserIdResult = Id<"users"> | null;
export type DatabaseQueryResult<T> = T | null;
export type MutationResult<T> = Promise<T>;
export type QueryResult<T> = Promise<T>;