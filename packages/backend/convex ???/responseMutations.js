import { mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
/**
 * Store a generated AI response
 */
export const storeResponse = mutation({
    args: {
        targetType: v.union(v.literal("tweet"), v.literal("mention")),
        targetId: v.union(v.id("tweets"), v.id("mentions")),
        content: v.string(),
        style: v.string(),
        confidence: v.number(),
        generationModel: v.optional(v.string()),
        contextUsed: v.optional(v.array(v.string())),
        responseStrategy: v.optional(v.string()),
        estimatedEngagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
        })),
        generatedImage: v.optional(v.string()),
        imagePrompt: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const responseId = await ctx.db.insert("responses", {
            targetType: args.targetType,
            targetId: args.targetId,
            userId: user._id,
            content: args.content,
            style: args.style,
            characterCount: args.content.length,
            confidence: args.confidence,
            generationModel: args.generationModel,
            contextUsed: args.contextUsed,
            responseStrategy: args.responseStrategy,
            estimatedEngagement: args.estimatedEngagement,
            status: "draft",
            generatedImage: args.generatedImage,
            imagePrompt: args.imagePrompt,
            createdAt: Date.now(),
            updatedAt: Date.now(),
        });
        return await ctx.db.get(responseId);
    },
});
/**
 * Update response status (approve, decline, etc.)
 */
export const updateResponseStatus = mutation({
    args: {
        responseId: v.id("responses"),
        status: v.union(v.literal("draft"), v.literal("approved"), v.literal("declined"), v.literal("posted"), v.literal("failed")),
        userFeedback: v.optional(v.object({
            rating: v.number(),
            notes: v.optional(v.string()),
        })),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        // Verify ownership
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user || response.userId !== user._id) {
            throw new Error("Not authorized to update this response");
        }
        const updateData = {
            status: args.status,
            updatedAt: Date.now(),
        };
        if (args.status === "approved") {
            updateData.approvedAt = Date.now();
        }
        if (args.userFeedback) {
            updateData.userFeedback = args.userFeedback;
        }
        await ctx.db.patch(args.responseId, updateData);
        return await ctx.db.get(args.responseId);
    },
});
/**
 * Edit response content
 */
export const editResponse = mutation({
    args: {
        responseId: v.id("responses"),
        content: v.string(),
        notes: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        // Verify ownership
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user || response.userId !== user._id) {
            throw new Error("Not authorized to edit this response");
        }
        // Create a new draft version
        const version = (await ctx.db
            .query("responseDrafts")
            .withIndex("by_response", (q) => q.eq("responseId", args.responseId))
            .collect()).length + 1;
        await ctx.db.insert("responseDrafts", {
            responseId: args.responseId,
            userId: user._id,
            content: args.content,
            version,
            notes: args.notes,
            createdAt: Date.now(),
        });
        // Update the main response
        await ctx.db.patch(args.responseId, {
            content: args.content,
            characterCount: args.content.length,
            updatedAt: Date.now(),
        });
        return await ctx.db.get(args.responseId);
    },
});
/**
 * Delete a response
 */
export const deleteResponse = mutation({
    args: {
        responseId: v.id("responses"),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        // Verify ownership
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user || response.userId !== user._id) {
            throw new Error("Not authorized to delete this response");
        }
        // Delete all drafts first
        const drafts = await ctx.db
            .query("responseDrafts")
            .withIndex("by_response", (q) => q.eq("responseId", args.responseId))
            .collect();
        for (const draft of drafts) {
            await ctx.db.delete(draft._id);
        }
        // Delete the response
        await ctx.db.delete(args.responseId);
        return { success: true };
    },
});
/**
 * Bulk approve responses
 */
export const bulkApproveResponses = mutation({
    args: {
        responseIds: v.array(v.id("responses")),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const updated = [];
        for (const responseId of args.responseIds) {
            const response = await ctx.db.get(responseId);
            if (response && response.userId === user._id) {
                await ctx.db.patch(responseId, {
                    status: "approved",
                    approvedAt: Date.now(),
                    updatedAt: Date.now(),
                });
                updated.push(responseId);
            }
        }
        return {
            success: true,
            updated: updated.length,
            total: args.responseIds.length,
        };
    },
});
/**
 * Generate multiple response variations
 */
export const generateResponseVariations = action({
    args: {
        originalResponseId: v.id("responses"),
        styles: v.array(v.string()),
        count: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const originalResponse = await ctx.runQuery(api.userQueries.getResponseById, {
            responseId: args.originalResponseId,
        });
        if (!originalResponse) {
            throw new Error("Original response not found");
        }
        // Get the target (tweet or mention) based on type
        let target;
        if (originalResponse.targetType === "tweet") {
            target = await ctx.runQuery(api.userQueries.getTweetById, {
                tweetId: originalResponse.targetId,
            });
        }
        else {
            target = await ctx.runQuery(api.userQueries.getMentionById, {
                mentionId: originalResponse.targetId,
            });
        }
        if (!target) {
            throw new Error("Target tweet/mention not found");
        }
        const variations = [];
        const count = args.count || 1;
        for (const style of args.styles) {
            for (let i = 0; i < count; i++) {
                try {
                    // Generate new response with different style
                    const result = await ctx.runAction(api.responseGeneration.generateSingleResponse, {
                        content: 'content' in target ? target.content : target.mentionContent,
                        responseType: originalResponse.targetType === "mention" ? "mention" : "reply",
                        style,
                        authorInfo: 'authorHandle' in target ? {
                            handle: target.authorHandle,
                            displayName: target.author,
                        } : {
                            handle: target.mentionAuthorHandle,
                            displayName: target.mentionAuthor,
                        },
                    });
                    // Store the variation
                    const variationId = await ctx.runMutation(api.responseMutations.storeResponse, {
                        targetType: originalResponse.targetType,
                        targetId: originalResponse.targetId,
                        content: result.content,
                        style: result.style,
                        confidence: result.confidence,
                        generationModel: result.model,
                        responseStrategy: result.strategy,
                    });
                    variations.push(variationId);
                }
                catch (error) {
                    console.error(`Failed to generate variation with style ${style}:`, error);
                }
            }
        }
        return {
            originalResponseId: args.originalResponseId,
            variations,
            generated: variations.length,
            requested: args.styles.length * count,
        };
    },
});
/**
 * Mark response as posted (when actually posted to Twitter)
 */
export const markResponsePosted = mutation({
    args: {
        responseId: v.id("responses"),
        postedTweetId: v.string(),
        actualEngagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        })),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        // Verify ownership
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user || response.userId !== user._id) {
            throw new Error("Not authorized to update this response");
        }
        await ctx.db.patch(args.responseId, {
            status: "posted",
            postedAt: Date.now(),
            postedTweetId: args.postedTweetId,
            actualEngagement: args.actualEngagement,
            updatedAt: Date.now(),
        });
        return await ctx.db.get(args.responseId);
    },
});
/**
 * Update actual engagement metrics for posted responses
 */
export const updateResponseEngagement = mutation({
    args: {
        responseId: v.id("responses"),
        actualEngagement: v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        }),
    },
    handler: async (ctx, args) => {
        const response = await ctx.db.get(args.responseId);
        if (!response) {
            throw new Error("Response not found");
        }
        if (response.status !== "posted") {
            throw new Error("Response has not been posted yet");
        }
        await ctx.db.patch(args.responseId, {
            actualEngagement: args.actualEngagement,
            updatedAt: Date.now(),
        });
        return await ctx.db.get(args.responseId);
    },
});
/**
 * Set user's preferred response style
 */
export const setUserPreferredStyle = mutation({
    args: {
        userId: v.id("users"),
        preferredStyle: v.string(),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db.get(args.userId);
        if (!user) {
            throw new Error("User not found");
        }
        // Verify ownership
        const currentUser = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!currentUser || currentUser._id !== args.userId) {
            throw new Error("Not authorized to update this user's preferences");
        }
        // Check if user settings exist, create or update
        const existingSettings = await ctx.db
            .query("userSettings")
            .filter((q) => q.eq(q.field("userId"), args.userId))
            .first();
        if (existingSettings) {
            await ctx.db.patch(existingSettings._id, {
                preferredResponseStyle: args.preferredStyle,
                updatedAt: Date.now(),
            });
            return await ctx.db.get(existingSettings._id);
        }
        else {
            const settingsId = await ctx.db.insert("userSettings", {
                userId: args.userId,
                preferredResponseStyle: args.preferredStyle,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            });
            return await ctx.db.get(settingsId);
        }
    },
});
