import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
// Security helper function to get authenticated user
async function getAuthenticatedUser(ctx) {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
        throw new Error("Authentication required");
    }
    const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();
    if (!user) {
        throw new Error("User not found");
    }
    return user;
}
/**
 * Get all todos for authenticated user only
 * 🔐 SECURITY: Users can only access their own todos
 */
export const getAll = query({
    handler: async (ctx) => {
        try {
            // 🔐 SECURITY: Authenticate user
            const user = await getAuthenticatedUser(ctx);
            // Get todos only for the authenticated user
            return await ctx.db
                .query("todos")
                .withIndex("by_user", (q) => q.eq("userId", user._id))
                .collect();
        }
        catch (error) {
            console.error('Error in getAll todos:', error);
            if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("User not found"))) {
                throw error;
            }
            return [];
        }
    },
});
/**
 * Create a new todo for authenticated user
 * 🔐 SECURITY: Users can only create todos for themselves
 */
export const create = mutation({
    args: {
        text: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            // 🔐 SECURITY: Authenticate user
            const user = await getAuthenticatedUser(ctx);
            // 🔐 SECURITY: Input validation
            const text = args.text.trim();
            if (text.length === 0) {
                throw new Error("Todo text cannot be empty");
            }
            if (text.length > 500) {
                throw new Error("Todo text too long (max 500 characters)");
            }
            const newTodoId = await ctx.db.insert("todos", {
                text: text,
                completed: false,
                userId: user._id,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            });
            return await ctx.db.get(newTodoId);
        }
        catch (error) {
            console.error('Error in create todo:', error);
            if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Todo text") || error.message.includes("User not found"))) {
                throw error;
            }
            throw new Error("Failed to create todo");
        }
    },
});
/**
 * Toggle todo completion status for authenticated user's todos only
 * 🔐 SECURITY: Users can only modify their own todos
 */
export const toggle = mutation({
    args: {
        id: v.id("todos"),
        completed: v.boolean(),
    },
    handler: async (ctx, args) => {
        try {
            // 🔐 SECURITY: Authenticate user
            const user = await getAuthenticatedUser(ctx);
            // 🔐 SECURITY: Get todo and verify ownership
            const todo = await ctx.db.get(args.id);
            if (!todo) {
                throw new Error("Todo not found");
            }
            if (todo.userId !== user._id) {
                throw new Error("Access denied: Todo not owned by user");
            }
            await ctx.db.patch(args.id, {
                completed: args.completed,
                updatedAt: Date.now(),
            });
            return { success: true };
        }
        catch (error) {
            console.error('Error in toggle todo:', error);
            if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Todo not found") || error.message.includes("User not found"))) {
                throw error;
            }
            return { success: false };
        }
    },
});
/**
 * Delete todo for authenticated user's todos only
 * 🔐 SECURITY: Users can only delete their own todos
 */
export const deleteTodo = mutation({
    args: {
        id: v.id("todos"),
    },
    handler: async (ctx, args) => {
        try {
            // 🔐 SECURITY: Authenticate user
            const user = await getAuthenticatedUser(ctx);
            // 🔐 SECURITY: Get todo and verify ownership
            const todo = await ctx.db.get(args.id);
            if (!todo) {
                throw new Error("Todo not found");
            }
            if (todo.userId !== user._id) {
                throw new Error("Access denied: Todo not owned by user");
            }
            await ctx.db.delete(args.id);
            return { success: true };
        }
        catch (error) {
            console.error('Error in delete todo:', error);
            if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Todo not found") || error.message.includes("User not found"))) {
                throw error;
            }
            return { success: false };
        }
    },
});
