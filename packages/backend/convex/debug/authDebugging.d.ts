/**
 * 🔍 COMPREHENSIVE AUTHENTICATION DEBUGGING SUITE
 *
 * This file contains detailed debugging functions to diagnose
 * JWT token validation and user identity issues between Clerk and Convex.
 *
 * NOTE: These functions are only active when DEBUG_AUTH is enabled via environment variables.
 */
/**
 * Test basic authentication context and user identity
 */
export declare const testAuthContext: any;
/**
 * Test JWT token validation process
 */
export declare const testJWTValidation: any;
/**
 * Test user creation and synchronization
 */
export declare const testUserSync: any;
/**
 * Test mention queries with authentication
 */
export declare const testAuthenticatedQueries: any;
/**
 * Complete authentication health check
 */
export declare const authHealthCheck: any;
