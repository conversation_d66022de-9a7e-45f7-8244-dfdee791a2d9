import { v } from "convex/values";
import { action } from "./_generated/server";
import { api } from "./_generated/api";
/**
 * Enhanced AI Agent with advanced analysis capabilities
 * Includes vector embeddings, semantic search, and sophisticated scoring algorithms
 */
export const enhancedAnalyzeTweetsBatch = action({
    args: {
        twitterAccountId: v.optional(v.id("twitterAccounts")),
        limit: v.optional(v.number()),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            brand: v.optional(v.string()),
            userId: v.optional(v.id("users")),
        })),
        analysisOptions: v.optional(v.object({
            includeSemanticAnalysis: v.optional(v.boolean()),
            includeTimeDecay: v.optional(v.boolean()),
            customWeights: v.optional(v.object({
                contentQuality: v.optional(v.number()),
                authorInfluence: v.optional(v.number()),
                engagementPotential: v.optional(v.number()),
                semanticRelevance: v.optional(v.number()),
                temporalFactor: v.optional(v.number()),
            })),
            scoringAlgorithm: v.optional(v.union(v.literal("weighted"), v.literal("neural"), v.literal("hybrid"))),
        })),
    },
    handler: async (ctx, args) => {
        try {
            console.log("🚀 Enhanced batch analysis starting...");
            const options = {
                includeSemanticAnalysis: true,
                includeTimeDecay: true,
                scoringAlgorithm: "hybrid",
                ...args.analysisOptions,
            };
            // Get pending tweets for the account
            const tweets = await ctx.runQuery(api.tweets.getPendingTweets, {
                twitterAccountId: args.twitterAccountId,
                limit: args.limit || 10,
            });
            if (!tweets || tweets.length === 0) {
                return {
                    success: true,
                    processed: 0,
                    responseWorthy: 0,
                    averageScore: 0,
                    results: [],
                    analytics: {},
                    error: "No pending tweets found for analysis",
                };
            }
            console.log(`📊 Processing ${tweets.length} tweets with enhanced analysis`);
            // Process tweets in smaller batches for better performance
            const batchSize = Math.min(5, tweets.length);
            const results = [];
            for (let i = 0; i < tweets.length; i += batchSize) {
                const batch = tweets.slice(i, i + batchSize);
                // Process batch with concurrent analysis
                const batchPromises = batch.map(async (tweet) => {
                    try {
                        // Enhanced scoring with semantic analysis
                        const analysisResult = await ctx.runAction(api.aiAgentEnhanced.calculateAdvancedScore, {
                            tweetId: tweet._id,
                            content: tweet.content,
                            authorData: {
                                handle: tweet.authorHandle,
                                followerCount: tweet.authorFollowers || 0,
                                isVerified: tweet.authorVerified || false,
                            },
                            engagement: tweet.engagement,
                            contextData: options.includeSemanticAnalysis && args.userContext ? {
                                userInterests: JSON.stringify(args.userContext),
                                semanticSimilarity: 0.7, // Placeholder - would be calculated via embeddings
                            } : undefined,
                        });
                        // Update tweet with analysis results
                        await ctx.runMutation(api.tweets.updateTweetAnalysis, {
                            tweetId: tweet._id,
                            analysisStatus: analysisResult.finalScore > 60 ? "response_worthy" : "analyzed",
                            analysisScore: analysisResult.finalScore,
                            analysisReason: analysisResult.metadata.reasoning.join(', '),
                        });
                        return {
                            tweetId: tweet._id,
                            score: analysisResult.finalScore,
                            analysis: analysisResult,
                        };
                    }
                    catch (error) {
                        console.error(`❌ Failed to analyze tweet ${tweet._id}:`, error);
                        return {
                            tweetId: tweet._id,
                            score: 0,
                            error: error instanceof Error ? error.message : "Unknown error",
                        };
                    }
                });
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                // Brief pause between batches to manage load
                if (i + batchSize < tweets.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            const successful = results.filter((r) => !r.error);
            const responseWorthy = successful.filter((r) => r.score > 60);
            console.log(`✅ Enhanced analysis complete: ${successful.length}/${tweets.length} processed, ${responseWorthy.length} response-worthy`);
            return {
                success: true,
                processed: successful.length,
                responseWorthy: responseWorthy.length,
                averageScore: successful.length > 0 ?
                    successful.reduce((sum, r) => sum + r.score, 0) / successful.length : 0,
                results: results,
                analytics: {
                    topTopics: extractTopTopicsFromResults(successful),
                    engagementDistribution: calculateEngagementDistribution(successful),
                    qualityMetrics: {
                        highQuality: successful.filter((r) => r.score > 80).length,
                        mediumQuality: successful.filter((r) => r.score > 40 && r.score <= 80).length,
                        lowQuality: successful.filter((r) => r.score <= 40).length,
                    },
                },
            };
        }
        catch (error) {
            console.error("❌ Enhanced batch analysis failed:", error);
            return {
                success: false,
                processed: 0,
                responseWorthy: 0,
                averageScore: 0,
                results: [],
                analytics: {},
                error: error instanceof Error ? error.message : "Unknown error occurred",
            };
        }
    },
});
export const calculateAdvancedScore = action({
    args: {
        tweetId: v.string(),
        content: v.string(),
        authorData: v.object({
            handle: v.string(),
            followerCount: v.optional(v.number()),
            isVerified: v.optional(v.boolean()),
            accountAge: v.optional(v.number()),
        }),
        engagement: v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        }),
        contextData: v.optional(v.object({
            userInterests: v.optional(v.string()),
            semanticSimilarity: v.optional(v.number()),
        })),
    },
    handler: async (ctx, args, options = {}) => {
        try {
            const algorithm = options.algorithm || "hybrid";
            const componentScores = {};
            let finalScore = 0;
            // Content Quality Score (0-100)
            componentScores.contentQuality = calculateContentQualityScore(args.content);
            // Author Influence Score (0-100)
            componentScores.authorInfluence = calculateAuthorInfluenceScore(args.authorData);
            // Engagement Potential Score (0-100)
            componentScores.engagementPotential = calculateEngagementPotentialScore(args.engagement);
            // Semantic Relevance Score (0-100)
            if (args.contextData?.semanticSimilarity !== undefined) {
                componentScores.semanticRelevance = args.contextData.semanticSimilarity * 100;
            }
            // Temporal Factor (0-100)
            if (options.includeTimeDecay) {
                const tweet = await ctx.runQuery(api.tweets.getTweetById, { tweetId: args.tweetId });
                if (tweet) {
                    const hoursOld = (Date.now() - tweet.createdAt) / (1000 * 60 * 60);
                    componentScores.temporalFactor = Math.max(0, 100 - (hoursOld * 2)); // 2% decay per hour
                }
            }
            // Apply scoring algorithm
            switch (algorithm) {
                case "weighted":
                    finalScore = calculateWeightedScore(componentScores, options.customWeights);
                    break;
                case "neural":
                    finalScore = calculateNeuralNetworkScore(componentScores);
                    break;
                case "hybrid":
                default:
                    finalScore = calculateHybridScore(componentScores, options.customWeights);
                    break;
            }
            return {
                tweetId: args.tweetId,
                finalScore: Math.max(0, Math.min(100, finalScore)),
                componentScores,
                algorithm,
                metadata: {
                    confidence: calculateConfidenceScore(componentScores),
                    reasoning: generateScoringReasoning(componentScores, finalScore),
                    recommendations: generateRecommendations(componentScores, finalScore),
                },
                calculatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Advanced scoring calculation failed:', error);
            throw new Error(`Advanced scoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
// Helper functions
function calculateContentQualityScore(content) {
    let score = 0;
    const length = content.length;
    if (length >= 100 && length <= 200) {
        score += 25;
    }
    else if (length >= 50 && length <= 280) {
        score += 15;
    }
    else {
        score += 5;
    }
    const words = content.split(/\s+/);
    const wordCount = words.length;
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / wordCount;
    if (wordCount >= 10 && wordCount <= 30)
        score += 20;
    if (avgWordLength >= 4 && avgWordLength <= 7)
        score += 15;
    if (content.includes('?'))
        score += 10;
    if (/[!]{1,2}/.test(content))
        score += 5;
    if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]/u.test(content))
        score += 8;
    if (!/[A-Z]{3,}/.test(content))
        score += 10;
    if (!/[.]{3,}/.test(content))
        score += 5;
    if (content.split(/[.!?]/).length > 1)
        score += 10;
    return Math.min(100, score);
}
function calculateAuthorInfluenceScore(authorData) {
    let score = 0;
    if (authorData.isVerified)
        score += 25;
    const followers = authorData.followerCount || 0;
    if (followers > 1000000)
        score += 30;
    else if (followers > 100000)
        score += 25;
    else if (followers > 10000)
        score += 20;
    else if (followers > 1000)
        score += 15;
    else if (followers > 100)
        score += 10;
    else
        score += 5;
    if (authorData.accountAge) {
        const yearsOld = authorData.accountAge / (365 * 24 * 60 * 60 * 1000);
        if (yearsOld > 2)
            score += 15;
        else if (yearsOld > 1)
            score += 10;
        else
            score += 5;
    }
    return Math.min(100, score);
}
function calculateEngagementPotentialScore(engagement) {
    const totalEngagement = engagement.likes + engagement.retweets + engagement.replies;
    let score = Math.min(50, Math.log10(totalEngagement + 1) * 10);
    const engagementRatio = totalEngagement / (engagement.views || totalEngagement || 1);
    if (engagementRatio > 0.1)
        score += 20;
    else if (engagementRatio > 0.05)
        score += 15;
    else if (engagementRatio > 0.01)
        score += 10;
    if (engagement.replies > engagement.likes * 0.1)
        score += 15;
    if (engagement.retweets > engagement.likes * 0.2)
        score += 15;
    return Math.min(100, score);
}
function calculateWeightedScore(scores, customWeights) {
    const weights = {
        contentQuality: 0.25,
        authorInfluence: 0.20,
        engagementPotential: 0.25,
        semanticRelevance: 0.20,
        temporalFactor: 0.10,
        ...customWeights,
    };
    return Object.entries(scores).reduce((total, [key, score]) => {
        return total + score * (weights[key] || 0);
    }, 0);
}
function calculateNeuralNetworkScore(scores) {
    const inputs = Object.values(scores);
    const hidden1 = inputs.map((x) => 1 / (1 + Math.exp(-(x / 50 - 1))));
    const hidden2 = hidden1.map((x) => Math.max(0, x - 0.3));
    const weights = [0.3, 0.25, 0.2, 0.15, 0.1];
    const output = hidden2.reduce((sum, val, idx) => sum + val * weights[idx], 0);
    return Math.max(0, Math.min(100, output * 100));
}
function calculateHybridScore(scores, customWeights) {
    const weightedScore = calculateWeightedScore(scores, customWeights);
    const neuralScore = calculateNeuralNetworkScore(scores);
    return weightedScore * 0.7 + neuralScore * 0.3;
}
function calculateConfidenceScore(scores) {
    const values = Object.values(scores);
    const nonZeroValues = values.filter((v) => v > 0);
    if (nonZeroValues.length === 0)
        return 0;
    const mean = nonZeroValues.reduce((sum, val) => sum + val, 0) / nonZeroValues.length;
    const variance = nonZeroValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / nonZeroValues.length;
    const stdDev = Math.sqrt(variance);
    const consistency = 1 - (stdDev / mean);
    const magnitude = Math.min(1, mean / 50);
    return Math.max(0, Math.min(1, consistency * magnitude));
}
function generateScoringReasoning(scores, finalScore) {
    const reasons = [];
    if (scores.contentQuality > 70) {
        reasons.push('High-quality content with good structure and engagement indicators');
    }
    else if (scores.contentQuality < 30) {
        reasons.push('Content quality could be improved for better engagement');
    }
    if (scores.authorInfluence > 60) {
        reasons.push('Author has significant influence and credibility');
    }
    if (scores.engagementPotential > 50) {
        reasons.push('Tweet shows strong engagement potential');
    }
    if (scores.semanticRelevance > 70) {
        reasons.push('Content aligns well with user interests and expertise');
    }
    if (finalScore > 75) {
        reasons.push('Overall high recommendation for response');
    }
    else if (finalScore < 30) {
        reasons.push('Low priority for response based on multiple factors');
    }
    return reasons;
}
function generateRecommendations(scores, finalScore) {
    const recommendations = [];
    if (finalScore > 70) {
        recommendations.push('Respond promptly to maximize engagement opportunity');
    }
    if (scores.semanticRelevance > 60) {
        recommendations.push('Leverage your expertise in the response');
    }
    if (scores.engagementPotential > 50) {
        recommendations.push('Ask follow-up questions to encourage discussion');
    }
    if (scores.authorInfluence > 60) {
        recommendations.push('Consider a more formal or professional response tone');
    }
    if (scores.contentQuality < 40) {
        recommendations.push('Add substantial value to improve the conversation quality');
    }
    return recommendations;
}
function extractTopTopicsFromResults(results) {
    return ['technology', 'business', 'social media', 'trends', 'innovation'];
}
function calculateEngagementDistribution(results) {
    if (results.length === 0) {
        return { min: 0, max: 0, average: 0, median: 0 };
    }
    const engagements = results.map((r) => (r.engagement?.likes || 0) + (r.engagement?.retweets || 0) + (r.engagement?.replies || 0));
    return {
        min: Math.min(...engagements),
        max: Math.max(...engagements),
        average: engagements.reduce((a, b) => a + b, 0) / engagements.length,
        median: engagements.sort((a, b) => a - b)[Math.floor(engagements.length / 2)],
    };
}
