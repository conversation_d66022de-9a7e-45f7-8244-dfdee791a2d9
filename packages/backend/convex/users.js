import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
export const getCurrentUser = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return null;
        }
        // Look up user in database by Clerk ID
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        return user;
    },
});
export const createOrUpdateUser = mutation({
    args: {
        name: v.optional(v.string()),
        email: v.optional(v.string()),
        clerkId: v.optional(v.string()),
        image: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const clerkId = identity.subject;
        const name = args.name || identity.name || "User";
        const email = args.email || identity.email || "<EMAIL>";
        const image = args.image || identity.pictureUrl || null;
        // Check if user already exists
        const existingUser = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", clerkId))
            .first();
        if (existingUser) {
            // Update existing user
            await ctx.db.patch(existingUser._id, {
                name,
                email,
                image: image || undefined,
            });
            return await ctx.db.get(existingUser._id);
        }
        else {
            // Create new user
            const userId = await ctx.db.insert("users", {
                name,
                email,
                clerkId,
                image: image || undefined,
                createdAt: Date.now(),
            });
            return await ctx.db.get(userId);
        }
    },
});
export const updateUser = mutation({
    args: {
        userId: v.id("users"),
        updates: v.object({
            lastMentionRefresh: v.optional(v.number()),
            updatedAt: v.optional(v.number()),
        }),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        // Verify user owns this record
        const user = await ctx.db.get(args.userId);
        if (!user || user.clerkId !== identity.subject) {
            throw new Error("Unauthorized");
        }
        // Update user with timestamp
        await ctx.db.patch(args.userId, {
            ...args.updates,
            updatedAt: Date.now(),
        });
        return await ctx.db.get(args.userId);
    },
});
export const getUserTwitterAccounts = query({
    handler: async (ctx) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return [];
        }
        // Get user first
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            return [];
        }
        // Get user's Twitter accounts
        return await ctx.db
            .query("twitterAccounts")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .collect();
    },
});
