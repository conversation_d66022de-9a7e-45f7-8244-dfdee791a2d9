export declare const addTwitterAccount: import("convex/server").RegisteredMutation<"public", {
    displayName?: string | undefined;
    isActive?: boolean | undefined;
    isMonitoringEnabled?: boolean | undefined;
    handle: string;
}, Promise<{
    _id: import("convex/values").GenericId<"twitterAccounts">;
    _creationTime: number;
    updatedAt?: number | undefined;
    isMonitoringEnabled?: boolean | undefined;
    lastScrapedAt?: number | undefined;
    createdAt: number;
    userId: import("convex/values").GenericId<"users">;
    handle: string;
    displayName: string;
    isActive: boolean;
} | null>>;
export declare const removeTwitterAccount: import("convex/server").RegisteredMutation<"public", {
    accountId: import("convex/values").GenericId<"twitterAccounts">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Get active Twitter accounts for workflows
 */
export declare const getActiveAccounts: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"twitterAccounts">;
    _creationTime: number;
    updatedAt?: number | undefined;
    isMonitoringEnabled?: boolean | undefined;
    lastScrapedAt?: number | undefined;
    createdAt: number;
    userId: import("convex/values").GenericId<"users">;
    handle: string;
    displayName: string;
    isActive: boolean;
}[]>>;
/**
 * Update last scraped timestamp for an account
 */
export declare const updateLastScraped: import("convex/server").RegisteredMutation<"public", {
    accountId: import("convex/values").GenericId<"twitterAccounts">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Add Twitter account with bulk mention import
 */
export declare const addAccountWithBulkImport: import("convex/server").RegisteredMutation<"public", {
    displayName?: string | undefined;
    isActive?: boolean | undefined;
    isMonitoringEnabled?: boolean | undefined;
    handle: string;
    bulkImportConfig: {
        enableViralDetection?: boolean | undefined;
        priorityMode?: boolean | undefined;
        maxMentions: number;
        timeframeDays: number;
    };
}, any>;
