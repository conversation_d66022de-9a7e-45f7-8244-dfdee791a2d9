import { action } from "./_generated/server";
import { v } from "convex/values";
import { getOpenRouterClient } from "./lib/openrouter_client";
import { ANALYSIS_TEMPLATES, ENHANCED_ANALYSIS_TEMPLATES, buildPromptContext } from "./lib/prompt_templates";
import { api } from "./_generated/api";
import { averageEmbeddings, preprocessTextForEmbedding, calculateCosineSimilarity, calculateEnhancedWorthinessScore, calculateBatchInsights } from "./lib/embeddingUtils";
import { validateAnalysisStructure, createFallbackAnalysis } from "./lib/analysisUtils";
/**
 * Enhanced batch tweet analysis with improved algorithms and vector embedding support
 */
export const analyzeTweetsBatchEnhanced = action({
    args: {
        tweets: v.array(v.object({
            id: v.string(),
            content: v.string(),
            author: v.string(),
            authorHandle: v.optional(v.string()),
            authorIsVerified: v.optional(v.boolean()),
            authorFollowerCount: v.optional(v.number()),
            engagement: v.optional(v.object({
                likes: v.number(),
                retweets: v.number(),
                replies: v.number(),
                views: v.optional(v.number()),
            })),
            createdAt: v.optional(v.number()),
        })),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            brand: v.optional(v.string()),
            userId: v.optional(v.id("users")),
        })),
        options: v.optional(v.object({
            includeEmbeddings: v.optional(v.boolean()),
            enhancedScoring: v.optional(v.boolean()),
            batchSize: v.optional(v.number()),
            useSemanticContext: v.optional(v.boolean()),
        })),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const results = [];
            let analyzed = 0;
            const options = args.options || {};
            const batchSize = options.batchSize || 8; // Optimized batch size
            // Pre-generate embeddings for semantic context if enabled
            let contextEmbeddings = null;
            if (options.useSemanticContext && args.userContext?.userId) {
                try {
                    // Embeddings disabled for now - module not properly exported
                    console.log('Semantic context disabled - embeddings module not available');
                }
                catch (contextError) {
                    console.warn('Failed to get user context embeddings:', contextError);
                }
            }
            // Process tweets in optimized batches
            for (let i = 0; i < args.tweets.length; i += batchSize) {
                const batch = args.tweets.slice(i, i + batchSize);
                // Generate embeddings for semantic analysis if requested
                let batchEmbeddings = [];
                if (options.includeEmbeddings) {
                    try {
                        const embeddingTexts = batch.map(tweet => preprocessTextForEmbedding(tweet.content));
                        const embeddingResponses = await client.generateEmbeddingsBatch(embeddingTexts, 'text-embedding-3-small');
                        batchEmbeddings = embeddingResponses.map(r => r.embedding);
                    }
                    catch (embError) {
                        console.warn('Failed to generate embeddings for batch:', embError);
                        batchEmbeddings = new Array(batch.length).fill(null);
                    }
                }
                // Process batch with enhanced analysis
                const batchPromises = batch.map(async (tweet, batchIndex) => {
                    try {
                        const tweetEmbedding = batchEmbeddings[batchIndex];
                        // Calculate semantic similarity if context available
                        let semanticScore = 0.5; // Default neutral score
                        if (contextEmbeddings && tweetEmbedding) {
                            semanticScore = calculateCosineSimilarity(contextEmbeddings, tweetEmbedding);
                        }
                        // Enhanced scoring algorithm
                        let enhancedScore = 0;
                        if (options.enhancedScoring) {
                            enhancedScore = calculateEnhancedWorthinessScore({
                                content: tweet.content,
                                authorIsVerified: tweet.authorIsVerified,
                                authorFollowerCount: tweet.authorFollowerCount,
                                engagement: tweet.engagement,
                                semanticScore,
                                recency: tweet.createdAt ? (Date.now() - tweet.createdAt) / (1000 * 60 * 60) : 1,
                            });
                        }
                        const promptContext = buildPromptContext(tweet.content, {
                            authorInfo: {
                                handle: tweet.authorHandle || tweet.author,
                                displayName: tweet.author,
                                isVerified: tweet.authorIsVerified,
                                followerCount: tweet.authorFollowerCount,
                            },
                            tweetMetadata: tweet.engagement ? {
                                engagement: tweet.engagement,
                                createdAt: tweet.createdAt || Date.now(),
                            } : undefined,
                            userContext: args.userContext,
                        });
                        // Use enhanced analysis template
                        const template = ENHANCED_ANALYSIS_TEMPLATES.comprehensiveWorthiness;
                        const response = await client.generateCompletion(template.userPrompt({
                            ...promptContext,
                            semanticScore,
                            enhancedScore,
                        }), {
                            systemPrompt: template.systemPrompt,
                            maxTokens: template.maxTokens,
                            temperature: template.temperature,
                        });
                        // Parse and validate JSON response
                        let analysis;
                        try {
                            analysis = JSON.parse(response.content);
                            analysis = validateAnalysisStructure(analysis);
                            // Enhance with calculated scores
                            if (options.enhancedScoring) {
                                analysis.enhancedScore = enhancedScore;
                                analysis.semanticRelevance = semanticScore;
                            }
                        }
                        catch (parseError) {
                            console.error('Failed to parse tweet analysis JSON:', response.content);
                            analysis = createFallbackAnalysis(tweet, enhancedScore, semanticScore);
                        }
                        return {
                            tweetId: tweet.id,
                            analysis,
                            embedding: tweetEmbedding,
                            model: response.model,
                            semanticScore,
                            enhancedScore: options.enhancedScoring ? enhancedScore : undefined,
                            success: true,
                        };
                    }
                    catch (error) {
                        console.error(`Failed to analyze tweet ${tweet.id}:`, error);
                        return {
                            tweetId: tweet.id,
                            analysis: null,
                            error: error instanceof Error ? error.message : 'Unknown error',
                            success: false,
                        };
                    }
                });
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                analyzed += batchResults.filter(r => r.success).length;
                // Adaptive delay based on API performance
                if (i + batchSize < args.tweets.length) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            // Calculate batch insights
            const successfulResults = results.filter(r => r.success);
            const batchInsights = calculateBatchInsights(successfulResults);
            return {
                analyzed,
                total: args.tweets.length,
                results,
                insights: batchInsights,
                analyzedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Enhanced batch tweet analysis failed:', error);
            throw new Error(`Enhanced batch analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Legacy batch analysis for backward compatibility
 */
export const analyzeTweetsBatch = action({
    args: {
        tweets: v.array(v.object({
            id: v.string(),
            content: v.string(),
            author: v.string(),
            authorHandle: v.optional(v.string()),
            authorIsVerified: v.optional(v.boolean()),
            authorFollowerCount: v.optional(v.number()),
            engagement: v.optional(v.object({
                likes: v.number(),
                retweets: v.number(),
                replies: v.number(),
                views: v.optional(v.number()),
            })),
        })),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            brand: v.optional(v.string()),
        })),
    },
    handler: async (ctx, args) => {
        // Delegate to enhanced version with legacy options
        return await ctx.runAction(api.aiAgent.analyzeTweetsBatchEnhanced, {
            tweets: args.tweets,
            userContext: args.userContext,
            options: {
                includeEmbeddings: false,
                enhancedScoring: false,
                batchSize: 5,
                useSemanticContext: false,
            },
        });
    },
});
/**
 * Enhanced comprehensive analysis for a single tweet with vector embeddings
 */
export const analyzeSingleTweetEnhanced = action({
    args: {
        tweetId: v.string(),
        content: v.string(),
        author: v.string(),
        authorHandle: v.optional(v.string()),
        authorIsVerified: v.optional(v.boolean()),
        authorFollowerCount: v.optional(v.number()),
        engagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        })),
        createdAt: v.optional(v.number()),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            brand: v.optional(v.string()),
            userId: v.optional(v.id("users")),
        })),
        options: v.optional(v.object({
            includeEmbedding: v.optional(v.boolean()),
            findSimilarContent: v.optional(v.boolean()),
            enhancedScoring: v.optional(v.boolean()),
            generateSemanticInsights: v.optional(v.boolean()),
        })),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const options = args.options || {};
            // Generate embedding for semantic analysis
            let tweetEmbedding = null;
            if (options.includeEmbedding || options.findSimilarContent) {
                try {
                    const embeddingResponse = await client.generateEmbedding(preprocessTextForEmbedding(args.content), 'text-embedding-3-small');
                    tweetEmbedding = embeddingResponse.embedding;
                }
                catch (embError) {
                    console.warn('Failed to generate embedding:', embError);
                }
            }
            // Find similar content for context
            let similarContent = [];
            if (options.findSimilarContent && tweetEmbedding) {
                try {
                    // Use a direct query instead of the non-existent function
                    similarContent = []; // Placeholder for now
                }
                catch (similarError) {
                    console.warn('Failed to find similar content:', similarError);
                }
            }
            // Get user semantic context
            let userSemanticContext = [];
            if (args.userContext?.userId) {
                try {
                    // Embeddings disabled for now - module not properly exported
                    console.log('User semantic context disabled - embeddings module not available');
                }
                catch (contextError) {
                    console.warn('Failed to get user context:', contextError);
                }
            }
            // Calculate enhanced scoring
            let enhancedScore = 0;
            let semanticRelevance = 0.5;
            if (options.enhancedScoring) {
                if (userSemanticContext.length > 0 && tweetEmbedding) {
                    const contextEmbeddings = userSemanticContext.map(ctx => ctx.embedding);
                    const avgContextEmbedding = averageEmbeddings(contextEmbeddings);
                    semanticRelevance = calculateCosineSimilarity(avgContextEmbedding, tweetEmbedding);
                }
                enhancedScore = calculateEnhancedWorthinessScore({
                    content: args.content,
                    authorIsVerified: args.authorIsVerified,
                    authorFollowerCount: args.authorFollowerCount,
                    engagement: args.engagement,
                    semanticScore: semanticRelevance,
                    recency: args.createdAt ? (Date.now() - args.createdAt) / (1000 * 60 * 60) : 1,
                });
            }
            const promptContext = buildPromptContext(args.content, {
                authorInfo: {
                    handle: args.authorHandle || args.author,
                    displayName: args.author,
                    isVerified: args.authorIsVerified,
                    followerCount: args.authorFollowerCount,
                },
                tweetMetadata: args.engagement ? {
                    engagement: args.engagement,
                    createdAt: args.createdAt || Date.now(),
                } : undefined,
                userContext: args.userContext,
            });
            // Enhanced analysis with semantic context
            const enhancedPromptContext = {
                ...promptContext,
                semanticContext: {
                    similarContent: similarContent.slice(0, 3),
                    userInterests: userSemanticContext.map(ctx => ctx.content),
                    relevanceScore: semanticRelevance,
                },
                enhancedScore,
            };
            // Run comprehensive analysis using the available template
            const [worthinessResult, sentimentResult, topicsResult, semanticResult] = await Promise.allSettled([
                // Enhanced worthiness analysis
                client.generateCompletion(ENHANCED_ANALYSIS_TEMPLATES.comprehensiveWorthiness.userPrompt(enhancedPromptContext), {
                    systemPrompt: ENHANCED_ANALYSIS_TEMPLATES.comprehensiveWorthiness.systemPrompt,
                    maxTokens: ENHANCED_ANALYSIS_TEMPLATES.comprehensiveWorthiness.maxTokens,
                    temperature: ENHANCED_ANALYSIS_TEMPLATES.comprehensiveWorthiness.temperature,
                }),
                // Use basic templates for sentiment and topics
                client.generateCompletion(ANALYSIS_TEMPLATES.sentimentAnalysis.userPrompt(enhancedPromptContext), {
                    systemPrompt: ANALYSIS_TEMPLATES.sentimentAnalysis.systemPrompt,
                    maxTokens: ANALYSIS_TEMPLATES.sentimentAnalysis.maxTokens,
                    temperature: ANALYSIS_TEMPLATES.sentimentAnalysis.temperature,
                }),
                client.generateCompletion(ANALYSIS_TEMPLATES.topicExtraction.userPrompt(enhancedPromptContext), {
                    systemPrompt: ANALYSIS_TEMPLATES.topicExtraction.systemPrompt,
                    maxTokens: ANALYSIS_TEMPLATES.topicExtraction.maxTokens,
                    temperature: ANALYSIS_TEMPLATES.topicExtraction.temperature,
                }),
                // Skip semantic insights for now
                Promise.resolve({ content: JSON.stringify({ insights: [] }) }),
            ]);
            // Parse and validate results
            const parsedResults = {
                worthiness: null,
                sentiment: null,
                topics: null,
                semanticInsights: null,
            };
            if (worthinessResult.status === 'fulfilled') {
                try {
                    parsedResults.worthiness = validateAnalysisStructure(JSON.parse(worthinessResult.value.content));
                    if (options.enhancedScoring) {
                        parsedResults.worthiness.enhancedScore = enhancedScore;
                        parsedResults.worthiness.semanticRelevance = semanticRelevance;
                    }
                }
                catch (e) {
                    console.error('Failed to parse worthiness analysis:', e);
                    parsedResults.worthiness = createFallbackAnalysis(args, enhancedScore, semanticRelevance);
                }
            }
            if (sentimentResult.status === 'fulfilled') {
                try {
                    parsedResults.sentiment = JSON.parse(sentimentResult.value.content);
                }
                catch (e) {
                    console.error('Failed to parse sentiment analysis:', e);
                    parsedResults.sentiment = { sentiment: 'neutral', confidence: 0.5 };
                }
            }
            if (topicsResult.status === 'fulfilled') {
                try {
                    parsedResults.topics = JSON.parse(topicsResult.value.content);
                }
                catch (e) {
                    console.error('Failed to parse topics analysis:', e);
                    parsedResults.topics = { mainTopics: ['general'], complexity: 'simple' };
                }
            }
            if (semanticResult.status === 'fulfilled') {
                try {
                    parsedResults.semanticInsights = JSON.parse(semanticResult.value.content);
                }
                catch (e) {
                    console.error('Failed to parse semantic insights:', e);
                }
            }
            return {
                tweetId: args.tweetId,
                analysis: parsedResults,
                embedding: tweetEmbedding,
                similarContent: similarContent.slice(0, 3),
                semanticRelevance,
                enhancedScore: options.enhancedScoring ? enhancedScore : undefined,
                analyzedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Enhanced single tweet analysis failed:', error);
            throw new Error(`Enhanced single tweet analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Legacy single tweet analysis for backward compatibility
 */
export const analyzeSingleTweet = action({
    args: {
        tweetId: v.string(),
        content: v.string(),
        author: v.string(),
        authorHandle: v.optional(v.string()),
        authorIsVerified: v.optional(v.boolean()),
        authorFollowerCount: v.optional(v.number()),
        engagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        })),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            brand: v.optional(v.string()),
        })),
    },
    handler: async (ctx, args) => {
        // Delegate to enhanced version with legacy options
        const result = await ctx.runAction(api.aiAgent.analyzeSingleTweetEnhanced, {
            ...args,
            options: {
                includeEmbedding: false,
                findSimilarContent: false,
                enhancedScoring: false,
                generateSemanticInsights: false,
            },
        });
        return {
            tweetId: result.tweetId,
            analysis: result.analysis,
            analyzedAt: result.analyzedAt,
        };
    },
});
