export declare const updateMentionProcessingStatus: import("convex/server").RegisteredMutation<"public", {
    aiAnalysisResult?: {
        responseStrategy?: string | undefined;
        sentiment?: string | undefined;
        topics?: string[] | undefined;
        confidence?: number | undefined;
        shouldRespond: boolean;
    } | undefined;
    isProcessed: boolean;
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
}>>;
export declare const markNotificationSent: import("convex/server").RegisteredMutation<"public", {
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Simple action for users to fetch their latest mentions from Twitter
 * This function fetches real mentions using TwitterAPI.io
 */
export declare const fetchUserMentions: any;
/**
 * OPTIMIZED: Refresh mentions for monitored accounts with real-time performance
 * Enhanced with intelligent caching, parallel processing, and smart batching
 */
export declare const refreshMentions: any;
export declare const updateMentionPriority: import("convex/server").RegisteredMutation<"public", {
    priority: "high" | "medium" | "low";
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
}>>;
export declare const updateMonitoringSettings: import("convex/server").RegisteredMutation<"public", {
    isMonitoringEnabled?: boolean | undefined;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
}, Promise<{
    success: boolean;
}>>;
export declare const bulkUpdateMentionNotifications: import("convex/server").RegisteredMutation<"public", {
    isNotificationSent: boolean;
    mentionIds: import("convex/values").GenericId<"mentions">[];
}, Promise<{
    success: boolean;
    updated: number;
}>>;
export declare const markAllMentionsAsRead: import("convex/server").RegisteredMutation<"public", {
    monitoredAccountId?: import("convex/values").GenericId<"twitterAccounts"> | undefined;
}, Promise<{
    success: boolean;
    updated: number;
}>>;
export declare const deleteMention: import("convex/server").RegisteredMutation<"public", {
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
}>>;
export declare const storeMention: import("convex/server").RegisteredMutation<"public", {
    url?: string | undefined;
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    priority: "high" | "medium" | "low";
}, Promise<import("convex/values").GenericId<"mentions">>>;
export declare const storeMentionWithCheck: import("convex/server").RegisteredMutation<"public", {
    url?: string | undefined;
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    forceUpdate?: boolean | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    priority: "high" | "medium" | "low";
}, Promise<{
    mentionId: import("convex/values").GenericId<"mentions">;
    action: string;
    changes: {
        engagement: boolean;
        priority: boolean;
        created?: undefined;
    };
} | {
    mentionId: import("convex/values").GenericId<"mentions">;
    action: string;
    changes: {
        engagement?: undefined;
        priority?: undefined;
        created?: undefined;
    };
} | {
    mentionId: import("convex/values").GenericId<"mentions">;
    action: string;
    changes: {
        created: boolean;
        engagement?: undefined;
        priority?: undefined;
    };
}>>;
export declare const updateMentionEngagement: import("convex/server").RegisteredMutation<"public", {
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
}>>;
export declare const processMentionWithAI: import("convex/server").RegisteredAction<"public", {
    userContext?: {
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    skipIfProcessed?: boolean | undefined;
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    success: boolean;
    error: string;
    message?: undefined;
    analysis?: undefined;
    model?: undefined;
} | {
    success: boolean;
    message: string;
    error?: undefined;
    analysis?: undefined;
    model?: undefined;
} | {
    success: boolean;
    analysis: {
        urgency: any;
        reasoning: any;
        riskLevel: any;
        suggestedTone: any;
        shouldRespond: any;
        responseStrategy: any;
        sentiment: any;
        topics: any;
        confidence: any;
    };
    model: string;
    error?: undefined;
    message?: undefined;
}>>;
export declare const bulkProcessMentions: any;
/**
 * Generate AI response to a mention
 */
export declare const generateMentionResponse: any;
/**
 * Generate multiple response options for a mention
 */
export declare const generateMentionResponseOptions: any;
/**
 * Automated mention monitoring action (for scheduled execution)
 * This can be called by cron jobs or scheduled tasks to continuously monitor mentions
 * Enhanced with intelligent scheduling and priority processing
 */
export declare const automatedMentionMonitoring: any;
/**
 * PHASE 2: Enhanced mention refresh with API lookup capabilities
 * Uses the new user lookup functionality to resolve missing author data
 */
export declare const refreshMentionsEnhanced: any;
/**
 * Simple one-click mention refresh for users
 * Optimized for quick frontend calls with immediate feedback
 */
export declare const quickMentionRefresh: any;
/**
 * Process sentiment analysis for a mention
 * Automatically called when new mentions are created
 */
export declare const processMentionSentiment: any;
/**
 * Batch process sentiment analysis for all unanalyzed mentions
 * Used for retroactive sentiment analysis
 */
export declare const batchProcessSentimentAnalysis: any;
