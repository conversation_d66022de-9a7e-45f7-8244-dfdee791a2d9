import { query } from "../_generated/server";
import { v } from "convex/values";
/**
 * Queries for sentiment analysis data
 */
/**
 * Get sentiment analytics for a specific account
 */
export const getAccountSentimentAnalytics = query({
    args: {
        accountId: v.id("twitterAccounts"),
        timeRange: v.optional(v.union(v.literal("24h"), v.literal("7d"), v.literal("30d"))),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "24h";
        const now = Date.now();
        const startTime = now - (timeRange === "24h" ? 24 * 60 * 60 * 1000 :
            timeRange === "7d" ? 7 * 24 * 60 * 60 * 1000 :
                30 * 24 * 60 * 60 * 1000);
        // Get mentions for this account within the time range
        const mentions = await ctx.db
            .query("mentions")
            .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.accountId))
            .filter((q) => q.gte(q.field("createdAt"), startTime))
            .collect();
        // Filter mentions that have sentiment analysis
        const withSentiment = mentions.filter(m => m.sentimentAnalysis);
        if (withSentiment.length === 0) {
            return {
                totalMentions: mentions.length,
                analyzedMentions: 0,
                sentimentBreakdown: {
                    bullish: 0,
                    bearish: 0,
                    neutral: 0,
                },
                averageScore: 50,
                confidence: 0,
                topKeywords: [],
                sentimentTrend: [],
            };
        }
        // Calculate sentiment breakdown
        const sentimentCounts = {
            bullish: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "bullish").length,
            bearish: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "bearish").length,
            neutral: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "neutral").length,
        };
        // Calculate average sentiment score
        const averageScore = withSentiment.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / withSentiment.length;
        // Calculate average confidence
        const averageConfidence = withSentiment.reduce((sum, m) => sum + (m.sentimentAnalysis?.confidence || 0), 0) / withSentiment.length;
        // Get top keywords
        const allKeywords = [];
        withSentiment.forEach(m => {
            if (m.sentimentAnalysis?.keyWords) {
                allKeywords.push(...m.sentimentAnalysis.keyWords);
            }
        });
        const keywordCounts = {};
        allKeywords.forEach(keyword => {
            keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
        });
        const topKeywords = Object.entries(keywordCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([keyword, count]) => ({ keyword, count }));
        // Calculate sentiment trend (daily aggregation)
        const sentimentTrend = calculateSentimentTrend(withSentiment, timeRange);
        return {
            totalMentions: mentions.length,
            analyzedMentions: withSentiment.length,
            sentimentBreakdown: sentimentCounts,
            averageScore: Math.round(averageScore),
            confidence: Math.round(averageConfidence * 100) / 100,
            topKeywords,
            sentimentTrend,
        };
    },
});
/**
 * Get overall sentiment statistics for all accounts
 */
export const getOverallSentimentStats = query({
    args: {
        timeRange: v.optional(v.union(v.literal("24h"), v.literal("7d"), v.literal("30d"))),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "24h";
        const now = Date.now();
        const startTime = now - (timeRange === "24h" ? 24 * 60 * 60 * 1000 :
            timeRange === "7d" ? 7 * 24 * 60 * 60 * 1000 :
                30 * 24 * 60 * 60 * 1000);
        // Get all mentions within time range with sentiment analysis
        const mentions = await ctx.db
            .query("mentions")
            .withIndex("by_discovered_at")
            .filter((q) => q.gte(q.field("discoveredAt"), startTime))
            .collect();
        const withSentiment = mentions.filter(m => m.sentimentAnalysis);
        if (withSentiment.length === 0) {
            return {
                totalMentions: mentions.length,
                analyzedMentions: 0,
                sentimentBreakdown: { bullish: 0, bearish: 0, neutral: 0 },
                averageScore: 50,
                highConfidenceCount: 0,
                mostBullishMention: null,
                mostBearishMention: null,
            };
        }
        // Calculate sentiment breakdown
        const sentimentCounts = {
            bullish: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "bullish").length,
            bearish: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "bearish").length,
            neutral: withSentiment.filter(m => m.sentimentAnalysis?.sentiment === "neutral").length,
        };
        // Calculate average score
        const averageScore = withSentiment.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / withSentiment.length;
        // Count high confidence analyses
        const highConfidenceCount = withSentiment.filter(m => (m.sentimentAnalysis?.confidence || 0) >= 0.8).length;
        // Find most bullish and bearish mentions
        const mostBullishMention = withSentiment
            .filter(m => m.sentimentAnalysis?.sentiment === "bullish")
            .sort((a, b) => (b.sentimentAnalysis?.sentimentScore || 0) - (a.sentimentAnalysis?.sentimentScore || 0))[0];
        const mostBearishMention = withSentiment
            .filter(m => m.sentimentAnalysis?.sentiment === "bearish")
            .sort((a, b) => (a.sentimentAnalysis?.sentimentScore || 100) - (b.sentimentAnalysis?.sentimentScore || 100))[0];
        return {
            totalMentions: mentions.length,
            analyzedMentions: withSentiment.length,
            sentimentBreakdown: sentimentCounts,
            averageScore: Math.round(averageScore),
            highConfidenceCount,
            mostBullishMention: mostBullishMention ? {
                id: mostBullishMention._id,
                content: mostBullishMention.mentionContent,
                author: mostBullishMention.mentionAuthor,
                score: mostBullishMention.sentimentAnalysis?.sentimentScore || 0,
            } : null,
            mostBearishMention: mostBearishMention ? {
                id: mostBearishMention._id,
                content: mostBearishMention.mentionContent,
                author: mostBearishMention.mentionAuthor,
                score: mostBearishMention.sentimentAnalysis?.sentimentScore || 0,
            } : null,
        };
    },
});
/**
 * Get mentions filtered by sentiment
 */
export const getMentionsBySentiment = query({
    args: {
        sentiment: v.optional(v.union(v.literal("bullish"), v.literal("bearish"), v.literal("neutral"))),
        minScore: v.optional(v.number()),
        maxScore: v.optional(v.number()),
        accountId: v.optional(v.id("twitterAccounts")),
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const limit = args.limit || 50;
        // Filter by account if specified
        const mentions = args.accountId
            ? await ctx.db.query("mentions")
                .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.accountId))
                .collect()
            : await ctx.db.query("mentions")
                .withIndex("by_discovered_at")
                .collect();
        // Filter by sentiment analysis criteria
        let filtered = mentions.filter(m => {
            if (!m.sentimentAnalysis)
                return false;
            // Filter by sentiment type
            if (args.sentiment && m.sentimentAnalysis.sentiment !== args.sentiment) {
                return false;
            }
            // Filter by score range
            if (args.minScore !== undefined && m.sentimentAnalysis.sentimentScore < args.minScore) {
                return false;
            }
            if (args.maxScore !== undefined && m.sentimentAnalysis.sentimentScore > args.maxScore) {
                return false;
            }
            return true;
        });
        // Sort by sentiment score (extreme scores first)
        filtered.sort((a, b) => {
            const aScore = a.sentimentAnalysis?.sentimentScore || 50;
            const bScore = b.sentimentAnalysis?.sentimentScore || 50;
            // For bullish, higher scores first; for bearish, lower scores first
            if (args.sentiment === "bullish") {
                return bScore - aScore;
            }
            else if (args.sentiment === "bearish") {
                return aScore - bScore;
            }
            else {
                // For neutral or no filter, show most extreme scores first
                const aDistance = Math.abs(aScore - 50);
                const bDistance = Math.abs(bScore - 50);
                return bDistance - aDistance;
            }
        });
        return filtered.slice(0, limit);
    },
});
/**
 * Helper function to calculate sentiment trend over time
 */
function calculateSentimentTrend(mentions, timeRange) {
    const now = Date.now();
    const intervals = timeRange === "24h" ? 24 : timeRange === "7d" ? 7 : 30;
    const intervalDuration = timeRange === "24h" ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000;
    const trend = [];
    for (let i = intervals - 1; i >= 0; i--) {
        const intervalStart = now - (i + 1) * intervalDuration;
        const intervalEnd = now - i * intervalDuration;
        const intervalMentions = mentions.filter(m => m.createdAt >= intervalStart && m.createdAt < intervalEnd);
        if (intervalMentions.length > 0) {
            const avgScore = intervalMentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / intervalMentions.length;
            const sentimentCounts = {
                bullish: intervalMentions.filter(m => m.sentimentAnalysis?.sentiment === "bullish").length,
                bearish: intervalMentions.filter(m => m.sentimentAnalysis?.sentiment === "bearish").length,
                neutral: intervalMentions.filter(m => m.sentimentAnalysis?.sentiment === "neutral").length,
            };
            trend.push({
                timestamp: intervalEnd,
                averageScore: Math.round(avgScore),
                count: intervalMentions.length,
                breakdown: sentimentCounts,
            });
        }
        else {
            trend.push({
                timestamp: intervalEnd,
                averageScore: 50,
                count: 0,
                breakdown: { bullish: 0, bearish: 0, neutral: 0 },
            });
        }
    }
    return trend;
}
