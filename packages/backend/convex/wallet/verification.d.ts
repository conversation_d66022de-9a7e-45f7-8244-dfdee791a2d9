/**
 * Initiate wallet verification process
 * Creates a challenge that the user must sign to prove wallet ownership
 */
export declare const initiateWalletVerification: any;
/**
 * Complete wallet verification with signature
 */
export declare const verifyWalletSignature: any;
/**
 * Get pending verification for user
 */
export declare const getPendingVerification: any;
/**
 * Cancel pending verification
 */
export declare const cancelVerification: any;
