/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as http from "../http.js";
import type * as lib_advancedCaching from "../lib/advancedCaching.js";
import type * as lib_advancedCaching from "../lib/advancedCaching.js";
import type * as lib_aiResponseCache from "../lib/aiResponseCache.js";
import type * as lib_aiResponseCache from "../lib/aiResponseCache.js";
import type * as lib_ai_fallback_client from "../lib/ai_fallback_client.js";
import type * as lib_ai_fallback_client from "../lib/ai_fallback_client.js";
import type * as lib_analysisUtils from "../lib/analysisUtils.js";
import type * as lib_analysisUtils from "../lib/analysisUtils.js";
import type * as lib_bandwidthMonitor from "../lib/bandwidthMonitor.js";
import type * as lib_bandwidthMonitor from "../lib/bandwidthMonitor.js";
import type * as lib_config from "../lib/config.js";
import type * as lib_config from "../lib/config.js";
import type * as lib_config_validator from "../lib/config_validator.js";
import type * as lib_config_validator from "../lib/config_validator.js";
import type * as lib_debugConfig from "../lib/debugConfig.js";
import type * as lib_debugConfig from "../lib/debugConfig.js";
import type * as lib_embeddingUtils from "../lib/embeddingUtils.js";
import type * as lib_embeddingUtils from "../lib/embeddingUtils.js";
import type * as lib_error_handler from "../lib/error_handler.js";
import type * as lib_error_handler from "../lib/error_handler.js";
import type * as lib_fal_client from "../lib/fal_client.js";
import type * as lib_fal_client from "../lib/fal_client.js";
import type * as lib_input_sanitizer from "../lib/input_sanitizer.js";
import type * as lib_input_sanitizer from "../lib/input_sanitizer.js";
import type * as lib_intelligentBatching from "../lib/intelligentBatching.js";
import type * as lib_intelligentBatching from "../lib/intelligentBatching.js";
import type * as lib_mentionCache from "../lib/mentionCache.js";
import type * as lib_mentionCache from "../lib/mentionCache.js";
import type * as lib_model_selector from "../lib/model_selector.js";
import type * as lib_model_selector from "../lib/model_selector.js";
import type * as lib_openai_client from "../lib/openai_client.js";
import type * as lib_openai_client from "../lib/openai_client.js";
import type * as lib_openrouter_client from "../lib/openrouter_client.js";
import type * as lib_openrouter_client from "../lib/openrouter_client.js";
import type * as lib_optimizationConfig from "../lib/optimizationConfig.js";
import type * as lib_optimizationConfig from "../lib/optimizationConfig.js";
import type * as lib_production_config from "../lib/production_config.js";
import type * as lib_production_config from "../lib/production_config.js";
import type * as lib_projections from "../lib/projections.js";
import type * as lib_projections from "../lib/projections.js";
import type * as lib_prompt_templates from "../lib/prompt_templates.js";
import type * as lib_prompt_templates from "../lib/prompt_templates.js";
import type * as lib_queryLimitEnforcer from "../lib/queryLimitEnforcer.js";
import type * as lib_queryLimitEnforcer from "../lib/queryLimitEnforcer.js";
import type * as lib_rate_limiter from "../lib/rate_limiter.js";
import type * as lib_rate_limiter from "../lib/rate_limiter.js";
import type * as lib_secure_logger from "../lib/secure_logger.js";
import type * as lib_secure_logger from "../lib/secure_logger.js";
import type * as lib_security_headers from "../lib/security_headers.js";
import type * as lib_security_headers from "../lib/security_headers.js";
import type * as lib_twitter_api_monitor from "../lib/twitter_api_monitor.js";
import type * as lib_twitter_api_monitor from "../lib/twitter_api_monitor.js";
import type * as lib_twitter_client from "../lib/twitter_client.js";
import type * as lib_twitter_client from "../lib/twitter_client.js";
import type * as lib_twitter_health_check from "../lib/twitter_health_check.js";
import type * as lib_twitter_health_check from "../lib/twitter_health_check.js";
import type * as lib_twitter_monitoring from "../lib/twitter_monitoring.js";
import type * as lib_twitter_monitoring from "../lib/twitter_monitoring.js";
import type * as lib_twitter_rate_limiting from "../lib/twitter_rate_limiting.js";
import type * as lib_twitter_rate_limiting from "../lib/twitter_rate_limiting.js";
import type * as lib_twitter_utils from "../lib/twitter_utils.js";
import type * as lib_twitter_utils from "../lib/twitter_utils.js";
import type * as lib_unified_image_client from "../lib/unified_image_client.js";
import type * as lib_unified_image_client from "../lib/unified_image_client.js";
import type * as lib_xai_client from "../lib/xai_client.js";
import type * as lib_xai_client from "../lib/xai_client.js";
import type * as todos from "../todos.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  http: typeof http;
  "lib/advancedCaching": typeof lib_advancedCaching;
  "lib/advancedCaching": typeof lib_advancedCaching;
  "lib/aiResponseCache": typeof lib_aiResponseCache;
  "lib/aiResponseCache": typeof lib_aiResponseCache;
  "lib/ai_fallback_client": typeof lib_ai_fallback_client;
  "lib/ai_fallback_client": typeof lib_ai_fallback_client;
  "lib/analysisUtils": typeof lib_analysisUtils;
  "lib/analysisUtils": typeof lib_analysisUtils;
  "lib/bandwidthMonitor": typeof lib_bandwidthMonitor;
  "lib/bandwidthMonitor": typeof lib_bandwidthMonitor;
  "lib/config": typeof lib_config;
  "lib/config": typeof lib_config;
  "lib/config_validator": typeof lib_config_validator;
  "lib/config_validator": typeof lib_config_validator;
  "lib/debugConfig": typeof lib_debugConfig;
  "lib/debugConfig": typeof lib_debugConfig;
  "lib/embeddingUtils": typeof lib_embeddingUtils;
  "lib/embeddingUtils": typeof lib_embeddingUtils;
  "lib/error_handler": typeof lib_error_handler;
  "lib/error_handler": typeof lib_error_handler;
  "lib/fal_client": typeof lib_fal_client;
  "lib/fal_client": typeof lib_fal_client;
  "lib/input_sanitizer": typeof lib_input_sanitizer;
  "lib/input_sanitizer": typeof lib_input_sanitizer;
  "lib/intelligentBatching": typeof lib_intelligentBatching;
  "lib/intelligentBatching": typeof lib_intelligentBatching;
  "lib/mentionCache": typeof lib_mentionCache;
  "lib/mentionCache": typeof lib_mentionCache;
  "lib/model_selector": typeof lib_model_selector;
  "lib/model_selector": typeof lib_model_selector;
  "lib/openai_client": typeof lib_openai_client;
  "lib/openai_client": typeof lib_openai_client;
  "lib/openrouter_client": typeof lib_openrouter_client;
  "lib/openrouter_client": typeof lib_openrouter_client;
  "lib/optimizationConfig": typeof lib_optimizationConfig;
  "lib/optimizationConfig": typeof lib_optimizationConfig;
  "lib/production_config": typeof lib_production_config;
  "lib/production_config": typeof lib_production_config;
  "lib/projections": typeof lib_projections;
  "lib/projections": typeof lib_projections;
  "lib/prompt_templates": typeof lib_prompt_templates;
  "lib/prompt_templates": typeof lib_prompt_templates;
  "lib/queryLimitEnforcer": typeof lib_queryLimitEnforcer;
  "lib/queryLimitEnforcer": typeof lib_queryLimitEnforcer;
  "lib/rate_limiter": typeof lib_rate_limiter;
  "lib/rate_limiter": typeof lib_rate_limiter;
  "lib/secure_logger": typeof lib_secure_logger;
  "lib/secure_logger": typeof lib_secure_logger;
  "lib/security_headers": typeof lib_security_headers;
  "lib/security_headers": typeof lib_security_headers;
  "lib/twitter_api_monitor": typeof lib_twitter_api_monitor;
  "lib/twitter_api_monitor": typeof lib_twitter_api_monitor;
  "lib/twitter_client": typeof lib_twitter_client;
  "lib/twitter_client": typeof lib_twitter_client;
  "lib/twitter_health_check": typeof lib_twitter_health_check;
  "lib/twitter_health_check": typeof lib_twitter_health_check;
  "lib/twitter_monitoring": typeof lib_twitter_monitoring;
  "lib/twitter_monitoring": typeof lib_twitter_monitoring;
  "lib/twitter_rate_limiting": typeof lib_twitter_rate_limiting;
  "lib/twitter_rate_limiting": typeof lib_twitter_rate_limiting;
  "lib/twitter_utils": typeof lib_twitter_utils;
  "lib/twitter_utils": typeof lib_twitter_utils;
  "lib/unified_image_client": typeof lib_unified_image_client;
  "lib/unified_image_client": typeof lib_unified_image_client;
  "lib/xai_client": typeof lib_xai_client;
  "lib/xai_client": typeof lib_xai_client;
  todos: typeof todos;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
