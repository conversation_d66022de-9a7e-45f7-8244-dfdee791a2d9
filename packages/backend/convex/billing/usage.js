import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
/**
 * Get user's current usage for today
 */
export const getUserUsage = query({
    args: {},
    handler: async (ctx) => {
        console.log("📊 Getting user usage...");
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("User not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const usage = await ctx.db
            .query("usageTracking")
            .withIndex("by_user_date", (q) => q.eq("userId", user._id).eq("date", today))
            .first();
        if (!usage) {
            console.log("📝 No usage found for today, returning zero usage");
            return {
                date: today,
                usage: {
                    aiResponses: 0,
                    imageGenerations: 0,
                    apiRequests: 0,
                    bulkOperations: 0,
                    premiumAiCalls: 0,
                    analyticsQueries: 0,
                },
                limits: {
                    aiResponses: 100, // Default starter limits
                    imageGenerations: 0,
                    apiRequests: 1000,
                    bulkOperations: 0,
                    premiumAiCalls: 0,
                    analyticsQueries: 10,
                },
            };
        }
        console.log(`✅ Found usage for ${today}`);
        return usage;
    },
});
/**
 * Track usage for a specific feature
 */
export const trackUsage = mutation({
    args: {
        feature: v.union(v.literal("aiResponses"), v.literal("imageGenerations"), v.literal("apiRequests"), v.literal("bulkOperations"), v.literal("premiumAiCalls"), v.literal("analyticsQueries")),
        amount: v.optional(v.number()), // Default to 1
    },
    handler: async (ctx, args) => {
        console.log(`📈 Tracking usage: ${args.feature} (+${args.amount || 1})`);
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("User not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Get user's subscription to determine limits
        const subscription = await ctx.db
            .query("subscriptions")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .filter((q) => q.eq(q.field("status"), "active"))
            .first();
        const planId = subscription?.planId || "starter";
        const today = new Date().toISOString().split('T')[0];
        const amount = args.amount || 1;
        // Get current usage
        const existingUsage = await ctx.db
            .query("usageTracking")
            .withIndex("by_user_date", (q) => q.eq("userId", user._id).eq("date", today))
            .first();
        // Get plan limits
        const limits = getPlanLimits(planId);
        if (existingUsage) {
            // Update existing usage
            const newUsage = {
                ...existingUsage.usage,
                [args.feature]: existingUsage.usage[args.feature] + amount,
            };
            await ctx.db.patch(existingUsage._id, {
                usage: newUsage,
                limits,
                lastUpdated: Date.now(),
            });
            console.log(`✅ Updated usage: ${args.feature} = ${newUsage[args.feature]}`);
            return { success: true, newUsage: newUsage[args.feature], limit: limits[args.feature] };
        }
        else {
            // Create new usage record
            const initialUsage = {
                aiResponses: 0,
                imageGenerations: 0,
                apiRequests: 0,
                bulkOperations: 0,
                premiumAiCalls: 0,
                analyticsQueries: 0,
                [args.feature]: amount,
            };
            await ctx.db.insert("usageTracking", {
                userId: user._id,
                date: today,
                planId,
                usage: initialUsage,
                limits,
                lastUpdated: Date.now(),
            });
            console.log(`✅ Created new usage record: ${args.feature} = ${amount}`);
            return { success: true, newUsage: amount, limit: limits[args.feature] };
        }
    },
});
/**
 * Check if user can perform an action (has remaining quota)
 */
export const canPerformAction = query({
    args: {
        feature: v.union(v.literal("aiResponses"), v.literal("imageGenerations"), v.literal("apiRequests"), v.literal("bulkOperations"), v.literal("premiumAiCalls"), v.literal("analyticsQueries")),
        amount: v.optional(v.number()), // Default to 1
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            return { canPerform: false, reason: "Not authenticated" };
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            return { canPerform: false, reason: "User not found" };
        }
        // Get user's subscription
        const subscription = await ctx.db
            .query("subscriptions")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .filter((q) => q.eq(q.field("status"), "active"))
            .first();
        const planId = subscription?.planId || "starter";
        const today = new Date().toISOString().split('T')[0];
        const amount = args.amount || 1;
        // Get current usage
        const usage = await ctx.db
            .query("usageTracking")
            .withIndex("by_user_date", (q) => q.eq("userId", user._id).eq("date", today))
            .first();
        const limits = getPlanLimits(planId);
        const currentUsage = usage?.usage[args.feature] || 0;
        const limit = limits[args.feature];
        // -1 means unlimited
        if (limit === -1) {
            return { canPerform: true, remaining: -1 };
        }
        const remaining = limit - currentUsage;
        const canPerform = remaining >= amount;
        return {
            canPerform,
            remaining,
            limit,
            currentUsage,
            reason: canPerform ? null : `Limit exceeded. Used ${currentUsage}/${limit}`,
        };
    },
});
/**
 * Helper function to get plan limits
 */
function getPlanLimits(planId) {
    const planLimits = {
        starter: {
            aiResponses: 100,
            imageGenerations: 0,
            apiRequests: 1000,
            bulkOperations: 0,
            premiumAiCalls: 0,
            analyticsQueries: 10,
        },
        pro: {
            aiResponses: 500,
            imageGenerations: 50,
            apiRequests: 5000,
            bulkOperations: 10,
            premiumAiCalls: 100,
            analyticsQueries: 100,
        },
        enterprise: {
            aiResponses: -1, // unlimited
            imageGenerations: -1, // unlimited
            apiRequests: 25000,
            bulkOperations: -1, // unlimited
            premiumAiCalls: -1, // unlimited
            analyticsQueries: -1, // unlimited
        },
    };
    return planLimits[planId];
}
