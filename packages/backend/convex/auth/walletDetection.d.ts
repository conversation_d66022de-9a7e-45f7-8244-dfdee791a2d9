/**
 * Detect and store wallet from Clerk authentication
 * This runs automatically when user logs in with wallet via Clerk
 */
export declare const detectAndStoreClerkWallet: any;
/**
 * Get user's wallets with enhanced information
 */
export declare const getUserWallets: any;
export declare const getUserTwitterAccounts: any;
/**
 * Get user's primary wallet
 */
export declare const getPrimaryWallet: any;
/**
 * Set primary wallet for user
 */
export declare const setPrimaryWallet: any;
