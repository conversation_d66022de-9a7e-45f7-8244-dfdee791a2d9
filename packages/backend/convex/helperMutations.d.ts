/**
 * Store a job result - SECURED
 * 🔐 SECURITY: Requires authentication and enforces user ownership
 */
export declare const storeJob: import("convex/server").RegisteredMutation<"public", {
    metadata?: {} | undefined;
    results?: {} | undefined;
    totalItems?: number | undefined;
    processedItems?: number | undefined;
    status: "pending" | "failed" | "completed" | "running" | "cancelled";
    progress: number;
    jobType: string;
}, Promise<import("convex/values").GenericId<"jobs">>>;
/**
 * Update job status - SECURED
 * 🔐 SECURITY: Requires authentication and verifies job ownership
 */
export declare const updateJobStatus: import("convex/server").RegisteredMutation<"public", {
    metadata?: {} | undefined;
    progress?: number | undefined;
    results?: {} | undefined;
    processedItems?: number | undefined;
    status: "pending" | "failed" | "completed" | "running" | "cancelled";
    jobId: import("convex/values").GenericId<"jobs">;
}, Promise<void>>;
/**
 * Store xAI search results for analytics and history - SECURED
 * 🔐 SECURITY: Requires authentication and associates with user
 */
export declare const storeXAISearchResult: import("convex/server").RegisteredMutation<"public", {
    metadata?: {} | undefined;
    content: string;
    searchType: string;
    query: string;
    citations: string[];
}, Promise<import("convex/values").GenericId<"searchResults">>>;
/**
 * Store mention with duplicate check
 */
export declare const storeMentionWithCheck: import("convex/server").RegisteredMutation<"public", {
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    url: string;
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote";
    priority: "high" | "medium" | "low";
}, Promise<import("convex/values").GenericId<"mentions"> | null>>;
/**
 * Update Twitter account last scraped timestamp
 */
export declare const updateTwitterAccountLastScraped: import("convex/server").RegisteredMutation<"public", {
    lastScrapedAt: number;
    accountId: import("convex/values").GenericId<"twitterAccounts">;
}, Promise<void>>;
export declare const bulkDeleteTweets: import("convex/server").RegisteredMutation<"public", {
    tweetIds: import("convex/values").GenericId<"tweets">[];
}, Promise<void>>;
export declare const bulkDeleteMentions: import("convex/server").RegisteredMutation<"public", {
    mentionIds: import("convex/values").GenericId<"mentions">[];
}, Promise<void>>;
export declare const bulkDeleteResponses: import("convex/server").RegisteredMutation<"public", {
    responseIds: import("convex/values").GenericId<"responses">[];
}, Promise<void>>;
export declare const deleteEmbedding: import("convex/server").RegisteredMutation<"public", {
    tableName: "tweetEmbeddings" | "mentionEmbeddings" | "responseEmbeddings" | "userContextEmbeddings";
    embeddingId: string;
}, Promise<void>>;
