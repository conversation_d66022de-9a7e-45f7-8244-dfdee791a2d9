import { mutation } from "./_generated/server";
import { v } from "convex/values";
/**
 * Remove a wallet from user's account
 */
export const removeWallet = mutation({
    args: {
        walletId: v.id("wallets"),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Get wallet to verify ownership
        const wallet = await ctx.db.get(args.walletId);
        if (!wallet || wallet.userId !== user._id) {
            throw new Error("Wallet not found or doesn't belong to user");
        }
        // Don't allow removing the only wallet
        const userWallets = await ctx.db
            .query("wallets")
            .withIndex("by_user", (q) => q.eq("userId", user._id))
            .collect();
        if (userWallets.length <= 1) {
            throw new Error("Cannot remove the only wallet");
        }
        const wasPrimary = wallet.isPrimary;
        // Delete the wallet
        await ctx.db.delete(args.walletId);
        // If this was the primary wallet, set another one as primary
        if (wasPrimary) {
            const remainingWallets = userWallets.filter(w => w._id !== args.walletId);
            if (remainingWallets.length > 0) {
                const newPrimary = remainingWallets[0];
                await ctx.db.patch(newPrimary._id, { isPrimary: true });
                await ctx.db.patch(user._id, { primaryWalletId: newPrimary._id });
            }
            else {
                await ctx.db.patch(user._id, { primaryWalletId: undefined });
            }
        }
        return { success: true };
    },
});
/**
 * Update wallet metadata
 */
export const updateWalletMetadata = mutation({
    args: {
        walletId: v.id("wallets"),
        ensName: v.optional(v.string()),
        solanaName: v.optional(v.string()),
        balance: v.optional(v.string()),
        lastBalanceUpdate: v.optional(v.number()),
        publicKey: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Verify wallet ownership
        const wallet = await ctx.db.get(args.walletId);
        if (!wallet || wallet.userId !== user._id) {
            throw new Error("Wallet not found or doesn't belong to user");
        }
        // Update wallet metadata
        await ctx.db.patch(args.walletId, {
            metadata: {
                ensName: args.ensName,
                solanaName: args.solanaName,
                balance: args.balance,
                lastBalanceUpdate: args.lastBalanceUpdate,
                publicKey: args.publicKey,
            },
            lastUsedAt: Date.now(),
        });
        return await ctx.db.get(args.walletId);
    },
});
/**
 * Update user wallet preferences
 */
export const updateWalletPreferences = mutation({
    args: {
        preferredBlockchain: v.optional(v.union(v.literal("ethereum"), v.literal("solana"), v.literal("polygon"), v.literal("base"))),
        autoConnectWallet: v.optional(v.boolean()),
        showBalances: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Update user preferences
        await ctx.db.patch(user._id, {
            walletPreferences: {
                preferredBlockchain: args.preferredBlockchain,
                autoConnectWallet: args.autoConnectWallet,
                showBalances: args.showBalances,
            },
            updatedAt: Date.now(),
        });
        return await ctx.db.get(user._id);
    },
});
/**
 * Mark wallet as recently used
 */
export const markWalletUsed = mutation({
    args: {
        walletId: v.id("wallets"),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Verify wallet ownership
        const wallet = await ctx.db.get(args.walletId);
        if (!wallet || wallet.userId !== user._id) {
            throw new Error("Wallet not found or doesn't belong to user");
        }
        // Update last used timestamp
        await ctx.db.patch(args.walletId, {
            lastUsedAt: Date.now(),
        });
        return { success: true };
    },
});
/**
 * Refresh wallet balance (to be called from frontend)
 */
export const refreshWalletBalance = mutation({
    args: {
        walletId: v.id("wallets"),
        balance: v.string(),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.db
            .query("users")
            .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
            .first();
        if (!user) {
            throw new Error("User not found");
        }
        // Verify wallet ownership
        const wallet = await ctx.db.get(args.walletId);
        if (!wallet || wallet.userId !== user._id) {
            throw new Error("Wallet not found or doesn't belong to user");
        }
        // Update balance in metadata
        const currentMetadata = wallet.metadata || {};
        await ctx.db.patch(args.walletId, {
            metadata: {
                ...currentMetadata,
                balance: args.balance,
                lastBalanceUpdate: Date.now(),
            },
            lastUsedAt: Date.now(),
        });
        return await ctx.db.get(args.walletId);
    },
});
