import type { Id } from "./_generated/dataModel";
/**
 * Store a generated AI response
 */
export declare const storeResponse: import("convex/server").RegisteredMutation<"public", {
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Update response status (approve, decline, etc.)
 */
export declare const updateResponseStatus: import("convex/server").RegisteredMutation<"public", {
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Edit response content
 */
export declare const editResponse: import("convex/server").RegisteredMutation<"public", {
    notes?: string | undefined;
    content: string;
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Delete a response
 */
export declare const deleteResponse: import("convex/server").RegisteredMutation<"public", {
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Bulk approve responses
 */
export declare const bulkApproveResponses: import("convex/server").RegisteredMutation<"public", {
    responseIds: import("convex/values").GenericId<"responses">[];
}, Promise<{
    success: boolean;
    updated: number;
    total: number;
}>>;
/**
 * Generate multiple response variations
 */
export declare const generateResponseVariations: import("convex/server").RegisteredAction<"public", {
    count?: number | undefined;
    styles: string[];
    originalResponseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    originalResponseId: import("convex/values").GenericId<"responses">;
    variations: Id<"responses">[];
    generated: number;
    requested: number;
}>>;
/**
 * Mark response as posted (when actually posted to Twitter)
 */
export declare const markResponsePosted: import("convex/server").RegisteredMutation<"public", {
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    postedTweetId: string;
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Update actual engagement metrics for posted responses
 */
export declare const updateResponseEngagement: import("convex/server").RegisteredMutation<"public", {
    actualEngagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Set user's preferred response style
 */
export declare const setUserPreferredStyle: import("convex/server").RegisteredMutation<"public", {
    userId: import("convex/values").GenericId<"users">;
    preferredStyle: string;
}, Promise<{
    _id: import("convex/values").GenericId<"userSettings">;
    _creationTime: number;
    preferredResponseStyle?: string | undefined;
    autoApproveResponses?: boolean | undefined;
    notificationPreferences?: {
        emailEnabled: boolean;
        pushEnabled: boolean;
        priorityOnly: boolean;
    } | undefined;
    analysisPreferences?: {
        enableSemanticAnalysis: boolean;
        minEngagementThreshold: number;
        keywordFilters: string[];
    } | undefined;
    apiPreferences?: {
        preferredModel: string;
        maxTokensPerRequest: number;
        temperatureSetting: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
} | null>>;
