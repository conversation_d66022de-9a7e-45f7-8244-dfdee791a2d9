declare const _default: import("convex/server").SchemaDefinition<{
    users: import("convex/server").TableDefinition<import("convex/values").VObject<{
        image?: string | undefined;
        primaryWalletId?: import("convex/values").GenericId<"wallets"> | undefined;
        walletPreferences?: {
            preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
            autoConnectWallet?: boolean | undefined;
            showBalances?: boolean | undefined;
        } | undefined;
        lastMentionRefresh?: number | undefined;
        updatedAt?: number | undefined;
        name: string;
        email: string;
        clerkId: string;
        createdAt: number;
    }, {
        name: import("convex/values").VString<string, "required">;
        email: import("convex/values").VString<string, "required">;
        clerkId: import("convex/values").VString<string, "required">;
        image: import("convex/values").VString<string | undefined, "optional">;
        primaryWalletId: import("convex/values").VId<import("convex/values").GenericId<"wallets"> | undefined, "optional">;
        walletPreferences: import("convex/values").VObject<{
            preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
            autoConnectWallet?: boolean | undefined;
            showBalances?: boolean | undefined;
        } | undefined, {
            preferredBlockchain: import("convex/values").VUnion<"ethereum" | "solana" | "polygon" | "base" | undefined, [import("convex/values").VLiteral<"ethereum", "required">, import("convex/values").VLiteral<"solana", "required">, import("convex/values").VLiteral<"polygon", "required">, import("convex/values").VLiteral<"base", "required">], "optional", never>;
            autoConnectWallet: import("convex/values").VBoolean<boolean | undefined, "optional">;
            showBalances: import("convex/values").VBoolean<boolean | undefined, "optional">;
        }, "optional", "preferredBlockchain" | "autoConnectWallet" | "showBalances">;
        lastMentionRefresh: import("convex/values").VFloat64<number | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number | undefined, "optional">;
    }, "required", "name" | "email" | "clerkId" | "image" | "primaryWalletId" | "walletPreferences" | "lastMentionRefresh" | "createdAt" | "updatedAt" | "walletPreferences.preferredBlockchain" | "walletPreferences.autoConnectWallet" | "walletPreferences.showBalances">, {
        by_clerk_id: ["clerkId", "_creationTime"];
        by_email: ["email", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
        by_last_mention_refresh: ["lastMentionRefresh", "_creationTime"];
    }, {}, {}>;
    wallets: import("convex/server").TableDefinition<import("convex/values").VObject<{
        lastUsedAt?: number | undefined;
        metadata?: {
            ensName?: string | undefined;
            solanaName?: string | undefined;
            balance?: string | undefined;
            lastBalanceUpdate?: number | undefined;
            publicKey?: string | undefined;
        } | undefined;
        userId: import("convex/values").GenericId<"users">;
        address: string;
        blockchain: "ethereum" | "solana" | "polygon" | "base";
        walletType: string;
        verified: boolean;
        isPrimary: boolean;
        connectedVia: "clerk" | "manual";
        connectedAt: number;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        address: import("convex/values").VString<string, "required">;
        blockchain: import("convex/values").VUnion<"ethereum" | "solana" | "polygon" | "base", [import("convex/values").VLiteral<"ethereum", "required">, import("convex/values").VLiteral<"solana", "required">, import("convex/values").VLiteral<"polygon", "required">, import("convex/values").VLiteral<"base", "required">], "required", never>;
        walletType: import("convex/values").VString<string, "required">;
        verified: import("convex/values").VBoolean<boolean, "required">;
        isPrimary: import("convex/values").VBoolean<boolean, "required">;
        connectedVia: import("convex/values").VUnion<"clerk" | "manual", [import("convex/values").VLiteral<"clerk", "required">, import("convex/values").VLiteral<"manual", "required">], "required", never>;
        connectedAt: import("convex/values").VFloat64<number, "required">;
        lastUsedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        metadata: import("convex/values").VObject<{
            ensName?: string | undefined;
            solanaName?: string | undefined;
            balance?: string | undefined;
            lastBalanceUpdate?: number | undefined;
            publicKey?: string | undefined;
        } | undefined, {
            ensName: import("convex/values").VString<string | undefined, "optional">;
            solanaName: import("convex/values").VString<string | undefined, "optional">;
            balance: import("convex/values").VString<string | undefined, "optional">;
            lastBalanceUpdate: import("convex/values").VFloat64<number | undefined, "optional">;
            publicKey: import("convex/values").VString<string | undefined, "optional">;
        }, "optional", "ensName" | "solanaName" | "balance" | "lastBalanceUpdate" | "publicKey">;
    }, "required", "userId" | "address" | "blockchain" | "walletType" | "verified" | "isPrimary" | "connectedVia" | "connectedAt" | "lastUsedAt" | "metadata" | "metadata.ensName" | "metadata.solanaName" | "metadata.balance" | "metadata.lastBalanceUpdate" | "metadata.publicKey">, {
        by_user: ["userId", "_creationTime"];
        by_address: ["address", "_creationTime"];
        by_blockchain: ["blockchain", "_creationTime"];
        by_user_primary: ["userId", "isPrimary", "_creationTime"];
        by_user_blockchain: ["userId", "blockchain", "_creationTime"];
    }, {}, {}>;
    credits: import("convex/server").TableDefinition<import("convex/values").VObject<{
        lastPurchaseAt?: number | undefined;
        lastPurchaseAmount?: number | undefined;
        userId: import("convex/values").GenericId<"users">;
        balance: number;
        version: number;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        balance: import("convex/values").VFloat64<number, "required">;
        lastPurchaseAt: import("convex/values").VFloat64<number | undefined, "optional">;
        lastPurchaseAmount: import("convex/values").VFloat64<number | undefined, "optional">;
        version: import("convex/values").VFloat64<number, "required">;
    }, "required", "userId" | "balance" | "lastPurchaseAt" | "lastPurchaseAmount" | "version">, {
        by_userId: ["userId", "_creationTime"];
    }, {}, {}>;
    processedTransactions: import("convex/server").TableDefinition<import("convex/values").VObject<{
        purchaseTransactionId?: string | undefined;
        userId: import("convex/values").GenericId<"users">;
        signature: string;
        processedAt: number;
        creditsPurchased: number;
        splTokenAmount: number;
    }, {
        signature: import("convex/values").VString<string, "required">;
        processedAt: import("convex/values").VFloat64<number, "required">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        creditsPurchased: import("convex/values").VFloat64<number, "required">;
        splTokenAmount: import("convex/values").VFloat64<number, "required">;
        purchaseTransactionId: import("convex/values").VString<string | undefined, "optional">;
    }, "required", "userId" | "signature" | "processedAt" | "creditsPurchased" | "splTokenAmount" | "purchaseTransactionId">, {
        by_signature: ["signature", "_creationTime"];
        by_userId: ["userId", "_creationTime"];
    }, {}, {}>;
    walletVerifications: import("convex/server").TableDefinition<import("convex/values").VObject<{
        signature?: string | undefined;
        attemptedAt?: number | undefined;
        createdAt: number;
        userId: import("convex/values").GenericId<"users">;
        address: string;
        blockchain: "ethereum" | "solana" | "polygon" | "base";
        challenge: string;
        status: "verified" | "pending" | "failed" | "expired";
        expiresAt: number;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        address: import("convex/values").VString<string, "required">;
        blockchain: import("convex/values").VUnion<"ethereum" | "solana" | "polygon" | "base", [import("convex/values").VLiteral<"ethereum", "required">, import("convex/values").VLiteral<"solana", "required">, import("convex/values").VLiteral<"polygon", "required">, import("convex/values").VLiteral<"base", "required">], "required", never>;
        challenge: import("convex/values").VString<string, "required">;
        signature: import("convex/values").VString<string | undefined, "optional">;
        status: import("convex/values").VUnion<"verified" | "pending" | "failed" | "expired", [import("convex/values").VLiteral<"pending", "required">, import("convex/values").VLiteral<"verified", "required">, import("convex/values").VLiteral<"failed", "required">, import("convex/values").VLiteral<"expired", "required">], "required", never>;
        expiresAt: import("convex/values").VFloat64<number, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        attemptedAt: import("convex/values").VFloat64<number | undefined, "optional">;
    }, "required", "createdAt" | "userId" | "address" | "blockchain" | "signature" | "challenge" | "status" | "expiresAt" | "attemptedAt">, {
        by_user: ["userId", "_creationTime"];
        by_address: ["address", "_creationTime"];
        by_status: ["status", "_creationTime"];
        by_expires: ["expiresAt", "_creationTime"];
    }, {}, {}>;
    twitterAccounts: import("convex/server").TableDefinition<import("convex/values").VObject<{
        updatedAt?: number | undefined;
        isMonitoringEnabled?: boolean | undefined;
        lastScrapedAt?: number | undefined;
        createdAt: number;
        userId: import("convex/values").GenericId<"users">;
        handle: string;
        displayName: string;
        isActive: boolean;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        handle: import("convex/values").VString<string, "required">;
        displayName: import("convex/values").VString<string, "required">;
        isActive: import("convex/values").VBoolean<boolean, "required">;
        isMonitoringEnabled: import("convex/values").VBoolean<boolean | undefined, "optional">;
        lastScrapedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number | undefined, "optional">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "handle" | "displayName" | "isActive" | "isMonitoringEnabled" | "lastScrapedAt">, {
        by_user: ["userId", "_creationTime"];
        by_handle: ["handle", "_creationTime"];
        by_user_and_active: ["userId", "isActive", "_creationTime"];
        by_user_and_monitoring: ["userId", "isMonitoringEnabled", "_creationTime"];
        by_active_and_monitoring: ["isActive", "isMonitoringEnabled", "_creationTime"];
        by_user_and_created_at: ["userId", "createdAt", "_creationTime"];
        by_last_scraped_at: ["lastScrapedAt", "_creationTime"];
    }, {}, {}>;
    todos: import("convex/server").TableDefinition<import("convex/values").VObject<{
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        text: string;
        completed: boolean;
    }, {
        text: import("convex/values").VString<string, "required">;
        completed: import("convex/values").VBoolean<boolean, "required">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "text" | "completed">, {
        by_user: ["userId", "_creationTime"];
    }, {}, {}>;
    tweets: import("convex/server").TableDefinition<import("convex/values").VObject<{
        metadata?: {
            isThread?: boolean | undefined;
            threadPosition?: number | undefined;
            hasMedia?: boolean | undefined;
            mediaType?: string | undefined;
            language?: string | undefined;
        } | undefined;
        authorProfileImage?: string | undefined;
        isRetweet?: boolean | undefined;
        retweetedFrom?: string | undefined;
        analysisScore?: number | undefined;
        analysisReason?: string | undefined;
        embeddingId?: string | undefined;
        url?: string | undefined;
        createdAt: number;
        twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
        tweetId: string;
        content: string;
        author: string;
        authorHandle: string;
        scrapedAt: number;
        engagement: {
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        };
        analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
    }, {
        twitterAccountId: import("convex/values").VId<import("convex/values").GenericId<"twitterAccounts">, "required">;
        tweetId: import("convex/values").VString<string, "required">;
        content: import("convex/values").VString<string, "required">;
        author: import("convex/values").VString<string, "required">;
        authorHandle: import("convex/values").VString<string, "required">;
        authorProfileImage: import("convex/values").VString<string | undefined, "optional">;
        isRetweet: import("convex/values").VBoolean<boolean | undefined, "optional">;
        retweetedFrom: import("convex/values").VString<string | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        scrapedAt: import("convex/values").VFloat64<number, "required">;
        engagement: import("convex/values").VObject<{
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        }, {
            likes: import("convex/values").VFloat64<number, "required">;
            retweets: import("convex/values").VFloat64<number, "required">;
            replies: import("convex/values").VFloat64<number, "required">;
            views: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "required", "likes" | "retweets" | "replies" | "views">;
        analysisStatus: import("convex/values").VUnion<"pending" | "analyzed" | "response_worthy" | "skip", [import("convex/values").VLiteral<"pending", "required">, import("convex/values").VLiteral<"analyzed", "required">, import("convex/values").VLiteral<"response_worthy", "required">, import("convex/values").VLiteral<"skip", "required">], "required", never>;
        analysisScore: import("convex/values").VFloat64<number | undefined, "optional">;
        analysisReason: import("convex/values").VString<string | undefined, "optional">;
        embeddingId: import("convex/values").VString<string | undefined, "optional">;
        url: import("convex/values").VString<string | undefined, "optional">;
        metadata: import("convex/values").VObject<{
            isThread?: boolean | undefined;
            threadPosition?: number | undefined;
            hasMedia?: boolean | undefined;
            mediaType?: string | undefined;
            language?: string | undefined;
        } | undefined, {
            isThread: import("convex/values").VBoolean<boolean | undefined, "optional">;
            threadPosition: import("convex/values").VFloat64<number | undefined, "optional">;
            hasMedia: import("convex/values").VBoolean<boolean | undefined, "optional">;
            mediaType: import("convex/values").VString<string | undefined, "optional">;
            language: import("convex/values").VString<string | undefined, "optional">;
        }, "optional", "isThread" | "threadPosition" | "hasMedia" | "mediaType" | "language">;
    }, "required", "createdAt" | "metadata" | "twitterAccountId" | "tweetId" | "content" | "author" | "authorHandle" | "authorProfileImage" | "isRetweet" | "retweetedFrom" | "scrapedAt" | "engagement" | "analysisStatus" | "analysisScore" | "analysisReason" | "embeddingId" | "url" | "metadata.isThread" | "metadata.threadPosition" | "metadata.hasMedia" | "metadata.mediaType" | "metadata.language" | "engagement.likes" | "engagement.retweets" | "engagement.replies" | "engagement.views">, {
        by_account: ["twitterAccountId", "_creationTime"];
        by_status: ["analysisStatus", "_creationTime"];
        by_tweet_id: ["tweetId", "_creationTime"];
        by_author_handle: ["authorHandle", "_creationTime"];
        by_engagement: ["engagement.likes", "_creationTime"];
        by_scraped_at: ["scrapedAt", "_creationTime"];
    }, {
        search_content: {
            searchField: "content";
            filterFields: "twitterAccountId" | "authorHandle" | "analysisStatus";
        };
    }, {}>;
    mentions: import("convex/server").TableDefinition<import("convex/values").VObject<{
        processedAt?: number | undefined;
        embeddingId?: string | undefined;
        url?: string | undefined;
        mentionAuthorFollowers?: number | undefined;
        mentionAuthorVerified?: boolean | undefined;
        originalTweetId?: string | undefined;
        aiAnalysisResult?: {
            responseStrategy?: string | undefined;
            sentiment?: string | undefined;
            topics?: string[] | undefined;
            confidence?: number | undefined;
            shouldRespond: boolean;
        } | undefined;
        sentimentAnalysis?: {
            emotions?: {
                excitement: number;
                fear: number;
                greed: number;
                fomo: number;
                panic: number;
            } | undefined;
            sentiment: "bullish" | "bearish" | "neutral";
            confidence: number;
            sentimentScore: number;
            marketSentiment: {
                bullishScore: number;
                bearishScore: number;
                neutralScore: number;
                marketContext: string[];
            };
            reasoning: string;
            keyWords: string[];
            analysisModel: string;
            analyzedAt: number;
        } | undefined;
        notificationSentAt?: number | undefined;
        createdAt: number;
        engagement: {
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        };
        mentionTweetId: string;
        mentionContent: string;
        mentionAuthor: string;
        mentionAuthorHandle: string;
        monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
        mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
        priority: "high" | "medium" | "low";
        isProcessed: boolean;
        isNotificationSent: boolean;
        discoveredAt: number;
    }, {
        mentionTweetId: import("convex/values").VString<string, "required">;
        mentionContent: import("convex/values").VString<string, "required">;
        mentionAuthor: import("convex/values").VString<string, "required">;
        mentionAuthorHandle: import("convex/values").VString<string, "required">;
        mentionAuthorFollowers: import("convex/values").VFloat64<number | undefined, "optional">;
        mentionAuthorVerified: import("convex/values").VBoolean<boolean | undefined, "optional">;
        monitoredAccountId: import("convex/values").VId<import("convex/values").GenericId<"twitterAccounts">, "required">;
        mentionType: import("convex/values").VUnion<"mention" | "reply" | "quote" | "retweet_with_comment", [import("convex/values").VLiteral<"mention", "required">, import("convex/values").VLiteral<"reply", "required">, import("convex/values").VLiteral<"quote", "required">, import("convex/values").VLiteral<"retweet_with_comment", "required">], "required", never>;
        originalTweetId: import("convex/values").VString<string | undefined, "optional">;
        engagement: import("convex/values").VObject<{
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        }, {
            likes: import("convex/values").VFloat64<number, "required">;
            retweets: import("convex/values").VFloat64<number, "required">;
            replies: import("convex/values").VFloat64<number, "required">;
            views: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "required", "likes" | "retweets" | "replies" | "views">;
        priority: import("convex/values").VUnion<"high" | "medium" | "low", [import("convex/values").VLiteral<"high", "required">, import("convex/values").VLiteral<"medium", "required">, import("convex/values").VLiteral<"low", "required">], "required", never>;
        isProcessed: import("convex/values").VBoolean<boolean, "required">;
        aiAnalysisResult: import("convex/values").VObject<{
            responseStrategy?: string | undefined;
            sentiment?: string | undefined;
            topics?: string[] | undefined;
            confidence?: number | undefined;
            shouldRespond: boolean;
        } | undefined, {
            shouldRespond: import("convex/values").VBoolean<boolean, "required">;
            responseStrategy: import("convex/values").VString<string | undefined, "optional">;
            sentiment: import("convex/values").VString<string | undefined, "optional">;
            topics: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
            confidence: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "optional", "shouldRespond" | "responseStrategy" | "sentiment" | "topics" | "confidence">;
        sentimentAnalysis: import("convex/values").VObject<{
            emotions?: {
                excitement: number;
                fear: number;
                greed: number;
                fomo: number;
                panic: number;
            } | undefined;
            sentiment: "bullish" | "bearish" | "neutral";
            confidence: number;
            sentimentScore: number;
            marketSentiment: {
                bullishScore: number;
                bearishScore: number;
                neutralScore: number;
                marketContext: string[];
            };
            reasoning: string;
            keyWords: string[];
            analysisModel: string;
            analyzedAt: number;
        } | undefined, {
            sentiment: import("convex/values").VUnion<"bullish" | "bearish" | "neutral", [import("convex/values").VLiteral<"bullish", "required">, import("convex/values").VLiteral<"bearish", "required">, import("convex/values").VLiteral<"neutral", "required">], "required", never>;
            sentimentScore: import("convex/values").VFloat64<number, "required">;
            confidence: import("convex/values").VFloat64<number, "required">;
            marketSentiment: import("convex/values").VObject<{
                bullishScore: number;
                bearishScore: number;
                neutralScore: number;
                marketContext: string[];
            }, {
                bullishScore: import("convex/values").VFloat64<number, "required">;
                bearishScore: import("convex/values").VFloat64<number, "required">;
                neutralScore: import("convex/values").VFloat64<number, "required">;
                marketContext: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
            }, "required", "bullishScore" | "bearishScore" | "neutralScore" | "marketContext">;
            emotions: import("convex/values").VObject<{
                excitement: number;
                fear: number;
                greed: number;
                fomo: number;
                panic: number;
            } | undefined, {
                excitement: import("convex/values").VFloat64<number, "required">;
                fear: import("convex/values").VFloat64<number, "required">;
                greed: import("convex/values").VFloat64<number, "required">;
                fomo: import("convex/values").VFloat64<number, "required">;
                panic: import("convex/values").VFloat64<number, "required">;
            }, "optional", "excitement" | "fear" | "greed" | "fomo" | "panic">;
            reasoning: import("convex/values").VString<string, "required">;
            keyWords: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
            analysisModel: import("convex/values").VString<string, "required">;
            analyzedAt: import("convex/values").VFloat64<number, "required">;
        }, "optional", "sentiment" | "confidence" | "sentimentScore" | "marketSentiment" | "emotions" | "reasoning" | "keyWords" | "analysisModel" | "analyzedAt" | "marketSentiment.bullishScore" | "marketSentiment.bearishScore" | "marketSentiment.neutralScore" | "marketSentiment.marketContext" | "emotions.excitement" | "emotions.fear" | "emotions.greed" | "emotions.fomo" | "emotions.panic">;
        isNotificationSent: import("convex/values").VBoolean<boolean, "required">;
        notificationSentAt: import("convex/values").VFloat64<number | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        discoveredAt: import("convex/values").VFloat64<number, "required">;
        processedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        url: import("convex/values").VString<string | undefined, "optional">;
        embeddingId: import("convex/values").VString<string | undefined, "optional">;
    }, "required", "createdAt" | "processedAt" | "engagement" | "embeddingId" | "url" | "engagement.likes" | "engagement.retweets" | "engagement.replies" | "engagement.views" | "mentionTweetId" | "mentionContent" | "mentionAuthor" | "mentionAuthorHandle" | "mentionAuthorFollowers" | "mentionAuthorVerified" | "monitoredAccountId" | "mentionType" | "originalTweetId" | "priority" | "isProcessed" | "aiAnalysisResult" | "sentimentAnalysis" | "isNotificationSent" | "notificationSentAt" | "discoveredAt" | "aiAnalysisResult.shouldRespond" | "aiAnalysisResult.responseStrategy" | "aiAnalysisResult.sentiment" | "aiAnalysisResult.topics" | "aiAnalysisResult.confidence" | "sentimentAnalysis.sentiment" | "sentimentAnalysis.confidence" | "sentimentAnalysis.sentimentScore" | "sentimentAnalysis.marketSentiment" | "sentimentAnalysis.emotions" | "sentimentAnalysis.reasoning" | "sentimentAnalysis.keyWords" | "sentimentAnalysis.analysisModel" | "sentimentAnalysis.analyzedAt" | "sentimentAnalysis.marketSentiment.bullishScore" | "sentimentAnalysis.marketSentiment.bearishScore" | "sentimentAnalysis.marketSentiment.neutralScore" | "sentimentAnalysis.marketSentiment.marketContext" | "sentimentAnalysis.emotions.excitement" | "sentimentAnalysis.emotions.fear" | "sentimentAnalysis.emotions.greed" | "sentimentAnalysis.emotions.fomo" | "sentimentAnalysis.emotions.panic">, {
        by_monitored_account: ["monitoredAccountId", "_creationTime"];
        by_priority: ["priority", "_creationTime"];
        by_processed: ["isProcessed", "_creationTime"];
        by_notification: ["isNotificationSent", "_creationTime"];
        by_discovered_at: ["discoveredAt", "_creationTime"];
        by_mention_type: ["mentionType", "_creationTime"];
        by_author_handle: ["mentionAuthorHandle", "_creationTime"];
        by_tweet_id: ["mentionTweetId", "_creationTime"];
        by_account_and_discovered_at: ["monitoredAccountId", "discoveredAt", "_creationTime"];
        by_account_and_created_at: ["monitoredAccountId", "createdAt", "_creationTime"];
        by_account_and_priority: ["monitoredAccountId", "priority", "_creationTime"];
        by_account_and_processed: ["monitoredAccountId", "isProcessed", "_creationTime"];
        by_account_and_notification: ["monitoredAccountId", "isNotificationSent", "_creationTime"];
        by_account_and_type: ["monitoredAccountId", "mentionType", "_creationTime"];
        by_priority_and_processed: ["priority", "isProcessed", "_creationTime"];
        by_notification_and_discovered: ["isNotificationSent", "discoveredAt", "_creationTime"];
        by_processed_and_priority: ["isProcessed", "priority", "_creationTime"];
    }, {
        search_mentions: {
            searchField: "mentionContent";
            filterFields: "monitoredAccountId" | "mentionType" | "priority" | "isProcessed";
        };
    }, {}>;
    responses: import("convex/server").TableDefinition<import("convex/values").VObject<{
        responseStrategy?: string | undefined;
        generationModel?: string | undefined;
        contextUsed?: string[] | undefined;
        estimatedEngagement?: {
            likes: number;
            retweets: number;
            replies: number;
        } | undefined;
        generatedImage?: string | undefined;
        imagePrompt?: string | undefined;
        isEnhanced?: boolean | undefined;
        postedAt?: number | undefined;
        postedTweetId?: string | undefined;
        actualEngagement?: {
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        } | undefined;
        approvedAt?: number | undefined;
        userFeedback?: {
            notes?: string | undefined;
            rating: number;
        } | undefined;
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        status: "failed" | "draft" | "approved" | "declined" | "posted";
        content: string;
        confidence: number;
        targetType: "mention" | "tweet";
        targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
        style: string;
        characterCount: number;
    }, {
        targetType: import("convex/values").VUnion<"mention" | "tweet", [import("convex/values").VLiteral<"tweet", "required">, import("convex/values").VLiteral<"mention", "required">], "required", never>;
        targetId: import("convex/values").VUnion<import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">, [import("convex/values").VId<import("convex/values").GenericId<"tweets">, "required">, import("convex/values").VId<import("convex/values").GenericId<"mentions">, "required">], "required", never>;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        content: import("convex/values").VString<string, "required">;
        style: import("convex/values").VString<string, "required">;
        characterCount: import("convex/values").VFloat64<number, "required">;
        confidence: import("convex/values").VFloat64<number, "required">;
        generationModel: import("convex/values").VString<string | undefined, "optional">;
        contextUsed: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        responseStrategy: import("convex/values").VString<string | undefined, "optional">;
        estimatedEngagement: import("convex/values").VObject<{
            likes: number;
            retweets: number;
            replies: number;
        } | undefined, {
            likes: import("convex/values").VFloat64<number, "required">;
            retweets: import("convex/values").VFloat64<number, "required">;
            replies: import("convex/values").VFloat64<number, "required">;
        }, "optional", "likes" | "retweets" | "replies">;
        status: import("convex/values").VUnion<"failed" | "draft" | "approved" | "declined" | "posted", [import("convex/values").VLiteral<"draft", "required">, import("convex/values").VLiteral<"approved", "required">, import("convex/values").VLiteral<"declined", "required">, import("convex/values").VLiteral<"posted", "required">, import("convex/values").VLiteral<"failed", "required">], "required", never>;
        generatedImage: import("convex/values").VString<string | undefined, "optional">;
        imagePrompt: import("convex/values").VString<string | undefined, "optional">;
        isEnhanced: import("convex/values").VBoolean<boolean | undefined, "optional">;
        postedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        postedTweetId: import("convex/values").VString<string | undefined, "optional">;
        actualEngagement: import("convex/values").VObject<{
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        } | undefined, {
            likes: import("convex/values").VFloat64<number, "required">;
            retweets: import("convex/values").VFloat64<number, "required">;
            replies: import("convex/values").VFloat64<number, "required">;
            views: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "optional", "likes" | "retweets" | "replies" | "views">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
        approvedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        userFeedback: import("convex/values").VObject<{
            notes?: string | undefined;
            rating: number;
        } | undefined, {
            rating: import("convex/values").VFloat64<number, "required">;
            notes: import("convex/values").VString<string | undefined, "optional">;
        }, "optional", "rating" | "notes">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "status" | "content" | "responseStrategy" | "confidence" | "targetType" | "targetId" | "style" | "characterCount" | "generationModel" | "contextUsed" | "estimatedEngagement" | "generatedImage" | "imagePrompt" | "isEnhanced" | "postedAt" | "postedTweetId" | "actualEngagement" | "approvedAt" | "userFeedback" | "estimatedEngagement.likes" | "estimatedEngagement.retweets" | "estimatedEngagement.replies" | "actualEngagement.likes" | "actualEngagement.retweets" | "actualEngagement.replies" | "actualEngagement.views" | "userFeedback.rating" | "userFeedback.notes">, {
        by_user: ["userId", "_creationTime"];
        by_target: ["targetType", "targetId", "_creationTime"];
        by_status: ["status", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
        by_style: ["style", "_creationTime"];
        by_confidence: ["confidence", "_creationTime"];
        by_user_and_status: ["userId", "status", "_creationTime"];
        by_user_and_created_at: ["userId", "createdAt", "_creationTime"];
        by_user_and_style: ["userId", "style", "_creationTime"];
        by_status_and_created_at: ["status", "createdAt", "_creationTime"];
        by_user_and_target_type: ["userId", "targetType", "_creationTime"];
        by_target_and_status: ["targetType", "targetId", "status", "_creationTime"];
    }, {
        search_responses: {
            searchField: "content";
            filterFields: "userId" | "status" | "targetType" | "style";
        };
    }, {}>;
    responseDrafts: import("convex/server").TableDefinition<import("convex/values").VObject<{
        notes?: string | undefined;
        createdAt: number;
        userId: import("convex/values").GenericId<"users">;
        version: number;
        content: string;
        responseId: import("convex/values").GenericId<"responses">;
    }, {
        responseId: import("convex/values").VId<import("convex/values").GenericId<"responses">, "required">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        content: import("convex/values").VString<string, "required">;
        version: import("convex/values").VFloat64<number, "required">;
        notes: import("convex/values").VString<string | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "userId" | "version" | "content" | "notes" | "responseId">, {
        by_response: ["responseId", "_creationTime"];
        by_user: ["userId", "_creationTime"];
        by_version: ["responseId", "version", "_creationTime"];
    }, {}, {}>;
    tweetEmbeddings: import("convex/server").TableDefinition<import("convex/values").VObject<{
        createdAt: number;
        tweetId: import("convex/values").GenericId<"tweets">;
        embedding: number[];
        model: string;
    }, {
        tweetId: import("convex/values").VId<import("convex/values").GenericId<"tweets">, "required">;
        embedding: import("convex/values").VArray<number[], import("convex/values").VFloat64<number, "required">, "required">;
        model: import("convex/values").VString<string, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "tweetId" | "embedding" | "model">, {
        by_tweet: ["tweetId", "_creationTime"];
    }, {}, {
        by_embedding: {
            vectorField: "embedding";
            dimensions: number;
            filterFields: "model";
        };
    }>;
    mentionEmbeddings: import("convex/server").TableDefinition<import("convex/values").VObject<{
        createdAt: number;
        embedding: number[];
        model: string;
        mentionId: import("convex/values").GenericId<"mentions">;
    }, {
        mentionId: import("convex/values").VId<import("convex/values").GenericId<"mentions">, "required">;
        embedding: import("convex/values").VArray<number[], import("convex/values").VFloat64<number, "required">, "required">;
        model: import("convex/values").VString<string, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "embedding" | "model" | "mentionId">, {
        by_mention: ["mentionId", "_creationTime"];
    }, {}, {
        by_embedding: {
            vectorField: "embedding";
            dimensions: number;
            filterFields: "model";
        };
    }>;
    responseEmbeddings: import("convex/server").TableDefinition<import("convex/values").VObject<{
        createdAt: number;
        responseId: import("convex/values").GenericId<"responses">;
        embedding: number[];
        model: string;
    }, {
        responseId: import("convex/values").VId<import("convex/values").GenericId<"responses">, "required">;
        embedding: import("convex/values").VArray<number[], import("convex/values").VFloat64<number, "required">, "required">;
        model: import("convex/values").VString<string, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "responseId" | "embedding" | "model">, {
        by_response: ["responseId", "_creationTime"];
    }, {}, {
        by_embedding: {
            vectorField: "embedding";
            dimensions: number;
            filterFields: "model";
        };
    }>;
    userContextEmbeddings: import("convex/server").TableDefinition<import("convex/values").VObject<{
        weight?: number | undefined;
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        content: string;
        embedding: number[];
        model: string;
        contextType: string;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        contextType: import("convex/values").VString<string, "required">;
        content: import("convex/values").VString<string, "required">;
        embedding: import("convex/values").VArray<number[], import("convex/values").VFloat64<number, "required">, "required">;
        model: import("convex/values").VString<string, "required">;
        weight: import("convex/values").VFloat64<number | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "content" | "embedding" | "model" | "contextType" | "weight">, {
        by_user: ["userId", "_creationTime"];
        by_context_type: ["userId", "contextType", "_creationTime"];
    }, {}, {
        by_embedding: {
            vectorField: "embedding";
            dimensions: number;
            filterFields: "userId" | "model" | "contextType";
        };
    }>;
    workflowStatus: import("convex/server").TableDefinition<import("convex/values").VObject<{
        metadata?: {
            currentBatch?: number | undefined;
            totalBatches?: number | undefined;
            lastProcessedId?: string | undefined;
            estimatedTimeRemaining?: number | undefined;
        } | undefined;
        options?: {
            priority?: string | undefined;
            batchSize?: number | undefined;
            enableParallel?: boolean | undefined;
            filters?: string[] | undefined;
        } | undefined;
        summary?: {
            analyzedTweets?: number | undefined;
            responseWorthy?: number | undefined;
            averageScore?: number | undefined;
            topTopics?: string[] | undefined;
        } | undefined;
        results?: {
            metadata?: string | undefined;
            score?: number | undefined;
            id: string;
            type: string;
        }[] | undefined;
        errors?: {
            step?: string | undefined;
            severity?: string | undefined;
            message: string;
            timestamp: number;
        }[] | undefined;
        completedAt?: number | undefined;
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        workflowId: string;
        totalTweets: number;
        step: string;
        progress: number;
    }, {
        workflowId: import("convex/values").VString<string, "required">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        totalTweets: import("convex/values").VFloat64<number, "required">;
        step: import("convex/values").VString<string, "required">;
        progress: import("convex/values").VFloat64<number, "required">;
        options: import("convex/values").VObject<{
            priority?: string | undefined;
            batchSize?: number | undefined;
            enableParallel?: boolean | undefined;
            filters?: string[] | undefined;
        } | undefined, {
            batchSize: import("convex/values").VFloat64<number | undefined, "optional">;
            enableParallel: import("convex/values").VBoolean<boolean | undefined, "optional">;
            priority: import("convex/values").VString<string | undefined, "optional">;
            filters: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        }, "optional", "priority" | "batchSize" | "enableParallel" | "filters">;
        metadata: import("convex/values").VObject<{
            currentBatch?: number | undefined;
            totalBatches?: number | undefined;
            lastProcessedId?: string | undefined;
            estimatedTimeRemaining?: number | undefined;
        } | undefined, {
            currentBatch: import("convex/values").VFloat64<number | undefined, "optional">;
            totalBatches: import("convex/values").VFloat64<number | undefined, "optional">;
            lastProcessedId: import("convex/values").VString<string | undefined, "optional">;
            estimatedTimeRemaining: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "optional", "currentBatch" | "totalBatches" | "lastProcessedId" | "estimatedTimeRemaining">;
        summary: import("convex/values").VObject<{
            analyzedTweets?: number | undefined;
            responseWorthy?: number | undefined;
            averageScore?: number | undefined;
            topTopics?: string[] | undefined;
        } | undefined, {
            analyzedTweets: import("convex/values").VFloat64<number | undefined, "optional">;
            responseWorthy: import("convex/values").VFloat64<number | undefined, "optional">;
            averageScore: import("convex/values").VFloat64<number | undefined, "optional">;
            topTopics: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        }, "optional", "analyzedTweets" | "responseWorthy" | "averageScore" | "topTopics">;
        results: import("convex/values").VArray<{
            metadata?: string | undefined;
            score?: number | undefined;
            id: string;
            type: string;
        }[] | undefined, import("convex/values").VObject<{
            metadata?: string | undefined;
            score?: number | undefined;
            id: string;
            type: string;
        }, {
            id: import("convex/values").VString<string, "required">;
            type: import("convex/values").VString<string, "required">;
            score: import("convex/values").VFloat64<number | undefined, "optional">;
            metadata: import("convex/values").VString<string | undefined, "optional">;
        }, "required", "id" | "type" | "metadata" | "score">, "optional">;
        errors: import("convex/values").VArray<{
            step?: string | undefined;
            severity?: string | undefined;
            message: string;
            timestamp: number;
        }[] | undefined, import("convex/values").VObject<{
            step?: string | undefined;
            severity?: string | undefined;
            message: string;
            timestamp: number;
        }, {
            message: import("convex/values").VString<string, "required">;
            timestamp: import("convex/values").VFloat64<number, "required">;
            step: import("convex/values").VString<string | undefined, "optional">;
            severity: import("convex/values").VString<string | undefined, "optional">;
        }, "required", "step" | "message" | "timestamp" | "severity">, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
        completedAt: import("convex/values").VFloat64<number | undefined, "optional">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "metadata" | "workflowId" | "totalTweets" | "step" | "progress" | "options" | "summary" | "results" | "errors" | "completedAt" | "metadata.currentBatch" | "metadata.totalBatches" | "metadata.lastProcessedId" | "metadata.estimatedTimeRemaining" | "options.priority" | "options.batchSize" | "options.enableParallel" | "options.filters" | "summary.analyzedTweets" | "summary.responseWorthy" | "summary.averageScore" | "summary.topTopics">, {
        by_user: ["userId", "_creationTime"];
        by_workflow_id: ["workflowId", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
    }, {}, {}>;
    generatedImages: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId?: import("convex/values").GenericId<"users"> | undefined;
        lastUsedAt?: number | undefined;
        tweetId?: import("convex/values").GenericId<"tweets"> | undefined;
        style?: string | undefined;
        responseId?: import("convex/values").GenericId<"responses"> | undefined;
        imageBase64?: string | undefined;
        revisedPrompt?: string | undefined;
        platform?: string | undefined;
        size?: string | undefined;
        quality?: string | undefined;
        customName?: string | undefined;
        isFavorite?: boolean | undefined;
        createdAt: number;
        updatedAt: number;
        model: string;
        imageUrl: string;
        originalPrompt: string;
        tags: string[];
        isPublic: boolean;
        downloadCount: number;
    }, {
        imageUrl: import("convex/values").VString<string, "required">;
        imageBase64: import("convex/values").VString<string | undefined, "optional">;
        originalPrompt: import("convex/values").VString<string, "required">;
        revisedPrompt: import("convex/values").VString<string | undefined, "optional">;
        model: import("convex/values").VString<string, "required">;
        style: import("convex/values").VString<string | undefined, "optional">;
        platform: import("convex/values").VString<string | undefined, "optional">;
        size: import("convex/values").VString<string | undefined, "optional">;
        quality: import("convex/values").VString<string | undefined, "optional">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users"> | undefined, "optional">;
        responseId: import("convex/values").VId<import("convex/values").GenericId<"responses"> | undefined, "optional">;
        tweetId: import("convex/values").VId<import("convex/values").GenericId<"tweets"> | undefined, "optional">;
        tags: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        customName: import("convex/values").VString<string | undefined, "optional">;
        isPublic: import("convex/values").VBoolean<boolean, "required">;
        isFavorite: import("convex/values").VBoolean<boolean | undefined, "optional">;
        downloadCount: import("convex/values").VFloat64<number, "required">;
        lastUsedAt: import("convex/values").VFloat64<number | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "lastUsedAt" | "tweetId" | "style" | "responseId" | "model" | "imageUrl" | "imageBase64" | "originalPrompt" | "revisedPrompt" | "platform" | "size" | "quality" | "tags" | "customName" | "isPublic" | "isFavorite" | "downloadCount">, {
        by_user: ["userId", "_creationTime"];
        by_response: ["responseId", "_creationTime"];
        by_tweet: ["tweetId", "_creationTime"];
        by_platform: ["platform", "_creationTime"];
        by_style: ["style", "_creationTime"];
        by_model: ["model", "_creationTime"];
        by_public: ["isPublic", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
        by_download_count: ["downloadCount", "_creationTime"];
    }, {
        search_images: {
            searchField: "originalPrompt";
            filterFields: "userId" | "style" | "model" | "platform" | "isPublic";
        };
    }, {}>;
    searchResults: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId?: import("convex/values").GenericId<"users"> | undefined;
        metadata?: {
            model?: string | undefined;
            responseTime?: number | undefined;
            success?: boolean | undefined;
            insights?: string[] | undefined;
            tokensUsed?: number | undefined;
            searchFilters?: string[] | undefined;
        } | undefined;
        createdAt: number;
        content: string;
        searchType: string;
        query: string;
        citations: string[];
    }, {
        searchType: import("convex/values").VString<string, "required">;
        query: import("convex/values").VString<string, "required">;
        content: import("convex/values").VString<string, "required">;
        citations: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        metadata: import("convex/values").VObject<{
            model?: string | undefined;
            responseTime?: number | undefined;
            success?: boolean | undefined;
            insights?: string[] | undefined;
            tokensUsed?: number | undefined;
            searchFilters?: string[] | undefined;
        } | undefined, {
            responseTime: import("convex/values").VFloat64<number | undefined, "optional">;
            success: import("convex/values").VBoolean<boolean | undefined, "optional">;
            insights: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
            tokensUsed: import("convex/values").VFloat64<number | undefined, "optional">;
            model: import("convex/values").VString<string | undefined, "optional">;
            searchFilters: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        }, "optional", "model" | "responseTime" | "success" | "insights" | "tokensUsed" | "searchFilters">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users"> | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "userId" | "metadata" | "content" | "searchType" | "query" | "citations" | "metadata.model" | "metadata.responseTime" | "metadata.success" | "metadata.insights" | "metadata.tokensUsed" | "metadata.searchFilters">, {
        by_user: ["userId", "_creationTime"];
        by_type: ["searchType", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
    }, {
        search_queries: {
            searchField: "query";
            filterFields: "userId" | "searchType";
        };
    }, {}>;
    twitterApiUsage: import("convex/server").TableDefinition<import("convex/values").VObject<{
        responseSize?: number | undefined;
        duration?: number | undefined;
        errorMessage?: string | undefined;
        createdAt: number;
        status: "success" | "error" | "rate_limited";
        timestamp: number;
        endpoint: string;
        requestCount: number;
        cost: number;
    }, {
        timestamp: import("convex/values").VFloat64<number, "required">;
        endpoint: import("convex/values").VString<string, "required">;
        requestCount: import("convex/values").VFloat64<number, "required">;
        responseSize: import("convex/values").VFloat64<number | undefined, "optional">;
        duration: import("convex/values").VFloat64<number | undefined, "optional">;
        status: import("convex/values").VUnion<"success" | "error" | "rate_limited", [import("convex/values").VLiteral<"success", "required">, import("convex/values").VLiteral<"error", "required">, import("convex/values").VLiteral<"rate_limited", "required">], "required", never>;
        errorMessage: import("convex/values").VString<string | undefined, "optional">;
        cost: import("convex/values").VFloat64<number, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "status" | "timestamp" | "endpoint" | "requestCount" | "responseSize" | "duration" | "errorMessage" | "cost">, {
        by_timestamp: ["timestamp", "_creationTime"];
        by_endpoint: ["endpoint", "_creationTime"];
        by_status: ["status", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
        by_endpoint_timestamp: ["endpoint", "timestamp", "_creationTime"];
        by_endpoint_and_status: ["endpoint", "status", "_creationTime"];
        by_status_and_timestamp: ["status", "timestamp", "_creationTime"];
    }, {}, {}>;
    apiQuotaStatus: import("convex/server").TableDefinition<import("convex/values").VObject<{
        date: string;
        totalRequests: number;
        successfulRequests: number;
        errorRequests: number;
        rateLimitedRequests: number;
        totalCost: number;
        lastUpdated: number;
    }, {
        date: import("convex/values").VString<string, "required">;
        totalRequests: import("convex/values").VFloat64<number, "required">;
        successfulRequests: import("convex/values").VFloat64<number, "required">;
        errorRequests: import("convex/values").VFloat64<number, "required">;
        rateLimitedRequests: import("convex/values").VFloat64<number, "required">;
        totalCost: import("convex/values").VFloat64<number, "required">;
        lastUpdated: import("convex/values").VFloat64<number, "required">;
    }, "required", "date" | "totalRequests" | "successfulRequests" | "errorRequests" | "rateLimitedRequests" | "totalCost" | "lastUpdated">, {
        by_date: ["date", "_creationTime"];
        by_last_updated: ["lastUpdated", "_creationTime"];
    }, {}, {}>;
    mentionCache: import("convex/server").TableDefinition<import("convex/values").VObject<{
        timestamp: number;
        tags: string[];
        key: string;
        data: {
            mentions?: {
                id: string;
                content: string;
                author: string;
                timestamp: number;
            }[] | undefined;
            searchResults?: string[] | undefined;
            analytics?: {
                totalCount: number;
                unreadCount: number;
                highPriorityCount: number;
            } | undefined;
            accounts?: {
                id: string;
                handle: string;
                isActive: boolean;
            }[] | undefined;
        };
        ttl: number;
        hits: number;
        lastAccessed: number;
    }, {
        key: import("convex/values").VString<string, "required">;
        data: import("convex/values").VObject<{
            mentions?: {
                id: string;
                content: string;
                author: string;
                timestamp: number;
            }[] | undefined;
            searchResults?: string[] | undefined;
            analytics?: {
                totalCount: number;
                unreadCount: number;
                highPriorityCount: number;
            } | undefined;
            accounts?: {
                id: string;
                handle: string;
                isActive: boolean;
            }[] | undefined;
        }, {
            mentions: import("convex/values").VArray<{
                id: string;
                content: string;
                author: string;
                timestamp: number;
            }[] | undefined, import("convex/values").VObject<{
                id: string;
                content: string;
                author: string;
                timestamp: number;
            }, {
                id: import("convex/values").VString<string, "required">;
                content: import("convex/values").VString<string, "required">;
                author: import("convex/values").VString<string, "required">;
                timestamp: import("convex/values").VFloat64<number, "required">;
            }, "required", "id" | "content" | "author" | "timestamp">, "optional">;
            analytics: import("convex/values").VObject<{
                totalCount: number;
                unreadCount: number;
                highPriorityCount: number;
            } | undefined, {
                totalCount: import("convex/values").VFloat64<number, "required">;
                unreadCount: import("convex/values").VFloat64<number, "required">;
                highPriorityCount: import("convex/values").VFloat64<number, "required">;
            }, "optional", "totalCount" | "unreadCount" | "highPriorityCount">;
            accounts: import("convex/values").VArray<{
                id: string;
                handle: string;
                isActive: boolean;
            }[] | undefined, import("convex/values").VObject<{
                id: string;
                handle: string;
                isActive: boolean;
            }, {
                id: import("convex/values").VString<string, "required">;
                handle: import("convex/values").VString<string, "required">;
                isActive: import("convex/values").VBoolean<boolean, "required">;
            }, "required", "id" | "handle" | "isActive">, "optional">;
            searchResults: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        }, "required", "mentions" | "searchResults" | "analytics" | "accounts" | "analytics.totalCount" | "analytics.unreadCount" | "analytics.highPriorityCount">;
        timestamp: import("convex/values").VFloat64<number, "required">;
        ttl: import("convex/values").VFloat64<number, "required">;
        hits: import("convex/values").VFloat64<number, "required">;
        lastAccessed: import("convex/values").VFloat64<number, "required">;
        tags: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
    }, "required", "timestamp" | "tags" | "key" | "data" | "ttl" | "hits" | "lastAccessed" | "data.mentions" | "data.searchResults" | "data.analytics" | "data.accounts" | "data.analytics.totalCount" | "data.analytics.unreadCount" | "data.analytics.highPriorityCount">, {
        by_key: ["key", "_creationTime"];
        by_timestamp: ["timestamp", "_creationTime"];
        by_last_accessed: ["lastAccessed", "_creationTime"];
        by_tags: ["tags", "_creationTime"];
        by_timestamp_and_ttl: ["timestamp", "ttl", "_creationTime"];
        by_last_accessed_and_hits: ["lastAccessed", "hits", "_creationTime"];
    }, {}, {}>;
    optimizationConfigs: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId?: import("convex/values").GenericId<"users"> | undefined;
        isGlobal?: boolean | undefined;
        createdAt: number;
        updatedAt: number;
        settings: {
            maxMentionsPerBatch: number;
            batchProcessingInterval: number;
            cacheRetentionDays: number;
            enableSemanticCaching: boolean;
            enablePriorityScoring: boolean;
            maxApiRequestsPerMinute: number;
            adaptiveThrottling: boolean;
            resourceMonitoring: boolean;
            minConfidenceThreshold: number;
            enableQualityMetrics: boolean;
            adaptiveQualityControl: boolean;
        };
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users"> | undefined, "optional">;
        settings: import("convex/values").VObject<{
            maxMentionsPerBatch: number;
            batchProcessingInterval: number;
            cacheRetentionDays: number;
            enableSemanticCaching: boolean;
            enablePriorityScoring: boolean;
            maxApiRequestsPerMinute: number;
            adaptiveThrottling: boolean;
            resourceMonitoring: boolean;
            minConfidenceThreshold: number;
            enableQualityMetrics: boolean;
            adaptiveQualityControl: boolean;
        }, {
            maxMentionsPerBatch: import("convex/values").VFloat64<number, "required">;
            batchProcessingInterval: import("convex/values").VFloat64<number, "required">;
            cacheRetentionDays: import("convex/values").VFloat64<number, "required">;
            enableSemanticCaching: import("convex/values").VBoolean<boolean, "required">;
            enablePriorityScoring: import("convex/values").VBoolean<boolean, "required">;
            maxApiRequestsPerMinute: import("convex/values").VFloat64<number, "required">;
            adaptiveThrottling: import("convex/values").VBoolean<boolean, "required">;
            resourceMonitoring: import("convex/values").VBoolean<boolean, "required">;
            minConfidenceThreshold: import("convex/values").VFloat64<number, "required">;
            enableQualityMetrics: import("convex/values").VBoolean<boolean, "required">;
            adaptiveQualityControl: import("convex/values").VBoolean<boolean, "required">;
        }, "required", "maxMentionsPerBatch" | "batchProcessingInterval" | "cacheRetentionDays" | "enableSemanticCaching" | "enablePriorityScoring" | "maxApiRequestsPerMinute" | "adaptiveThrottling" | "resourceMonitoring" | "minConfidenceThreshold" | "enableQualityMetrics" | "adaptiveQualityControl">;
        isGlobal: import("convex/values").VBoolean<boolean | undefined, "optional">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "settings" | "isGlobal" | "settings.maxMentionsPerBatch" | "settings.batchProcessingInterval" | "settings.cacheRetentionDays" | "settings.enableSemanticCaching" | "settings.enablePriorityScoring" | "settings.maxApiRequestsPerMinute" | "settings.adaptiveThrottling" | "settings.resourceMonitoring" | "settings.minConfidenceThreshold" | "settings.enableQualityMetrics" | "settings.adaptiveQualityControl">, {
        by_user: ["userId", "_creationTime"];
        by_global: ["isGlobal", "_creationTime"];
        by_updated_at: ["updatedAt", "_creationTime"];
    }, {}, {}>;
    userSettings: import("convex/server").TableDefinition<import("convex/values").VObject<{
        preferredResponseStyle?: string | undefined;
        autoApproveResponses?: boolean | undefined;
        notificationPreferences?: {
            emailEnabled: boolean;
            pushEnabled: boolean;
            priorityOnly: boolean;
        } | undefined;
        analysisPreferences?: {
            enableSemanticAnalysis: boolean;
            minEngagementThreshold: number;
            keywordFilters: string[];
        } | undefined;
        apiPreferences?: {
            preferredModel: string;
            maxTokensPerRequest: number;
            temperatureSetting: number;
        } | undefined;
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        preferredResponseStyle: import("convex/values").VString<string | undefined, "optional">;
        autoApproveResponses: import("convex/values").VBoolean<boolean | undefined, "optional">;
        notificationPreferences: import("convex/values").VObject<{
            emailEnabled: boolean;
            pushEnabled: boolean;
            priorityOnly: boolean;
        } | undefined, {
            emailEnabled: import("convex/values").VBoolean<boolean, "required">;
            pushEnabled: import("convex/values").VBoolean<boolean, "required">;
            priorityOnly: import("convex/values").VBoolean<boolean, "required">;
        }, "optional", "emailEnabled" | "pushEnabled" | "priorityOnly">;
        analysisPreferences: import("convex/values").VObject<{
            enableSemanticAnalysis: boolean;
            minEngagementThreshold: number;
            keywordFilters: string[];
        } | undefined, {
            enableSemanticAnalysis: import("convex/values").VBoolean<boolean, "required">;
            minEngagementThreshold: import("convex/values").VFloat64<number, "required">;
            keywordFilters: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        }, "optional", "enableSemanticAnalysis" | "minEngagementThreshold" | "keywordFilters">;
        apiPreferences: import("convex/values").VObject<{
            preferredModel: string;
            maxTokensPerRequest: number;
            temperatureSetting: number;
        } | undefined, {
            preferredModel: import("convex/values").VString<string, "required">;
            maxTokensPerRequest: import("convex/values").VFloat64<number, "required">;
            temperatureSetting: import("convex/values").VFloat64<number, "required">;
        }, "optional", "preferredModel" | "maxTokensPerRequest" | "temperatureSetting">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "preferredResponseStyle" | "autoApproveResponses" | "notificationPreferences" | "analysisPreferences" | "apiPreferences" | "notificationPreferences.emailEnabled" | "notificationPreferences.pushEnabled" | "notificationPreferences.priorityOnly" | "analysisPreferences.enableSemanticAnalysis" | "analysisPreferences.minEngagementThreshold" | "analysisPreferences.keywordFilters" | "apiPreferences.preferredModel" | "apiPreferences.maxTokensPerRequest" | "apiPreferences.temperatureSetting">, {
        by_user: ["userId", "_creationTime"];
    }, {}, {}>;
    jobs: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId?: import("convex/values").GenericId<"users"> | undefined;
        metadata?: {
            description?: string | undefined;
            startedAt?: number | undefined;
            estimatedCompletion?: number | undefined;
            errorCount?: number | undefined;
            lastErrorMessage?: string | undefined;
        } | undefined;
        results?: {
            outputData?: string[] | undefined;
            errorCount: number;
            successCount: number;
        } | undefined;
        completedAt?: number | undefined;
        totalItems?: number | undefined;
        processedItems?: number | undefined;
        createdAt: number;
        updatedAt: number;
        status: "pending" | "failed" | "completed" | "running" | "cancelled";
        progress: number;
        jobType: string;
    }, {
        jobType: import("convex/values").VString<string, "required">;
        status: import("convex/values").VUnion<"pending" | "failed" | "completed" | "running" | "cancelled", [import("convex/values").VLiteral<"pending", "required">, import("convex/values").VLiteral<"running", "required">, import("convex/values").VLiteral<"completed", "required">, import("convex/values").VLiteral<"failed", "required">, import("convex/values").VLiteral<"cancelled", "required">], "required", never>;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users"> | undefined, "optional">;
        progress: import("convex/values").VFloat64<number, "required">;
        totalItems: import("convex/values").VFloat64<number | undefined, "optional">;
        processedItems: import("convex/values").VFloat64<number | undefined, "optional">;
        metadata: import("convex/values").VObject<{
            description?: string | undefined;
            startedAt?: number | undefined;
            estimatedCompletion?: number | undefined;
            errorCount?: number | undefined;
            lastErrorMessage?: string | undefined;
        } | undefined, {
            description: import("convex/values").VString<string | undefined, "optional">;
            startedAt: import("convex/values").VFloat64<number | undefined, "optional">;
            estimatedCompletion: import("convex/values").VFloat64<number | undefined, "optional">;
            errorCount: import("convex/values").VFloat64<number | undefined, "optional">;
            lastErrorMessage: import("convex/values").VString<string | undefined, "optional">;
        }, "optional", "description" | "startedAt" | "estimatedCompletion" | "errorCount" | "lastErrorMessage">;
        results: import("convex/values").VObject<{
            outputData?: string[] | undefined;
            errorCount: number;
            successCount: number;
        } | undefined, {
            successCount: import("convex/values").VFloat64<number, "required">;
            errorCount: import("convex/values").VFloat64<number, "required">;
            outputData: import("convex/values").VArray<string[] | undefined, import("convex/values").VString<string, "required">, "optional">;
        }, "optional", "errorCount" | "successCount" | "outputData">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
        completedAt: import("convex/values").VFloat64<number | undefined, "optional">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "metadata" | "status" | "progress" | "results" | "completedAt" | "jobType" | "totalItems" | "processedItems" | "metadata.description" | "metadata.startedAt" | "metadata.estimatedCompletion" | "metadata.errorCount" | "metadata.lastErrorMessage" | "results.errorCount" | "results.successCount" | "results.outputData">, {
        by_status: ["status", "_creationTime"];
        by_user: ["userId", "_creationTime"];
        by_type: ["jobType", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
        by_status_and_type: ["status", "jobType", "_creationTime"];
        by_user_and_status: ["userId", "status", "_creationTime"];
        by_type_and_created_at: ["jobType", "createdAt", "_creationTime"];
        by_status_and_created_at: ["status", "createdAt", "_creationTime"];
    }, {}, {}>;
    cache: import("convex/server").TableDefinition<import("convex/values").VObject<{
        metadata?: {
            lastAccessed?: number | undefined;
            hitCount?: number | undefined;
            computeTime?: number | undefined;
            source?: string | undefined;
        } | undefined;
        createdAt: number;
        expiresAt: number;
        priority: "high" | "medium" | "low";
        size: number;
        tags: string[];
        key: string;
        data: any;
    }, {
        key: import("convex/values").VString<string, "required">;
        data: import("convex/values").VAny<any, "required", string>;
        expiresAt: import("convex/values").VFloat64<number, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        tags: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        priority: import("convex/values").VUnion<"high" | "medium" | "low", [import("convex/values").VLiteral<"high", "required">, import("convex/values").VLiteral<"medium", "required">, import("convex/values").VLiteral<"low", "required">], "required", never>;
        size: import("convex/values").VFloat64<number, "required">;
        metadata: import("convex/values").VObject<{
            lastAccessed?: number | undefined;
            hitCount?: number | undefined;
            computeTime?: number | undefined;
            source?: string | undefined;
        } | undefined, {
            hitCount: import("convex/values").VFloat64<number | undefined, "optional">;
            lastAccessed: import("convex/values").VFloat64<number | undefined, "optional">;
            computeTime: import("convex/values").VFloat64<number | undefined, "optional">;
            source: import("convex/values").VString<string | undefined, "optional">;
        }, "optional", "lastAccessed" | "hitCount" | "computeTime" | "source">;
    }, "required", "createdAt" | "metadata" | "expiresAt" | "priority" | "size" | "tags" | "key" | "data" | "metadata.lastAccessed" | "metadata.hitCount" | "metadata.computeTime" | "metadata.source" | `data.${string}`>, {
        by_key: ["key", "_creationTime"];
        by_expiration: ["expiresAt", "_creationTime"];
        by_priority: ["priority", "_creationTime"];
        by_created_at: ["createdAt", "_creationTime"];
    }, {}, {}>;
    tweetStatsCache: import("convex/server").TableDefinition<import("convex/values").VObject<{
        expiresAt: number;
        cacheKey: string;
        stats: any;
        cachedAt: number;
    }, {
        cacheKey: import("convex/values").VString<string, "required">;
        stats: import("convex/values").VAny<any, "required", string>;
        cachedAt: import("convex/values").VFloat64<number, "required">;
        expiresAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "expiresAt" | "cacheKey" | "stats" | "cachedAt" | `stats.${string}`>, {
        by_cacheKey: ["cacheKey", "_creationTime"];
        by_expiresAt: ["expiresAt", "_creationTime"];
    }, {}, {}>;
    analyticsEvents: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId?: import("convex/values").GenericId<"users"> | undefined;
        clerkUserId?: string | undefined;
        sessionId?: string | undefined;
        path?: string | undefined;
        timestamp: number;
        eventName: string;
        properties: any;
    }, {
        eventName: import("convex/values").VString<string, "required">;
        timestamp: import("convex/values").VFloat64<number, "required">;
        userId: import("convex/values").VId<import("convex/values").GenericId<"users"> | undefined, "optional">;
        clerkUserId: import("convex/values").VString<string | undefined, "optional">;
        sessionId: import("convex/values").VString<string | undefined, "optional">;
        path: import("convex/values").VString<string | undefined, "optional">;
        properties: import("convex/values").VAny<any, "required", string>;
    }, "required", "userId" | "timestamp" | "eventName" | "clerkUserId" | "sessionId" | "path" | "properties" | `properties.${string}`>, {
        by_timestamp: ["timestamp", "_creationTime"];
        by_eventName: ["eventName", "_creationTime"];
        by_userId: ["userId", "_creationTime"];
        by_clerkUserId: ["clerkUserId", "_creationTime"];
    }, {}, {}>;
    aiCache: import("convex/server").TableDefinition<import("convex/values").VObject<{
        lastAccessed?: number | undefined;
        createdAt: number;
        content: string;
        model: string;
        tokensUsed: number;
        cost: number;
        lastUpdated: number;
        ttl: number;
        hitCount: number;
        contentHash: string;
        responseType: "sentiment_analysis" | "viral_detection" | "content_analysis" | "response_generation";
        aiResponse: any;
    }, {
        contentHash: import("convex/values").VString<string, "required">;
        content: import("convex/values").VString<string, "required">;
        responseType: import("convex/values").VUnion<"sentiment_analysis" | "viral_detection" | "content_analysis" | "response_generation", [import("convex/values").VLiteral<"sentiment_analysis", "required">, import("convex/values").VLiteral<"viral_detection", "required">, import("convex/values").VLiteral<"content_analysis", "required">, import("convex/values").VLiteral<"response_generation", "required">], "required", never>;
        aiResponse: import("convex/values").VAny<any, "required", string>;
        model: import("convex/values").VString<string, "required">;
        tokensUsed: import("convex/values").VFloat64<number, "required">;
        cost: import("convex/values").VFloat64<number, "required">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        lastUpdated: import("convex/values").VFloat64<number, "required">;
        lastAccessed: import("convex/values").VFloat64<number | undefined, "optional">;
        ttl: import("convex/values").VFloat64<number, "required">;
        hitCount: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "content" | "model" | "tokensUsed" | "cost" | "lastUpdated" | "ttl" | "lastAccessed" | "hitCount" | "contentHash" | "responseType" | "aiResponse" | `aiResponse.${string}`>, {
        by_hash: ["contentHash", "_creationTime"];
        by_type: ["responseType", "_creationTime"];
        by_created: ["createdAt", "_creationTime"];
        by_model: ["model", "_creationTime"];
        by_type_and_model: ["responseType", "model", "_creationTime"];
        by_model_and_created: ["model", "createdAt", "_creationTime"];
        by_type_and_created: ["responseType", "createdAt", "_creationTime"];
        by_last_accessed_and_hit_count: ["lastAccessed", "hitCount", "_creationTime"];
    }, {}, {}>;
    sentimentAnalysis: import("convex/server").TableDefinition<import("convex/values").VObject<{
        sentiment: "bullish" | "bearish" | "neutral";
        confidence: number;
        sentimentScore: number;
        reasoning: string;
        keyWords: string[];
        analysisModel: string;
        analyzedAt: number;
        mentionId: import("convex/values").GenericId<"mentions">;
    }, {
        mentionId: import("convex/values").VId<import("convex/values").GenericId<"mentions">, "required">;
        sentiment: import("convex/values").VUnion<"bullish" | "bearish" | "neutral", [import("convex/values").VLiteral<"bullish", "required">, import("convex/values").VLiteral<"bearish", "required">, import("convex/values").VLiteral<"neutral", "required">], "required", never>;
        sentimentScore: import("convex/values").VFloat64<number, "required">;
        confidence: import("convex/values").VFloat64<number, "required">;
        reasoning: import("convex/values").VString<string, "required">;
        keyWords: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        analysisModel: import("convex/values").VString<string, "required">;
        analyzedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "sentiment" | "confidence" | "sentimentScore" | "reasoning" | "keyWords" | "analysisModel" | "analyzedAt" | "mentionId">, {
        by_mention: ["mentionId", "_creationTime"];
        by_sentiment: ["sentiment", "_creationTime"];
        by_score: ["sentimentScore", "_creationTime"];
        by_analyzed_at: ["analyzedAt", "_creationTime"];
    }, {}, {}>;
    marketSentiment: import("convex/server").TableDefinition<import("convex/values").VObject<{
        bullishScore: number;
        bearishScore: number;
        neutralScore: number;
        marketContext: string[];
        analyzedAt: number;
        mentionId: import("convex/values").GenericId<"mentions">;
    }, {
        mentionId: import("convex/values").VId<import("convex/values").GenericId<"mentions">, "required">;
        bullishScore: import("convex/values").VFloat64<number, "required">;
        bearishScore: import("convex/values").VFloat64<number, "required">;
        neutralScore: import("convex/values").VFloat64<number, "required">;
        marketContext: import("convex/values").VArray<string[], import("convex/values").VString<string, "required">, "required">;
        analyzedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "bullishScore" | "bearishScore" | "neutralScore" | "marketContext" | "analyzedAt" | "mentionId">, {
        by_mention: ["mentionId", "_creationTime"];
        by_bullish_score: ["bullishScore", "_creationTime"];
        by_bearish_score: ["bearishScore", "_creationTime"];
    }, {}, {}>;
    emotionalAnalysis: import("convex/server").TableDefinition<import("convex/values").VObject<{
        analyzedAt: number;
        mentionId: import("convex/values").GenericId<"mentions">;
        score: number;
        emotionType: "excitement" | "fear" | "greed" | "fomo" | "panic";
    }, {
        mentionId: import("convex/values").VId<import("convex/values").GenericId<"mentions">, "required">;
        emotionType: import("convex/values").VUnion<"excitement" | "fear" | "greed" | "fomo" | "panic", [import("convex/values").VLiteral<"excitement", "required">, import("convex/values").VLiteral<"fear", "required">, import("convex/values").VLiteral<"greed", "required">, import("convex/values").VLiteral<"fomo", "required">, import("convex/values").VLiteral<"panic", "required">], "required", never>;
        score: import("convex/values").VFloat64<number, "required">;
        analyzedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "analyzedAt" | "mentionId" | "score" | "emotionType">, {
        by_mention: ["mentionId", "_creationTime"];
        by_emotion_type: ["emotionType", "_creationTime"];
        by_score: ["score", "_creationTime"];
    }, {}, {}>;
    bandwidthLogs: import("convex/server").TableDefinition<import("convex/values").VObject<{
        optimizationType?: string | undefined;
        estimatedSavings?: number | undefined;
        timestamp: number;
        operation: string;
        bytesRead: number;
        executionTime: number;
        recordsScanned: number;
        recordsReturned: number;
        cacheHit: boolean;
    }, {
        operation: import("convex/values").VString<string, "required">;
        bytesRead: import("convex/values").VFloat64<number, "required">;
        executionTime: import("convex/values").VFloat64<number, "required">;
        recordsScanned: import("convex/values").VFloat64<number, "required">;
        recordsReturned: import("convex/values").VFloat64<number, "required">;
        cacheHit: import("convex/values").VBoolean<boolean, "required">;
        optimizationType: import("convex/values").VString<string | undefined, "optional">;
        estimatedSavings: import("convex/values").VFloat64<number | undefined, "optional">;
        timestamp: import("convex/values").VFloat64<number, "required">;
    }, "required", "timestamp" | "operation" | "bytesRead" | "executionTime" | "recordsScanned" | "recordsReturned" | "cacheHit" | "optimizationType" | "estimatedSavings">, {
        by_timestamp: ["timestamp", "_creationTime"];
        by_operation: ["operation", "_creationTime"];
        by_optimization_type: ["optimizationType", "_creationTime"];
        by_operation_and_timestamp: ["operation", "timestamp", "_creationTime"];
        by_cache_hit_and_timestamp: ["cacheHit", "timestamp", "_creationTime"];
        by_operation_and_cache_hit: ["operation", "cacheHit", "_creationTime"];
    }, {}, {}>;
    subscriptions: import("convex/server").TableDefinition<import("convex/values").VObject<{
        metadata?: {
            stripeCustomerId?: string | undefined;
            stripeSubscriptionId?: string | undefined;
            lastPaymentDate?: number | undefined;
            nextBillingDate?: number | undefined;
        } | undefined;
        trialEnd?: number | undefined;
        createdAt: number;
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        status: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
        clerkSubscriptionId: string;
        planId: "starter" | "pro" | "enterprise";
        currentPeriodStart: number;
        currentPeriodEnd: number;
        cancelAtPeriodEnd: boolean;
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        clerkSubscriptionId: import("convex/values").VString<string, "required">;
        planId: import("convex/values").VUnion<"starter" | "pro" | "enterprise", [import("convex/values").VLiteral<"starter", "required">, import("convex/values").VLiteral<"pro", "required">, import("convex/values").VLiteral<"enterprise", "required">], "required", never>;
        status: import("convex/values").VUnion<"active" | "canceled" | "past_due" | "unpaid" | "incomplete", [import("convex/values").VLiteral<"active", "required">, import("convex/values").VLiteral<"canceled", "required">, import("convex/values").VLiteral<"past_due", "required">, import("convex/values").VLiteral<"unpaid", "required">, import("convex/values").VLiteral<"incomplete", "required">], "required", never>;
        currentPeriodStart: import("convex/values").VFloat64<number, "required">;
        currentPeriodEnd: import("convex/values").VFloat64<number, "required">;
        cancelAtPeriodEnd: import("convex/values").VBoolean<boolean, "required">;
        trialEnd: import("convex/values").VFloat64<number | undefined, "optional">;
        metadata: import("convex/values").VObject<{
            stripeCustomerId?: string | undefined;
            stripeSubscriptionId?: string | undefined;
            lastPaymentDate?: number | undefined;
            nextBillingDate?: number | undefined;
        } | undefined, {
            stripeCustomerId: import("convex/values").VString<string | undefined, "optional">;
            stripeSubscriptionId: import("convex/values").VString<string | undefined, "optional">;
            lastPaymentDate: import("convex/values").VFloat64<number | undefined, "optional">;
            nextBillingDate: import("convex/values").VFloat64<number | undefined, "optional">;
        }, "optional", "stripeCustomerId" | "stripeSubscriptionId" | "lastPaymentDate" | "nextBillingDate">;
        createdAt: import("convex/values").VFloat64<number, "required">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "createdAt" | "updatedAt" | "userId" | "metadata" | "status" | "clerkSubscriptionId" | "planId" | "currentPeriodStart" | "currentPeriodEnd" | "cancelAtPeriodEnd" | "trialEnd" | "metadata.stripeCustomerId" | "metadata.stripeSubscriptionId" | "metadata.lastPaymentDate" | "metadata.nextBillingDate">, {
        by_user: ["userId", "_creationTime"];
        by_clerk_subscription: ["clerkSubscriptionId", "_creationTime"];
        by_plan: ["planId", "_creationTime"];
        by_status: ["status", "_creationTime"];
        by_period_end: ["currentPeriodEnd", "_creationTime"];
        by_user_and_status: ["userId", "status", "_creationTime"];
        by_plan_and_status: ["planId", "status", "_creationTime"];
        by_status_and_period_end: ["status", "currentPeriodEnd", "_creationTime"];
    }, {}, {}>;
    usageTracking: import("convex/server").TableDefinition<import("convex/values").VObject<{
        userId: import("convex/values").GenericId<"users">;
        date: string;
        lastUpdated: number;
        planId: "starter" | "pro" | "enterprise";
        usage: {
            aiResponses: number;
            imageGenerations: number;
            apiRequests: number;
            bulkOperations: number;
            premiumAiCalls: number;
            analyticsQueries: number;
        };
        limits: {
            aiResponses: number;
            imageGenerations: number;
            apiRequests: number;
            bulkOperations: number;
            premiumAiCalls: number;
            analyticsQueries: number;
        };
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        date: import("convex/values").VString<string, "required">;
        planId: import("convex/values").VUnion<"starter" | "pro" | "enterprise", [import("convex/values").VLiteral<"starter", "required">, import("convex/values").VLiteral<"pro", "required">, import("convex/values").VLiteral<"enterprise", "required">], "required", never>;
        usage: import("convex/values").VObject<{
            aiResponses: number;
            imageGenerations: number;
            apiRequests: number;
            bulkOperations: number;
            premiumAiCalls: number;
            analyticsQueries: number;
        }, {
            aiResponses: import("convex/values").VFloat64<number, "required">;
            imageGenerations: import("convex/values").VFloat64<number, "required">;
            apiRequests: import("convex/values").VFloat64<number, "required">;
            bulkOperations: import("convex/values").VFloat64<number, "required">;
            premiumAiCalls: import("convex/values").VFloat64<number, "required">;
            analyticsQueries: import("convex/values").VFloat64<number, "required">;
        }, "required", "aiResponses" | "imageGenerations" | "apiRequests" | "bulkOperations" | "premiumAiCalls" | "analyticsQueries">;
        limits: import("convex/values").VObject<{
            aiResponses: number;
            imageGenerations: number;
            apiRequests: number;
            bulkOperations: number;
            premiumAiCalls: number;
            analyticsQueries: number;
        }, {
            aiResponses: import("convex/values").VFloat64<number, "required">;
            imageGenerations: import("convex/values").VFloat64<number, "required">;
            apiRequests: import("convex/values").VFloat64<number, "required">;
            bulkOperations: import("convex/values").VFloat64<number, "required">;
            premiumAiCalls: import("convex/values").VFloat64<number, "required">;
            analyticsQueries: import("convex/values").VFloat64<number, "required">;
        }, "required", "aiResponses" | "imageGenerations" | "apiRequests" | "bulkOperations" | "premiumAiCalls" | "analyticsQueries">;
        lastUpdated: import("convex/values").VFloat64<number, "required">;
    }, "required", "userId" | "date" | "lastUpdated" | "planId" | "usage" | "limits" | "usage.aiResponses" | "usage.imageGenerations" | "usage.apiRequests" | "usage.bulkOperations" | "usage.premiumAiCalls" | "usage.analyticsQueries" | "limits.aiResponses" | "limits.imageGenerations" | "limits.apiRequests" | "limits.bulkOperations" | "limits.premiumAiCalls" | "limits.analyticsQueries">, {
        by_user_date: ["userId", "date", "_creationTime"];
        by_date: ["date", "_creationTime"];
        by_plan: ["planId", "_creationTime"];
        by_last_updated: ["lastUpdated", "_creationTime"];
        by_plan_and_date: ["planId", "date", "_creationTime"];
        by_user_and_last_updated: ["userId", "lastUpdated", "_creationTime"];
    }, {}, {}>;
    featureAccess: import("convex/server").TableDefinition<import("convex/values").VObject<{
        updatedAt: number;
        userId: import("convex/values").GenericId<"users">;
        planId: "starter" | "pro" | "enterprise";
        limits: {
            maxAccounts: number;
            maxAiResponses: number;
            maxImageGenerations: number;
            maxApiRequests: number;
            maxBulkOperations: number;
        };
        features: {
            basicMonitoring: boolean;
            premiumAi: boolean;
            imageGeneration: boolean;
            bulkProcessing: boolean;
            advancedAnalytics: boolean;
            prioritySupport: boolean;
            customIntegrations: boolean;
            whiteLabel: boolean;
        };
    }, {
        userId: import("convex/values").VId<import("convex/values").GenericId<"users">, "required">;
        planId: import("convex/values").VUnion<"starter" | "pro" | "enterprise", [import("convex/values").VLiteral<"starter", "required">, import("convex/values").VLiteral<"pro", "required">, import("convex/values").VLiteral<"enterprise", "required">], "required", never>;
        features: import("convex/values").VObject<{
            basicMonitoring: boolean;
            premiumAi: boolean;
            imageGeneration: boolean;
            bulkProcessing: boolean;
            advancedAnalytics: boolean;
            prioritySupport: boolean;
            customIntegrations: boolean;
            whiteLabel: boolean;
        }, {
            basicMonitoring: import("convex/values").VBoolean<boolean, "required">;
            premiumAi: import("convex/values").VBoolean<boolean, "required">;
            imageGeneration: import("convex/values").VBoolean<boolean, "required">;
            bulkProcessing: import("convex/values").VBoolean<boolean, "required">;
            advancedAnalytics: import("convex/values").VBoolean<boolean, "required">;
            prioritySupport: import("convex/values").VBoolean<boolean, "required">;
            customIntegrations: import("convex/values").VBoolean<boolean, "required">;
            whiteLabel: import("convex/values").VBoolean<boolean, "required">;
        }, "required", "basicMonitoring" | "premiumAi" | "imageGeneration" | "bulkProcessing" | "advancedAnalytics" | "prioritySupport" | "customIntegrations" | "whiteLabel">;
        limits: import("convex/values").VObject<{
            maxAccounts: number;
            maxAiResponses: number;
            maxImageGenerations: number;
            maxApiRequests: number;
            maxBulkOperations: number;
        }, {
            maxAccounts: import("convex/values").VFloat64<number, "required">;
            maxAiResponses: import("convex/values").VFloat64<number, "required">;
            maxImageGenerations: import("convex/values").VFloat64<number, "required">;
            maxApiRequests: import("convex/values").VFloat64<number, "required">;
            maxBulkOperations: import("convex/values").VFloat64<number, "required">;
        }, "required", "maxAccounts" | "maxAiResponses" | "maxImageGenerations" | "maxApiRequests" | "maxBulkOperations">;
        updatedAt: import("convex/values").VFloat64<number, "required">;
    }, "required", "updatedAt" | "userId" | "planId" | "limits" | "features" | "limits.maxAccounts" | "limits.maxAiResponses" | "limits.maxImageGenerations" | "limits.maxApiRequests" | "limits.maxBulkOperations" | "features.basicMonitoring" | "features.premiumAi" | "features.imageGeneration" | "features.bulkProcessing" | "features.advancedAnalytics" | "features.prioritySupport" | "features.customIntegrations" | "features.whiteLabel">, {
        by_user: ["userId", "_creationTime"];
        by_plan: ["planId", "_creationTime"];
    }, {}, {}>;
}, true>;
export default _default;
