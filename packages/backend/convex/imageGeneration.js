import { action } from "./_generated/server";
import { v } from "convex/values";
import { getOpenAIClient } from "./lib/openai_client";
/**
 * Generate images using OpenAI's DALL-E models
 */
export const generateImage = action({
    args: {
        prompt: v.string(),
        model: v.optional(v.union(v.literal("dall-e-2"), v.literal("dall-e-3"))),
        size: v.optional(v.union(v.literal("256x256"), v.literal("512x512"), v.literal("1024x1024"), v.literal("1792x1024"), v.literal("1024x1792"))),
        quality: v.optional(v.union(v.literal("standard"), v.literal("hd"))),
        style: v.optional(v.union(v.literal("vivid"), v.literal("natural"))),
        responseFormat: v.optional(v.union(v.literal("url"), v.literal("b64_json"))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            const response = await client.generateImage(args.prompt, {
                model: args.model || 'dall-e-3',
                size: args.size || '1024x1024',
                quality: args.quality || 'hd',
                style: args.style || 'vivid',
                responseFormat: args.responseFormat || 'url',
                user: args.userId,
            });
            return {
                ...response,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Image generation action failed:', error);
            throw new Error(`Image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate social media optimized images
 */
export const generateSocialMediaImage = action({
    args: {
        description: v.string(),
        platform: v.optional(v.union(v.literal("twitter"), v.literal("instagram"), v.literal("linkedin"))),
        style: v.optional(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic"))),
        includeText: v.optional(v.string()),
        aspectRatio: v.optional(v.union(v.literal("square"), v.literal("landscape"), v.literal("portrait"))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            const response = await client.generateSocialMediaImage(args.description, {
                platform: args.platform,
                style: args.style,
                includeText: args.includeText,
                aspectRatio: args.aspectRatio,
            });
            return {
                ...response,
                platform: args.platform,
                style: args.style,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Social media image generation failed:', error);
            throw new Error(`Social media image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate response with accompanying image using OpenAI Responses API
 */
export const generateResponseWithImage = action({
    args: {
        prompt: v.string(),
        systemPrompt: v.optional(v.string()),
        imagePrompt: v.optional(v.string()),
        maxTokens: v.optional(v.number()),
        temperature: v.optional(v.number()),
        imageModel: v.optional(v.literal("gpt-image-1")),
        imageSize: v.optional(v.union(v.literal("256x256"), v.literal("512x512"), v.literal("1024x1024"), v.literal("1792x1024"), v.literal("1024x1792"))),
        imageQuality: v.optional(v.union(v.literal("standard"), v.literal("hd"))),
        imageStyle: v.optional(v.union(v.literal("vivid"), v.literal("natural"))),
        responseType: v.optional(v.string()),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            const response = await client.generateResponseWithImage(args.prompt, {
                systemPrompt: args.systemPrompt,
                maxTokens: args.maxTokens,
                temperature: args.temperature,
                imagePrompt: args.imagePrompt,
                imageModel: args.imageModel,
                imageSize: args.imageSize,
                imageQuality: args.imageQuality,
                imageStyle: args.imageStyle,
            });
            return {
                ...response,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Response with image generation failed:', error);
            throw new Error(`Response with image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate tweet with contextual image
 */
export const generateTweetWithImage = action({
    args: {
        tweetContent: v.string(),
        tweetContext: v.optional(v.object({
            authorHandle: v.optional(v.string()),
            authorDisplayName: v.optional(v.string()),
            authorIsVerified: v.optional(v.boolean()),
            engagement: v.optional(v.object({
                likes: v.number(),
                retweets: v.number(),
                replies: v.number(),
            })),
        })),
        imageStyle: v.optional(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic"))),
        responseStyle: v.optional(v.string()),
        userContext: v.optional(v.object({
            expertise: v.optional(v.array(v.string())),
            interests: v.optional(v.array(v.string())),
            writingStyle: v.optional(v.string()),
            brand: v.optional(v.string()),
        })),
        maxLength: v.optional(v.number()),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            // Build context for both text and image generation
            const contextInfo = args.tweetContext ?
                `Tweet by ${args.tweetContext.authorDisplayName || 'unknown'} (@${args.tweetContext.authorHandle || 'unknown'})` :
                'Tweet content';
            const userExpertise = args.userContext?.expertise?.join(', ') || 'general topics';
            const userBrand = args.userContext?.brand || 'professional brand';
            const systemPrompt = `You are an AI assistant helping to create engaging social media responses with accompanying images. 
Context: Responding to ${contextInfo}
User expertise: ${userExpertise}
Brand voice: ${userBrand}
Response style: ${args.responseStyle || 'professional'}

Generate a compelling response that would work well with a visual image. Consider what kind of image would enhance the message.
If an image would add value, suggest it in your response or use the image generation tool.`;
            const prompt = `Create an engaging response to this tweet: "${args.tweetContent}"

Response requirements:
- Maximum ${args.maxLength || 280} characters
- Style: ${args.responseStyle || 'professional'}
- Should complement a visual image if appropriate
- Match the user's expertise and brand voice

Consider generating an image that would enhance this response visually.`;
            const response = await client.generateResponseWithImage(prompt, {
                systemPrompt,
                maxTokens: 150, // Shorter for tweet responses
                temperature: 0.8,
                imageSize: '1792x1024', // Twitter-optimized landscape
                imageQuality: 'hd',
                imageStyle: 'vivid',
            });
            // Extract text content
            let textContent = '';
            let imageData = null;
            for (const content of response.content) {
                if (content.type === 'text' && content.text) {
                    textContent = content.text.trim();
                }
                else if (content.type === 'image' && content.image) {
                    imageData = content.image;
                }
            }
            // Clean up text content
            textContent = textContent
                .replace(/^(Reply:|Response:|Tweet:)\s*/i, '')
                .replace(/^["']|["']$/g, '')
                .trim();
            return {
                textContent,
                characterCount: textContent.length,
                imageData,
                hasImage: !!imageData,
                responseStyle: args.responseStyle || 'professional',
                imageStyle: args.imageStyle,
                model: response.model,
                generatedAt: Date.now(),
                estimatedEngagement: {
                    likes: Math.floor(Math.random() * 30) + 10, // Higher with images
                    retweets: Math.floor(Math.random() * 15) + 5,
                    replies: Math.floor(Math.random() * 10) + 2,
                },
            };
        }
        catch (error) {
            console.error('Tweet with image generation failed:', error);
            throw new Error(`Tweet with image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate image for existing response content
 */
export const generateImageForResponse = action({
    args: {
        responseContent: v.string(),
        responseContext: v.optional(v.string()),
        imageStyle: v.optional(v.union(v.literal("minimal"), v.literal("vibrant"), v.literal("professional"), v.literal("artistic"))),
        platform: v.optional(v.union(v.literal("twitter"), v.literal("instagram"), v.literal("linkedin"))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            // Create an image prompt based on the response content
            let imagePrompt = `Create a visually appealing image that complements this social media response: "${args.responseContent}"`;
            if (args.responseContext) {
                imagePrompt += ` Context: ${args.responseContext}`;
            }
            // Add style and platform specific instructions
            imagePrompt += '. The image should be eye-catching, relevant, and suitable for social media sharing.';
            if (args.imageStyle) {
                const styleInstructions = {
                    minimal: 'Use a clean, minimal design with plenty of white space',
                    vibrant: 'Use vibrant colors and dynamic composition',
                    professional: 'Use professional, business-appropriate styling',
                    artistic: 'Use creative, artistic elements and unique perspectives',
                };
                imagePrompt += ` ${styleInstructions[args.imageStyle]}.`;
            }
            const response = await client.generateSocialMediaImage(imagePrompt, {
                platform: args.platform,
                style: args.imageStyle,
                aspectRatio: args.platform === 'twitter' ? 'landscape' : 'square',
            });
            return {
                ...response,
                responseContent: args.responseContent,
                platform: args.platform,
                style: args.imageStyle,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Image generation for response failed:', error);
            throw new Error(`Image generation for response failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Generate multiple image variations for a single prompt
 */
export const generateImageVariations = action({
    args: {
        prompt: v.string(),
        count: v.optional(v.number()),
        model: v.optional(v.union(v.literal("dall-e-2"), v.literal("dall-e-3"))),
        size: v.optional(v.union(v.literal("256x256"), v.literal("512x512"), v.literal("1024x1024"), v.literal("1792x1024"), v.literal("1024x1792"))),
        styles: v.optional(v.array(v.union(v.literal("vivid"), v.literal("natural")))),
        userId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            const count = Math.min(args.count || 3, 5); // Limit to 5 variations max
            const styles = args.styles || ['vivid', 'natural'];
            const variations = [];
            for (let i = 0; i < count; i++) {
                try {
                    const style = styles[i % styles.length];
                    const variation = await client.generateImage(args.prompt, {
                        model: args.model || 'dall-e-3',
                        size: args.size || '1024x1024',
                        quality: 'hd',
                        style,
                        responseFormat: 'url',
                        user: args.userId,
                    });
                    variations.push({
                        ...variation,
                        variationIndex: i,
                        style,
                    });
                }
                catch (variationError) {
                    console.error(`Variation ${i} failed:`, variationError);
                    // Continue with other variations
                }
            }
            if (variations.length === 0) {
                throw new Error('All image variations failed to generate');
            }
            return {
                variations,
                originalPrompt: args.prompt,
                totalCount: variations.length,
                requestedCount: count,
                generatedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Image variations generation failed:', error);
            throw new Error(`Image variations generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Test image generation functionality
 */
export const testImageGeneration = action({
    args: {
        testPrompt: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenAIClient();
            const testPrompt = args.testPrompt || "A simple test image of a colorful abstract pattern";
            // Test basic connection first
            const connectionTest = await client.testConnection();
            if (!connectionTest) {
                throw new Error('OpenAI connection test failed');
            }
            // Test image generation
            const imageResponse = await client.generateImage(testPrompt, {
                model: 'dall-e-3',
                size: '1024x1024',
                quality: 'standard',
                style: 'natural',
                responseFormat: 'url',
            });
            return {
                success: true,
                connectionTest: true,
                imageGeneration: !!imageResponse.url,
                imageUrl: imageResponse.url,
                testPrompt,
                model: imageResponse.model,
                testedAt: Date.now(),
            };
        }
        catch (error) {
            console.error('Image generation test failed:', error);
            return {
                success: false,
                connectionTest: false,
                imageGeneration: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                testPrompt: args.testPrompt,
                testedAt: Date.now(),
            };
        }
    },
});
