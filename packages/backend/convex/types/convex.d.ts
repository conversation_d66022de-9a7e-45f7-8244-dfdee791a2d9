/**
 * Common Convex type definitions and utilities
 */
import { Id } from "../_generated/dataModel";
export declare const jobStatusValidator: import("convex/values").VUnion<"pending" | "failed" | "completed" | "running" | "cancelled", [import("convex/values").VLiteral<"pending", "required">, import("convex/values").VLiteral<"running", "required">, import("convex/values").VLiteral<"completed", "required">, import("convex/values").VLiteral<"failed", "required">, import("convex/values").VLiteral<"cancelled", "required">], "required", never>;
export declare const priorityValidator: import("convex/values").VUnion<"high" | "medium" | "low", [import("convex/values").VLiteral<"high", "required">, import("convex/values").VLiteral<"medium", "required">, import("convex/values").VLiteral<"low", "required">], "required", never>;
export declare const mentionTypeValidator: import("convex/values").VUnion<"mention" | "reply" | "quote", [import("convex/values").VLiteral<"mention", "required">, import("convex/values").VLiteral<"reply", "required">, import("convex/values").VLiteral<"quote", "required">], "required", never>;
export declare const searchTypeValidator: import("convex/values").VUnion<any, any, "required", any>;
export declare const engagementValidator: import("convex/values").VObject<any, any, "required", any>;
export declare const jobMetadataValidator: import("convex/values").VObject<any, any, "required", any>;
export declare const jobResultsValidator: import("convex/values").VObject<any, any, "required", any>;
export declare const searchMetadataValidator: import("convex/values").VObject<any, any, "required", any>;
export interface JobStatus {
    jobType: string;
    status: "pending" | "running" | "completed" | "failed" | "cancelled";
    userId?: Id<"users">;
    progress: number;
    totalItems?: number;
    processedItems?: number;
    metadata?: {
        description?: string;
        startedAt?: number;
        estimatedCompletion?: number;
        errorCount?: number;
        lastErrorMessage?: string;
    };
    results?: {
        successCount: number;
        errorCount: number;
        outputData?: string[];
    };
    createdAt: number;
    updatedAt: number;
    completedAt?: number;
}
export interface SearchResult {
    query: string;
    searchType: "xai" | "tweetio" | "hybrid";
    content: string;
    citations: string[];
    metadata?: {
        responseTime?: number;
        success?: boolean;
        insights?: string[];
        tokensUsed?: number;
        model?: string;
    };
    userId?: Id<"users">;
    createdAt: number;
}
export interface MentionData {
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    mentionAuthorFollowers?: number;
    mentionAuthorVerified?: boolean;
    monitoredAccountId: Id<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote";
    originalTweetId?: string;
    engagement: {
        likes: number;
        retweets: number;
        replies: number;
        views?: number;
    };
    priority: "high" | "medium" | "low";
    url: string;
    createdAt: number;
}
export interface AuthContext {
    auth: {
        getUserIdentity(): Promise<{
            subject: string;
        } | null>;
    };
    db: {
        query(table: string): any;
        insert(table: string, document: any): Promise<Id<any>>;
        patch(id: Id<any>, fields: any): Promise<void>;
    };
}
export type UserIdResult = Id<"users"> | null;
export type DatabaseQueryResult<T> = T | null;
export type MutationResult<T> = Promise<T>;
export type QueryResult<T> = Promise<T>;
