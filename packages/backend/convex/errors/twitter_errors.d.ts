/**
 * Error handling for Twitter API integration
 */
import type { RateLimitInfo } from "../types/twitter";
export declare class TweetIOError extends Error {
    statusCode?: number | undefined;
    rateLimitInfo?: RateLimitInfo | undefined;
    constructor(message: string, statusCode?: number | undefined, rateLimitInfo?: RateLimitInfo | undefined);
}
/**
 * Check if an error is a rate limiting error
 */
export declare function isRateLimitError(error: unknown): error is TweetIOError;
/**
 * Check if an error is a authentication error
 */
export declare function isAuthError(error: unknown): error is TweetIOError;
/**
 * Check if an error is a not found error
 */
export declare function isNotFoundError(error: unknown): error is TweetIOError;
/**
 * Check if an error should be retried
 */
export declare function shouldRetryError(error: unknown): boolean;
/**
 * Parse error response from TwitterAPI.io
 */
export declare function parseTwitterApiError(response: Response, errorText: string): {
    message: string;
    code?: number;
};
/**
 * Format rate limit reset time for user display
 */
export declare function formatRateLimitReset(resetTimestamp: number): string;
/**
 * Calculate time until rate limit reset
 */
export declare function getTimeUntilReset(resetTimestamp: number): number;
