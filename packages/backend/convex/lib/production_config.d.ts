/**
 * Production Configuration Validator
 * 🔐 SECURITY: Validates all required environment variables and enforces production security standards
 */
interface EnvironmentConfig {
    NODE_ENV: string;
    TWITTERAPI_IO_API_KEY: string;
    OPENROUTER_API_KEY: string;
    CONVEX_DEPLOYMENT?: string;
}
interface ValidationResult {
    isValid: boolean;
    missing: string[];
    warnings: string[];
}
/**
 * Validates that all required environment variables are present and properly configured
 */
export declare function validateEnvironmentConfig(): ValidationResult;
/**
 * Throws an error if environment validation fails (for startup validation)
 */
export declare function validateEnvironmentOrThrow(): void;
/**
 * Safe way to get environment variables with fallbacks
 */
export declare function getEnvironmentConfig(): EnvironmentConfig;
/**
 * Check if we're running in production
 */
export declare function isProduction(): boolean;
/**
 * Check if we're running in development
 */
export declare function isDevelopment(): boolean;
/**
 * Get a masked version of an API key for logging (shows first 4 and last 4 characters)
 */
export declare function maskApiKey(apiKey: string): string;
/**
 * Production security configuration
 */
export declare const PRODUCTION_SECURITY_CONFIG: {
    readonly DEFAULT_RATE_LIMIT: 100;
    readonly EXPENSIVE_OPERATION_RATE_LIMIT: 10;
    readonly MAX_XAI_SEARCHES_PER_DAY: 50;
    readonly MAX_TWITTER_REQUESTS_PER_HOUR: 100;
    readonly MAX_QUERY_LENGTH: 500;
    readonly MAX_CONTENT_LENGTH: 50000;
    readonly MAX_BATCH_SIZE: 10;
    readonly API_TIMEOUT_MS: 30000;
    readonly DATABASE_TIMEOUT_MS: 10000;
};
/**
 * Development vs Production feature flags
 */
export declare const FEATURE_FLAGS: {
    readonly ENABLE_DEBUG_LOGGING: boolean;
    readonly ENABLE_DETAILED_ERRORS: boolean;
    readonly ENABLE_PERFORMANCE_MONITORING: boolean;
    readonly ENABLE_STRICT_VALIDATION: boolean;
};
export {};
