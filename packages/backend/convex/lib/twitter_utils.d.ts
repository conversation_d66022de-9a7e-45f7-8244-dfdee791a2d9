/**
 * Utility functions for Twitter API integration
 */
import type { <PERSON><PERSON><PERSON>, TwitterU<PERSON>, <PERSON><PERSON><PERSON>weet, NormalizedTweet, MentionPriority } from "../types/twitter";
/**
 * Extract Twitter username from various URL formats
 */
export declare function extractUsernameFromUrl(url: string): string | null;
/**
 * Extract tweet ID from Twitter URL
 */
export declare function extractTweetIdFromUrl(url: string): string | null;
/**
 * Validate Twitter URL format
 */
export declare function isValidTwitterUrl(url: string): boolean;
/**
 * Clean username by removing @ prefix
 */
export declare function cleanUsername(username: string): string;
/**
 * Add @ prefix to username if not present
 */
export declare function addAtPrefix(username: string): string;
/**
 * Normalize Twitter data for database storage
 */
export declare function normalizeTwitterTweet(tweet: TwitterTweet, users: TwitterUser[]): NormalizedTweet;
/**
 * Calculate mention priority based on user metrics
 */
export declare function calculateMentionPriority(user: TwitterUser): MentionPriority;
/**
 * Convert TwitterIO tweet format to standard format
 */
export declare function convertTwitterIOTweet(tweet: Twitter<PERSON><PERSON>weet): TwitterTweet;
/**
 * Convert TwitterIO author to standard user format
 */
export declare function convertTwitterIOUser(author: <PERSON><PERSON>Tweet["author"]): TwitterUser | null;
/**
 * Build Twitter reply URL with pre-filled text
 */
export declare function buildTwitterReplyUrl(tweetId: string, replyText: string): string;
/**
 * Build Twitter URL from username and tweet ID
 */
export declare function buildTwitterUrl(username: string, tweetId: string): string;
/**
 * Extract hashtags from tweet text
 */
export declare function extractHashtags(text: string): string[];
/**
 * Extract mentions from tweet text
 */
export declare function extractMentions(text: string): string[];
/**
 * Extract URLs from tweet text
 */
export declare function extractUrls(text: string): string[];
/**
 * Calculate engagement rate for a tweet
 */
export declare function calculateEngagementRate(tweet: TwitterTweet): number;
/**
 * Determine if a tweet is potentially viral
 */
export declare function isViralTweet(tweet: TwitterTweet): boolean;
/**
 * Format large numbers for display (e.g., 1.2K, 3.4M)
 */
export declare function formatNumber(num: number): string;
/**
 * Format timestamp for display
 */
export declare function formatTimestamp(timestamp: number): string;
/**
 * Remove duplicates from array of Twitter users
 */
export declare function deduplicateUsers(users: TwitterUser[]): TwitterUser[];
/**
 * Remove duplicates from array of Twitter tweets
 */
export declare function deduplicateTweets(tweets: TwitterTweet[]): TwitterTweet[];
/**
 * PHASE 1: USERNAME FALLBACK UTILITIES
 * These functions provide comprehensive fallback strategies for missing username data
 */
/**
 * Extract username from tweet text mentions
 * Looks for @username patterns in the tweet content
 */
export declare function extractUsernameFromTweetText(tweetText: string, authorId: string): string | null;
/**
 * Generate fallback username from user ID
 * Creates a valid username when no other data is available
 */
export declare function generateFallbackUsername(userId: string, userDisplayName?: string): string;
/**
 * Extract username from tweet entities (mentions)
 * Looks for username in tweet entities.mentions array
 */
export declare function extractUsernameFromEntities(tweet: TwitterTweet): string | null;
/**
 * Comprehensive username resolution with multiple fallback strategies
 * This is the main function to use when username is missing
 */
export declare function resolveUsername(options: {
    user?: TwitterUser | null;
    tweet?: TwitterTweet;
    authorId: string;
    displayName?: string;
}): string;
/**
 * Validate and sanitize mention data before storage
 * Ensures all required fields are present and valid
 */
export declare function sanitizeMentionData(mentionData: any): {
    isValid: boolean;
    sanitized: any;
    warnings: string[];
};
/**
 * Enhanced convertTwitterIOUser with fallback username resolution
 */
export declare function convertTwitterIOUserWithFallback(author: TwitterIOTweet["author"], tweet?: TwitterTweet): TwitterUser | null;
/**
 * PHASE 2: Enhanced author resolution with API lookup fallback
 * When author data is missing, attempts to fetch it from TwitterAPI.io
 */
export declare function resolveAuthorWithApiLookup(options: {
    authorId: string;
    existingAuthor?: TwitterUser | null;
    tweet?: TwitterTweet;
    twitterClient?: any;
}): Promise<{
    author: TwitterUser;
    username: string;
    name: string;
    wasResolved: boolean;
}>;
/**
 * PHASE 2: Batch resolve multiple missing authors
 * Efficiently resolves multiple missing authors in a single operation
 */
export declare function batchResolveAuthors(options: {
    missingAuthorIds: string[];
    twitterClient?: any;
    maxBatchSize?: number;
}): Promise<Map<string, TwitterUser>>;
