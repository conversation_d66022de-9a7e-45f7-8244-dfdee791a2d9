/**
 * x<PERSON><PERSON> Grok Live Search Client for BuddyChip Pro
 *
 * This client provides xAI's native Live Search capabilities including:
 * - Real-time X/Twitter search and monitoring
 * - Multi-source data integration (web, X, news, RSS)
 * - AI-powered relevance scoring and analysis
 * - Citation tracking and source verification
 */
export interface XAISearchParameters {
    mode: "auto" | "on" | "off";
    sources?: XAISearchSource[];
    from_date?: string;
    to_date?: string;
    max_search_results?: number;
    return_citations?: boolean;
}
export interface XAISearchSource {
    type: "web" | "x" | "news" | "rss";
    country?: string;
    excluded_websites?: string[];
    allowed_websites?: string[];
    safe_search?: boolean;
    x_handles?: string[];
    links?: string[];
}
export interface XAIMessage {
    role: "user" | "assistant" | "system";
    content: string;
}
export interface XAILiveSearchRequest {
    messages: XAIMessage[];
    model: string;
    search_parameters: XAISearchParameters;
    stream?: boolean;
    temperature?: number;
    max_tokens?: number;
}
export interface XAILiveSearchResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    citations?: string[];
}
export declare class XAILiveSearchError extends Error {
    statusCode?: number | undefined;
    response?: any | undefined;
    constructor(message: string, statusCode?: number | undefined, response?: any | undefined);
}
export declare class XAILiveSearchClient {
    private baseUrl;
    private apiKey;
    constructor(apiKey: string);
    /**
     * Perform live search using xAI chat completions with search parameters
     */
    liveSearch(request: XAILiveSearchRequest): Promise<XAILiveSearchResponse>;
    /**
     * Search for mentions of specific X handles with real-time data
     */
    searchMentions(handles: string[], options?: {
        dateRange?: {
            from?: string;
            to?: string;
        };
        maxResults?: number;
        additionalContext?: string;
    }): Promise<{
        content: string;
        citations: string[];
        relevantMentions: any[];
    }>;
    /**
     * Search for trending topics and conversations related to user's interests
     */
    searchTrendingTopics(topics: string[], options?: {
        dateRange?: {
            from?: string;
            to?: string;
        };
        sources?: ("web" | "x" | "news")[];
        maxResults?: number;
    }): Promise<{
        content: string;
        citations: string[];
        trends: any[];
    }>;
    /**
     * Analyze content for response worthiness using live context
     */
    analyzeContentWorthiness(content: string, userContext: {
        expertise: string[];
        interests: string[];
        brand?: string;
    }): Promise<{
        worthinessScore: number;
        reasoning: string;
        suggestedStrategy: string;
        liveContext: string;
        citations: string[];
    }>;
    /**
     * Generate contextually aware responses using live data
     */
    generateContextualResponse(originalContent: string, context: {
        responseType: "reply" | "quote" | "comment";
        userProfile: {
            expertise: string[];
            interests: string[];
            brand?: string;
            tone?: string;
        };
        targetAudience?: string;
        strategy?: string;
    }): Promise<{
        suggestedResponse: string;
        alternativeResponses: string[];
        liveContext: string;
        citations: string[];
        confidence: number;
    }>;
    /**
     * Monitor competitors and industry conversations
     */
    monitorCompetitors(competitors: string[], industry: string, options?: {
        dateRange?: {
            from?: string;
            to?: string;
        };
        focus?: string;
    }): Promise<{
        insights: string;
        competitorActivity: any[];
        opportunities: string[];
        citations: string[];
    }>;
    private parseMentionsFromResponse;
    private parseTrendsFromResponse;
    private parseWorthinessAnalysis;
    private parseResponseGeneration;
    private parseCompetitorAnalysis;
}
/**
 * Create an xAI Live Search client instance
 */
export declare function createXAIClient(apiKey?: string): XAILiveSearchClient;
/**
 * Helper function to format date for xAI search parameters
 */
export declare function formatDateForXAI(date: Date): string;
/**
 * Helper function to get date range for recent searches
 */
export declare function getRecentDateRange(hoursBack?: number): {
    from: string;
    to: string;
};
