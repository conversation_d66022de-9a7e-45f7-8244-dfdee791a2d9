/**
 * Unified Image Generation Client
 * Intelligently routes image generation requests between OpenAI and Fal.ai
 */
import { getOpenAIClient } from './openai_client';
import { getFalClient } from './fal_client';
/**
 * Unified image generation client with intelligent provider selection
 */
export class UnifiedImageClient {
    static instance;
    constructor() { }
    static getInstance() {
        if (!UnifiedImageClient.instance) {
            UnifiedImageClient.instance = new UnifiedImageClient();
        }
        return UnifiedImageClient.instance;
    }
    /**
     * Generate image with intelligent provider selection
     */
    async generateImage(request) {
        const startTime = Date.now();
        // Determine optimal provider and model
        const providerStrategy = this.selectProvider(request);
        let response = null;
        let isFallback = false;
        // Try primary provider
        try {
            response = await this.generateWithProvider(request, providerStrategy.primary);
        }
        catch (error) {
            console.warn(`Primary provider ${providerStrategy.primary} failed:`, error);
            // Try fallback provider if available
            if (providerStrategy.fallback) {
                try {
                    console.log(`🔄 Trying fallback provider: ${providerStrategy.fallback}`);
                    response = await this.generateWithProvider(request, providerStrategy.fallback);
                    isFallback = true;
                }
                catch (fallbackError) {
                    console.error(`Fallback provider ${providerStrategy.fallback} failed:`, fallbackError);
                }
            }
        }
        if (!response) {
            throw new Error('All image generation providers failed');
        }
        return {
            ...response,
            isFallback,
            generatedAt: startTime,
        };
    }
    /**
     * Generate multiple image variations using optimal provider mix
     */
    async generateImageVariations(request, count = 3) {
        const results = [];
        const providers = this.getOptimalProviderMix(request, count);
        // Generate in parallel with different providers
        const promises = providers.map(async (provider, index) => {
            try {
                const variationRequest = {
                    ...request,
                    provider,
                    falOptions: {
                        ...request.falOptions,
                        seed: request.falOptions?.seed ? request.falOptions.seed + index : undefined,
                    },
                };
                return await this.generateImage(variationRequest);
            }
            catch (error) {
                console.error(`Variation ${index} with ${provider} failed:`, error);
                return null;
            }
        });
        const settled = await Promise.allSettled(promises);
        for (const result of settled) {
            if (result.status === 'fulfilled' && result.value) {
                results.push(result.value);
            }
        }
        if (results.length === 0) {
            throw new Error('All image variations failed to generate');
        }
        return results;
    }
    /**
     * Generate image with specific provider
     */
    async generateWithProvider(request, provider) {
        const strategy = request.strategy || 'quality';
        if (provider === 'openai') {
            return this.generateWithOpenAI(request, strategy);
        }
        else if (provider === 'fal') {
            return this.generateWithFal(request, strategy);
        }
        else {
            throw new Error(`Unknown provider: ${provider}`);
        }
    }
    /**
     * Generate image using OpenAI DALL-E
     */
    async generateWithOpenAI(request, strategy) {
        const client = getOpenAIClient();
        // Convert unified request to OpenAI format
        const size = this.mapSizeToOpenAI(request.size, request.aspectRatio);
        const style = this.mapStyleToOpenAI(request.style);
        const response = await client.generateImage(request.prompt, {
            model: request.openaiOptions?.model || 'dall-e-3',
            size,
            quality: request.quality || 'hd',
            style,
            responseFormat: request.openaiOptions?.responseFormat || 'url',
            user: request.openaiOptions?.user,
        });
        return {
            url: response.url || '',
            provider: 'openai',
            model: response.model,
            revisedPrompt: response.revisedPrompt,
            estimatedCost: this.getOpenAICost(request.openaiOptions?.model || 'dall-e-3', size),
            strategy,
            usage: response.usage,
        };
    }
    /**
     * Generate image using Fal.ai Flux Pro
     */
    async generateWithFal(request, strategy) {
        const client = getFalClient();
        // Convert unified request to Fal format
        const imageSize = this.mapSizeToFal(request.size, request.aspectRatio);
        const response = await client.generateImage(request.prompt, {
            image_size: imageSize,
            num_inference_steps: this.getInferenceSteps(strategy),
            guidance_scale: request.falOptions?.guidanceScale || 3.5,
            seed: request.falOptions?.seed,
            expand_prompt: request.falOptions?.expandPrompt ?? true,
            format: request.falOptions?.format || 'jpeg',
        });
        return {
            url: response.url,
            provider: 'fal',
            model: response.model,
            width: response.width,
            height: response.height,
            contentType: response.contentType,
            revisedPrompt: response.revisedPrompt,
            seed: response.seed,
            inferenceTime: response.inferenceTime,
            estimatedCost: 0.055, // Fal.ai Flux Pro cost
            strategy,
        };
    }
    /**
     * Intelligent provider selection based on request characteristics
     */
    selectProvider(request) {
        // If provider is explicitly specified, use it
        if (request.provider && request.provider !== 'auto') {
            const fallback = request.provider === 'openai' ? 'fal' : 'openai';
            return { primary: request.provider, fallback };
        }
        // Intelligent selection based on use case
        const strategy = request.strategy || 'quality';
        const isArtistic = request.style === 'artistic' || request.style === 'vibrant';
        const isProfessional = request.style === 'professional' || request.platform === 'linkedin';
        const needsSpeed = strategy === 'ultra_fast' || strategy === 'fast';
        // Fal.ai is better for artistic, creative content
        if (isArtistic && !needsSpeed) {
            return { primary: 'fal', fallback: 'openai' };
        }
        // OpenAI is better for professional, standard content and speed
        if (isProfessional || needsSpeed) {
            return { primary: 'openai', fallback: 'fal' };
        }
        // Default: try OpenAI first (more reliable), fallback to Fal
        return { primary: 'openai', fallback: 'fal' };
    }
    /**
     * Get optimal provider mix for variations
     */
    getOptimalProviderMix(request, count) {
        const providers = [];
        const strategy = this.selectProvider(request);
        // Distribute across providers for variety
        for (let i = 0; i < count; i++) {
            if (i === 0) {
                providers.push(strategy.primary);
            }
            else if (i === 1 && strategy.fallback) {
                providers.push(strategy.fallback);
            }
            else {
                // Alternate between providers
                providers.push(i % 2 === 0 ? strategy.primary : (strategy.fallback || strategy.primary));
            }
        }
        return providers;
    }
    /**
     * Map unified size to OpenAI format
     */
    mapSizeToOpenAI(size, aspectRatio) {
        if (size) {
            // Direct size mapping
            const sizeMap = {
                'small': '512x512',
                'medium': '1024x1024',
                'large': '1024x1024',
                'xl': '1792x1024',
                '256x256': '256x256',
                '512x512': '512x512',
                '1024x1024': '1024x1024',
                '1792x1024': '1792x1024',
                '1024x1792': '1024x1792',
            };
            if (sizeMap[size]) {
                return sizeMap[size];
            }
        }
        // Aspect ratio based mapping
        if (aspectRatio === 'landscape') {
            return '1792x1024';
        }
        else if (aspectRatio === 'portrait') {
            return '1024x1792';
        }
        return '1024x1024'; // Default square
    }
    /**
     * Map unified size to Fal format
     */
    mapSizeToFal(size, aspectRatio) {
        if (aspectRatio === 'landscape') {
            return 'landscape_16_9';
        }
        else if (aspectRatio === 'portrait') {
            return 'portrait_4_3';
        }
        return 'square_hd'; // Default high-quality square
    }
    /**
     * Map unified style to OpenAI format
     */
    mapStyleToOpenAI(style) {
        const styleMap = {
            'minimal': 'natural',
            'professional': 'natural',
            'vibrant': 'vivid',
            'artistic': 'vivid',
            'vivid': 'vivid',
            'natural': 'natural',
        };
        return styleMap[style || ''] || 'vivid';
    }
    /**
     * Get inference steps based on strategy
     */
    getInferenceSteps(strategy) {
        const stepsMap = {
            'ultra_fast': 20,
            'fast': 25,
            'quality': 35,
            'bulk': 25,
            'auto': 30,
        };
        return stepsMap[strategy] || 30;
    }
    /**
     * Get OpenAI pricing
     */
    getOpenAICost(model, size) {
        const pricing = {
            'dall-e-2': {
                '256x256': 0.016,
                '512x512': 0.018,
                '1024x1024': 0.020,
            },
            'dall-e-3': {
                '1024x1024': 0.040,
                '1792x1024': 0.080,
                '1024x1792': 0.080,
            },
        };
        return pricing[model]?.[size] || 0.040;
    }
    /**
     * Test all providers
     */
    async testAllProviders() {
        const results = {
            openai: false,
            fal: false,
            overall: false,
        };
        // Test OpenAI
        try {
            const openaiClient = getOpenAIClient();
            results.openai = await openaiClient.testConnection();
        }
        catch (error) {
            console.error('OpenAI test failed:', error);
        }
        // Test Fal.ai
        try {
            const falClient = getFalClient();
            results.fal = await falClient.testConnection();
        }
        catch (error) {
            console.error('Fal.ai test failed:', error);
        }
        results.overall = results.openai || results.fal;
        return results;
    }
}
/**
 * Convenience functions for common use cases
 */
export async function generateImage(request) {
    const client = UnifiedImageClient.getInstance();
    return client.generateImage(request);
}
export async function generateSocialMediaImage(prompt, options = {}) {
    return generateImage({
        prompt,
        platform: options.platform,
        style: options.style,
        provider: options.provider || 'auto',
        strategy: 'quality',
        aspectRatio: options.platform === 'twitter' ? 'landscape' : 'square',
    });
}
export async function generateImageVariations(prompt, count = 3, options = {}) {
    const client = UnifiedImageClient.getInstance();
    return client.generateImageVariations({ prompt, ...options }, count);
}
export async function testImageGeneration() {
    const client = UnifiedImageClient.getInstance();
    return client.testAllProviders();
}
