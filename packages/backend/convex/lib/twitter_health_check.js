/**
 * Twitter API Health Check and Testing Utilities
 *
 * This file provides utilities to test the TwitterAPI.io integration
 * and verify that everything is working correctly.
 */
import { action } from "../_generated/server";
import { v } from "convex/values";
import { createTwitterClient } from "./twitter_client";
import { validateConfig } from "./config";
import { api } from "../_generated/api";
export class TwitterApiError extends Error {
    statusCode;
    rateLimitReset;
    constructor(message, statusCode, rateLimitReset) {
        super(message);
        this.statusCode = statusCode;
        this.rateLimitReset = rateLimitReset;
        this.name = 'TwitterApiError';
    }
}
export const healthCheck = action({
    handler: async () => {
        const results = {
            timestamp: new Date().toISOString(),
            config: { valid: false, errors: [] },
            apiConnection: { connected: false, error: null },
            rateLimits: {},
            testResults: {
                userLookup: { success: false, error: null },
                tweetFetch: { success: false, error: null },
                search: { success: false, error: null },
            },
        };
        try {
            // 1. Validate Configuration
            const configValidation = validateConfig();
            results.config = configValidation;
            if (!configValidation.valid) {
                return {
                    success: false,
                    message: "Configuration validation failed",
                    results,
                };
            }
            // 2. Test API Connection
            try {
                const apiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
                const twitterClient = createTwitterClient(apiKey);
                // Test with a well-known account
                const testUser = await twitterClient.getUserByUsername("twitter");
                if (testUser) {
                    results.apiConnection.connected = true;
                    results.testResults.userLookup.success = true;
                }
                else {
                    results.apiConnection.error = "Failed to fetch test user";
                }
                // Get rate limit status (simplified)
                results.rateLimits = {};
            }
            catch (error) {
                results.apiConnection.connected = false;
                results.apiConnection.error = error instanceof Error ? error.message : String(error);
            }
            // 3. Test Tweet Fetching (if user lookup succeeded)
            if (results.testResults.userLookup.success) {
                try {
                    const apiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
                    const twitterClient = createTwitterClient(apiKey);
                    const tweets = await twitterClient.getTweetsByUsername("twitter", { maxResults: 1 });
                    if (tweets.tweets.length > 0) {
                        results.testResults.tweetFetch.success = true;
                    }
                    else {
                        results.testResults.tweetFetch.error = "No tweets returned";
                    }
                }
                catch (error) {
                    results.testResults.tweetFetch.error = error instanceof Error ? error.message : String(error);
                }
            }
            // 4. Test Search Functionality
            try {
                const apiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
                const twitterClient = createTwitterClient(apiKey);
                const searchResults = await twitterClient.searchTweets("twitter", { maxResults: 1 });
                if (searchResults.tweets.length > 0) {
                    results.testResults.search.success = true;
                }
                else {
                    results.testResults.search.error = "No search results returned";
                }
            }
            catch (error) {
                results.testResults.search.error = error instanceof Error ? error.message : String(error);
            }
            // Calculate overall success
            const allTestsPassed = results.config.valid &&
                results.apiConnection.connected &&
                results.testResults.userLookup.success &&
                results.testResults.tweetFetch.success &&
                results.testResults.search.success;
            return {
                success: allTestsPassed,
                message: allTestsPassed
                    ? "All health checks passed successfully"
                    : "Some health checks failed - see results for details",
                results,
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
                results,
            };
        }
    },
});
export const testApiKey = action({
    args: {
        apiKey: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            // Use provided API key or default from environment
            const apiKey = args.apiKey || process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
            if (!apiKey) {
                return {
                    success: false,
                    message: "No API key provided and TWITTERAPI_IO_API_KEY environment variable not set",
                    data: null,
                    error: "No API key provided and TWITTERAPI_IO_API_KEY environment variable not set",
                };
            }
            // Test the API key by making a simple request
            const response = await fetch("https://api.twitterapi.io/v2/users/by/username/twitter", {
                headers: {
                    "X-API-Key": apiKey,
                    "Content-Type": "application/json",
                },
            });
            const rateLimitInfo = {
                limit: response.headers.get("x-rate-limit-limit"),
                remaining: response.headers.get("x-rate-limit-remaining"),
                reset: response.headers.get("x-rate-limit-reset"),
            };
            if (response.ok) {
                const data = await response.json();
                return {
                    success: true,
                    message: "API key is valid and working",
                    data: {
                        rateLimitInfo,
                        username: data.data?.username,
                        name: data.data?.name,
                        verified: data.data?.verified,
                    },
                    error: null,
                };
            }
            else {
                const errorText = await response.text();
                return {
                    success: false,
                    message: `API request failed: ${response.status} ${response.statusText}`,
                    data: {
                        rateLimitInfo,
                    },
                    error: errorText,
                };
            }
        }
        catch (error) {
            return {
                success: false,
                message: `Network error: ${error instanceof Error ? error.message : String(error)}`,
                data: {
                    rateLimitInfo: null,
                },
                error: error instanceof Error ? error.message : String(error),
            };
        }
    },
});
export const testTweetUrl = action({
    args: {
        url: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            const result = await ctx.runAction(api.lib.twitter_health_check.testTweetUrl, {
                url: args.url,
            });
            return {
                success: result.success,
                message: result.success
                    ? "Tweet URL test successful"
                    : `Tweet URL test failed: ${result.error}`,
                data: result.data ? {
                    id: result.data.id,
                    author: result.data.author.name,
                    username: result.data.author.username,
                    content: result.data.content.substring(0, 100) + "...",
                    engagement: result.data.engagement,
                    createdAt: new Date(result.data.createdAt).toISOString(),
                } : null,
                error: result.error,
            };
        }
        catch (error) {
            return {
                success: false,
                message: "Tweet URL test failed",
                error: error instanceof Error ? error.message : String(error),
                data: null,
            };
        }
    },
});
export const testAccountScraping = action({
    args: {
        handle: v.string(),
        maxResults: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        try {
            const result = await ctx.runAction(api.lib.twitter_health_check.testAccountScraping, {
                handle: args.handle,
                maxResults: args.maxResults || 5,
            });
            return {
                success: result.success,
                message: result.success
                    ? `Successfully scraped ${result.data?.scraped} tweets from @${args.handle}`
                    : `Failed to scrape tweets from @${args.handle}: ${result.error}`,
                data: result.success && result.data ? {
                    handle: args.handle,
                    scraped: result.data.scraped,
                    latestTweetId: result.data.latestTweetId,
                    account: result.data.account,
                } : null,
                error: result.error,
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Account scraping test failed for @${args.handle}`,
                error: error instanceof Error ? error.message : String(error),
                data: null,
            };
        }
    },
});
export const testMentionSearch = action({
    args: {
        handle: v.string(),
        maxResults: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        try {
            const result = await ctx.runAction(api.lib.twitter_health_check.testMentionSearch, {
                handle: args.handle,
                maxResults: args.maxResults || 10,
            });
            return {
                success: result.success,
                message: result.success
                    ? `Found ${result.data?.found ?? 0} mentions for @${args.handle}, stored ${result.data?.stored ?? 0}`
                    : `Failed to search mentions for @${args.handle}: ${result.error}`,
                data: result.success && result.data ? {
                    handle: args.handle,
                    found: result.data?.found ?? 0,
                    stored: result.data?.stored ?? 0,
                } : null,
                error: result.error,
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Mention search test failed for @${args.handle}`,
                error: error instanceof Error ? error.message : String(error),
                data: null,
            };
        }
    },
});
export const runFullTest = action({
    args: {
        testHandle: v.optional(v.string()),
        testTweetUrl: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const testHandle = args.testHandle || "twitter";
        const testTweetUrl = args.testTweetUrl || "https://twitter.com/twitter/status/1";
        console.log("Starting full Twitter API integration test...");
        const results = {
            healthCheck: null,
            apiKeyTest: null,
            tweetUrlTest: null,
            accountScrapingTest: null,
            mentionSearchTest: null,
            summary: {
                totalTests: 5,
                passed: 0,
                failed: 0,
                overallSuccess: false,
            },
        };
        try {
            // 1. Health Check
            console.log("Running health check...");
            results.healthCheck = await ctx.runAction(api.lib.twitter_health_check.healthCheck);
            if (results.healthCheck.success)
                results.summary.passed++;
            else
                results.summary.failed++;
            // 2. API Key Test
            console.log("Testing API key...");
            results.apiKeyTest = await ctx.runAction(api.lib.twitter_health_check.testApiKey, {
                apiKey: process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY,
            });
            if (results.apiKeyTest.success)
                results.summary.passed++;
            else
                results.summary.failed++;
            // 3. Tweet URL Test (skip if invalid URL)
            if (testTweetUrl && testTweetUrl.includes("/status/")) {
                console.log("Testing tweet URL fetching...");
                results.tweetUrlTest = await ctx.runAction(api.lib.twitter_health_check.testTweetUrl, {
                    url: testTweetUrl,
                });
                if (results.tweetUrlTest.success)
                    results.summary.passed++;
                else
                    results.summary.failed++;
            }
            else {
                results.tweetUrlTest = { success: false, message: "Invalid test URL provided", skipped: true };
                results.summary.failed++;
            }
            // 4. Account Scraping Test (only if we have a valid account)
            if (testHandle && results.apiKeyTest.success) {
                console.log(`Testing account scraping for @${testHandle}...`);
                results.accountScrapingTest = await ctx.runAction(api.lib.twitter_health_check.testAccountScraping, {
                    handle: testHandle,
                    maxResults: 3,
                });
                if (results.accountScrapingTest.success)
                    results.summary.passed++;
                else
                    results.summary.failed++;
            }
            else {
                results.accountScrapingTest = { success: false, message: "Skipped due to API key failure", skipped: true };
                results.summary.failed++;
            }
            // 5. Mention Search Test
            if (testHandle && results.apiKeyTest.success) {
                console.log(`Testing mention search for @${testHandle}...`);
                results.mentionSearchTest = await ctx.runAction(api.lib.twitter_health_check.testMentionSearch, {
                    handle: testHandle,
                    maxResults: 5,
                });
                if (results.mentionSearchTest.success)
                    results.summary.passed++;
                else
                    results.summary.failed++;
            }
            else {
                results.mentionSearchTest = { success: false, message: "Skipped due to API key failure", skipped: true };
                results.summary.failed++;
            }
            // Calculate overall success
            results.summary.overallSuccess = results.summary.failed === 0;
            console.log(`Test completed: ${results.summary.passed}/${results.summary.totalTests} passed`);
            return {
                success: results.summary.overallSuccess,
                message: results.summary.overallSuccess
                    ? "All integration tests passed successfully!"
                    : `Integration test failed: ${results.summary.failed} out of ${results.summary.totalTests} tests failed`,
                results,
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Full test suite failed: ${error instanceof Error ? error.message : String(error)}`,
                results,
            };
        }
    },
});
