/**
 * Embedding utilities for vector operations and semantic analysis
 */
/**
 * Calculate cosine similarity between two vectors
 */
export declare function calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number;
/**
 * Average multiple embeddings into a single vector
 */
export declare function averageEmbeddings(embeddings: number[][]): number[];
/**
 * Preprocess text for embedding generation
 */
export declare function preprocessTextForEmbedding(text: string): string;
/**
 * Calculate enhanced worthiness score based on multiple factors
 */
export declare function calculateEnhancedWorthinessScore(params: {
    content: string;
    authorIsVerified?: boolean;
    authorFollowerCount?: number;
    engagement?: {
        likes: number;
        retweets: number;
        replies: number;
        views?: number;
    };
    semanticScore?: number;
    recency?: number;
}): number;
/**
 * Calculate batch insights from tweet analysis results
 */
export declare function calculateBatchInsights(analyses: any[]): {
    averageWorthiness: number;
    topTopics: string[];
    engagementPatterns: any;
    recommendedActions: string[];
};
