/**
 * Prompt templates for AI response generation
 * Handles different response styles and scenarios
 */
export interface PromptContext {
    originalContent: string;
    authorInfo?: {
        handle: string;
        displayName: string;
        isVerified?: boolean;
        followerCount?: number;
    };
    tweetMetadata?: {
        engagement: {
            likes: number;
            retweets: number;
            replies: number;
        };
        createdAt: number;
        hasMedia?: boolean;
    };
    userContext?: {
        expertise?: string[];
        interests?: string[];
        writingStyle?: string;
        brand?: string;
    };
    responseStrategy?: 'engage' | 'educate' | 'promote' | 'support' | 'humor';
    maxLength?: number;
}
export interface EnhancedPromptContext extends PromptContext {
    semanticScore?: number;
    enhancedScore?: number;
    semanticContext?: {
        similarContent?: any[];
        userInterests?: string[];
    };
}
export interface PromptTemplate {
    systemPrompt: string;
    userPrompt: (context: PromptContext) => string;
    maxTokens?: number;
    temperature?: number;
}
/**
 * Base system prompts for different response styles
 */
export declare const SYSTEM_PROMPTS: {
    professional: string;
    casual: string;
    humorous: string;
    technical: string;
    supportive: string;
};
/**
 * Response generation templates
 */
export declare const RESPONSE_TEMPLATES: Record<string, PromptTemplate>;
/**
 * Enhanced analysis context interface
 */
export interface EnhancedPromptContext extends PromptContext {
    semanticScore?: number;
    enhancedScore?: number;
    semanticContext?: {
        similarContent?: any[];
        userInterests?: string[];
        relevanceScore?: number;
    };
    similarContent?: any[];
}
/**
 * Tweet analysis templates
 */
export declare const ANALYSIS_TEMPLATES: {
    worthinessAnalysis: {
        systemPrompt: string;
        userPrompt: (context: PromptContext) => string;
        maxTokens: number;
        temperature: number;
    };
    sentimentAnalysis: {
        systemPrompt: string;
        userPrompt: (context: PromptContext) => string;
        maxTokens: number;
        temperature: number;
    };
    topicExtraction: {
        systemPrompt: string;
        userPrompt: (context: PromptContext) => string;
        maxTokens: number;
        temperature: number;
    };
    enhancedWorthiness: {
        systemPrompt: string;
        userPrompt: (context: EnhancedPromptContext) => string;
        maxTokens: number;
        temperature: number;
    };
};
/**
 * Enhanced analysis templates for comprehensive tweet evaluation
 */
export declare const ENHANCED_ANALYSIS_TEMPLATES: {
    comprehensiveWorthiness: {
        systemPrompt: string;
        userPrompt: (context: EnhancedPromptContext) => string;
        maxTokens: number;
        temperature: number;
    };
};
/**
 * Helper function to get appropriate template based on context
 */
export declare function getResponseTemplate(type: 'reply' | 'remake' | 'mention' | 'thread' | 'question', style?: 'professional' | 'casual' | 'humorous' | 'technical' | 'supportive'): PromptTemplate;
/**
 * Helper function to build context for prompts
 */
export declare function buildPromptContext(originalContent: string, options?: Partial<PromptContext>): PromptContext;
