/**
 * Configuration Validation Script for TwitterAPI.io Integration
 *
 * This script validates the environment configuration and provides
 * helpful guidance for setting up TwitterAPI.io integration properly.
 */
import { action } from "../_generated/server";
import { validateConfig, loadConfig } from "./config";
export const validateEnvironmentSetup = action({
    handler: async () => {
        const details = {
            configValidation: {},
            environmentInfo: {},
            recommendations: [],
            criticalIssues: [],
            warnings: [],
        };
        try {
            // 1. Validate configuration
            const configValidation = validateConfig();
            details.configValidation = configValidation;
            // 2. Check environment information
            details.environmentInfo = {
                nodeEnv: process.env.NODE_ENV || "development",
                hasLegacyKey: !!process.env.TWEETIO_API_KEY,
                hasNewKey: !!process.env.TWITTERAPI_IO_API_KEY,
                convexUrl: process.env.CONVEX_URL ? "configured" : "missing",
                timestamp: new Date().toISOString(),
            };
            // 3. Analyze configuration issues
            if (!configValidation.valid) {
                details.criticalIssues.push(...configValidation.errors);
            }
            if (configValidation.warnings) {
                details.warnings.push(...configValidation.warnings);
            }
            // 4. Check API key setup
            if (!process.env.TWITTERAPI_IO_API_KEY && !process.env.TWEETIO_API_KEY) {
                details.criticalIssues.push("No TwitterAPI.io API key found. Please set TWITTERAPI_IO_API_KEY in your environment variables.");
                details.recommendations.push("Visit https://twitterapi.io/ to get your API key, then add it to your .env.local file");
            }
            else if (process.env.TWEETIO_API_KEY && !process.env.TWITTERAPI_IO_API_KEY) {
                details.warnings.push("Using legacy TWEETIO_API_KEY. Consider updating to TWITTERAPI_IO_API_KEY for clarity.");
                details.recommendations.push("Update your environment variable from TWEETIO_API_KEY to TWITTERAPI_IO_API_KEY");
            }
            // 5. Environment-specific recommendations
            const nodeEnv = process.env.NODE_ENV || "development";
            if (nodeEnv === "production") {
                if (!process.env.TWITTERAPI_IO_API_KEY) {
                    details.criticalIssues.push("Production environment should use TWITTERAPI_IO_API_KEY explicitly");
                }
                const dailyLimit = parseInt(process.env.TWITTERAPI_DAILY_REQUEST_LIMIT || "10000");
                if (dailyLimit < 5000) {
                    details.warnings.push(`Daily request limit (${dailyLimit}) may be too low for production usage`);
                    details.recommendations.push("Consider increasing TWITTERAPI_DAILY_REQUEST_LIMIT for production workloads");
                }
            }
            else {
                // Development recommendations
                if (!process.env.TWITTERAPI_REQUEST_DELAY) {
                    details.recommendations.push("Consider setting TWITTERAPI_REQUEST_DELAY=2000 to be gentler on the API during development");
                }
            }
            // 6. Check optional but recommended settings
            const recommendedSettings = [
                { key: "TWITTERAPI_ENABLE_QUOTA_TRACKING", recommended: "true", purpose: "quota management" },
                { key: "TWITTERAPI_ENABLE_USAGE_LOGGING", recommended: "true", purpose: "monitoring" },
                { key: "TWITTERAPI_WARNING_THRESHOLD", recommended: "80", purpose: "early warning system" },
                { key: "TWITTERAPI_EMERGENCY_STOP_THRESHOLD", recommended: "95", purpose: "cost protection" },
            ];
            for (const setting of recommendedSettings) {
                if (!process.env[setting.key]) {
                    details.recommendations.push(`Consider setting ${setting.key}=${setting.recommended} for ${setting.purpose}`);
                }
            }
            // 7. Try to load configuration to test for runtime issues
            try {
                const config = loadConfig();
                details.environmentInfo.configLoadSuccess = true;
                details.environmentInfo.detectedEnvironment = {
                    isDevelopment: config.environment.isDevelopment,
                    isProduction: config.environment.isProduction,
                    quotaTracking: config.twitterapi.quotaManagement.enableQuotaTracking,
                    usageLogging: config.twitterapi.monitoring.enableUsageLogging,
                };
            }
            catch (error) {
                details.criticalIssues.push(`Configuration loading failed: ${error instanceof Error ? error.message : String(error)}`);
            }
            // 8. Determine overall success
            const success = details.criticalIssues.length === 0;
            let message;
            if (success && details.warnings.length === 0 && details.recommendations.length === 0) {
                message = "✅ Configuration is perfect! TwitterAPI.io integration is ready to use.";
            }
            else if (success && details.warnings.length === 0) {
                message = "✅ Configuration is valid with some optional improvements available.";
            }
            else if (success) {
                message = "⚠️ Configuration is valid but has some warnings to address.";
            }
            else {
                message = "❌ Configuration has critical issues that must be fixed.";
            }
            return {
                success,
                message,
                details,
            };
        }
        catch (error) {
            details.criticalIssues.push(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
            return {
                success: false,
                message: "❌ Configuration validation encountered an error.",
                details,
            };
        }
    },
});
export const generateEnvironmentTemplate = action({
    args: {},
    handler: async () => {
        const developmentTemplate = `# TwitterAPI.io Configuration for Development
# Copy this to your .env.local file in packages/backend/

# REQUIRED: Get your API key from https://twitterapi.io/
TWITTERAPI_IO_API_KEY=your_api_key_here

# Basic Configuration
TWITTERAPI_IO_BASE_URL=https://api.twitterapi.io
NODE_ENV=development

# Rate Limiting (gentler for development)
TWITTERAPI_ENABLE_RATE_LIMITING=true
TWITTERAPI_REQUEST_DELAY=2000
TWITTERAPI_RETRY_ATTEMPTS=3
TWITTERAPI_RETRY_DELAY=5000
TWITTERAPI_EXPONENTIAL_BACKOFF=true

# Quota Management (lower limits for development)
TWITTERAPI_ENABLE_QUOTA_TRACKING=true
TWITTERAPI_DAILY_REQUEST_LIMIT=1000
TWITTERAPI_WARNING_THRESHOLD=80
TWITTERAPI_EMERGENCY_STOP_THRESHOLD=95

# Request Configuration (smaller batches for development)
TWITTERAPI_DEFAULT_MAX_RESULTS=10
TWITTERAPI_MAX_ACCOUNTS_PER_BATCH=5
TWITTERAPI_MENTION_LOOKBACK_HOURS=24
TWITTERAPI_REQUEST_TIMEOUT=30000

# Monitoring and Logging
TWITTERAPI_ENABLE_USAGE_LOGGING=true
TWITTERAPI_ENABLE_COST_TRACKING=true
TWITTERAPI_LOG_RETENTION_DAYS=30

# Application Intervals (longer for development)
DEFAULT_SCRAPE_INTERVAL_MINUTES=120
MENTION_CHECK_INTERVAL_MINUTES=30
MAX_MENTIONS_PER_ACCOUNT=100

# Priority Settings
VERIFIED_ACCOUNT_WEIGHT=2.0
HIGH_PRIORITY_FOLLOWER_THRESHOLD=10000
MEDIUM_PRIORITY_FOLLOWER_THRESHOLD=1000

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_MENTION_MONITORING=true
ENABLE_TWEET_SCRAPING=true
ENABLE_RESPONSE_GENERATION=true`;
        const productionTemplate = `# TwitterAPI.io Configuration for Production
# Set these as Convex environment variables or in your production .env

# REQUIRED: Production API key
TWITTERAPI_IO_API_KEY=your_production_api_key_here

# Environment
NODE_ENV=production

# Rate Limiting (optimized for production)
TWITTERAPI_ENABLE_RATE_LIMITING=true
TWITTERAPI_REQUEST_DELAY=1000
TWITTERAPI_RETRY_ATTEMPTS=3
TWITTERAPI_RETRY_DELAY=5000
TWITTERAPI_EXPONENTIAL_BACKOFF=true

# Quota Management (higher limits for production)
TWITTERAPI_ENABLE_QUOTA_TRACKING=true
TWITTERAPI_DAILY_REQUEST_LIMIT=10000
TWITTERAPI_WARNING_THRESHOLD=80
TWITTERAPI_EMERGENCY_STOP_THRESHOLD=95

# Request Configuration (optimized batches)
TWITTERAPI_DEFAULT_MAX_RESULTS=20
TWITTERAPI_MAX_ACCOUNTS_PER_BATCH=10
TWITTERAPI_MENTION_LOOKBACK_HOURS=24
TWITTERAPI_REQUEST_TIMEOUT=30000

# Monitoring (essential for production)
TWITTERAPI_ENABLE_USAGE_LOGGING=true
TWITTERAPI_ENABLE_COST_TRACKING=true
TWITTERAPI_LOG_RETENTION_DAYS=30

# Application Intervals (optimized for production)
DEFAULT_SCRAPE_INTERVAL_MINUTES=60
MENTION_CHECK_INTERVAL_MINUTES=15
MAX_MENTIONS_PER_ACCOUNT=100`;
        const convexCommands = [
            "# Set your TwitterAPI.io API key",
            "npx convex env set TWITTERAPI_IO_API_KEY your_production_api_key_here",
            "",
            "# Set environment to production",
            "npx convex env set NODE_ENV production",
            "",
            "# Set production quota limits",
            "npx convex env set TWITTERAPI_DAILY_REQUEST_LIMIT 10000",
            "npx convex env set TWITTERAPI_WARNING_THRESHOLD 80",
            "npx convex env set TWITTERAPI_EMERGENCY_STOP_THRESHOLD 95",
            "",
            "# Enable monitoring",
            "npx convex env set TWITTERAPI_ENABLE_QUOTA_TRACKING true",
            "npx convex env set TWITTERAPI_ENABLE_USAGE_LOGGING true",
            "npx convex env set TWITTERAPI_ENABLE_COST_TRACKING true",
            "",
            "# Verify your configuration",
            "npx convex run lib/config_validator:validateEnvironmentSetup",
        ];
        return {
            developmentTemplate,
            productionTemplate,
            convexCommands,
        };
    },
});
export const testTwitterAPIConnection = action({
    handler: async (ctx) => {
        const startTime = Date.now();
        try {
            // Get API key
            const apiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
            if (!apiKey) {
                return {
                    success: false,
                    message: "No API key found in environment variables",
                    details: {
                        apiKeyValid: false,
                        rateLimitInfo: null,
                        responseTime: 0,
                        endpoint: "none",
                        timestamp: new Date().toISOString(),
                    },
                };
            }
            // Test API connection
            const testEndpoint = "https://api.twitterapi.io/v2/users/by/username/twitter";
            const response = await fetch(testEndpoint, {
                method: "HEAD", // Just test connection, don't fetch data
                headers: {
                    "X-API-Key": apiKey,
                    "Content-Type": "application/json",
                },
            });
            const responseTime = Date.now() - startTime;
            const rateLimitInfo = {
                limit: response.headers.get("x-rate-limit-limit"),
                remaining: response.headers.get("x-rate-limit-remaining"),
                reset: response.headers.get("x-rate-limit-reset"),
            };
            if (response.ok) {
                return {
                    success: true,
                    message: "✅ TwitterAPI.io connection successful! API key is valid.",
                    details: {
                        apiKeyValid: true,
                        rateLimitInfo,
                        responseTime,
                        endpoint: testEndpoint,
                        timestamp: new Date().toISOString(),
                    },
                };
            }
            else {
                return {
                    success: false,
                    message: `❌ TwitterAPI.io connection failed: ${response.status} ${response.statusText}`,
                    details: {
                        apiKeyValid: false,
                        rateLimitInfo,
                        responseTime,
                        endpoint: testEndpoint,
                        timestamp: new Date().toISOString(),
                    },
                };
            }
        }
        catch (error) {
            return {
                success: false,
                message: `❌ Connection test failed: ${error instanceof Error ? error.message : String(error)}`,
                details: {
                    apiKeyValid: false,
                    rateLimitInfo: null,
                    responseTime: Date.now() - startTime,
                    endpoint: "failed",
                    timestamp: new Date().toISOString(),
                },
            };
        }
    },
});
