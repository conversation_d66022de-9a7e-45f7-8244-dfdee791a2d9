/**
 * Advanced Mention Caching System
 * Implements intelligent caching strategies for optimal performance
 */
export interface CachedMentionEntry {
    id: string;
    content: string;
    author: string;
    timestamp: number;
}
export interface CachedAnalytics {
    totalCount: number;
    unreadCount: number;
    highPriorityCount: number;
}
export interface CachedAccount {
    id: string;
    handle: string;
    isActive: boolean;
}
export interface SchemaCacheData {
    mentions?: CachedMentionEntry[];
    analytics?: CachedAnalytics;
    accounts?: CachedAccount[];
    searchResults?: string[];
}
export interface CacheEntry {
    key: string;
    data: SchemaCacheData;
    timestamp: number;
    ttl: number;
    hits: number;
    lastAccessed: number;
    tags: string[];
}
export interface CacheStats {
    totalEntries: number;
    totalHits: number;
    totalMisses: number;
    hitRate: number;
    memoryUsage: number;
    oldestEntry: number;
    newestEntry: number;
}
export declare const setCacheEntry: any;
/**
 * Get cache entry with automatic hit tracking
 */
export declare const getCacheEntry: any;
/**
 * Invalidate cache entries by key pattern or tags
 */
export declare const invalidateCache: any;
/**
 * Clean up expired cache entries
 */
export declare const cleanupExpiredCache: any;
/**
 * Get cache statistics
 */
export declare const getCacheStats: any;
/**
 * Smart cache warming for mentions
 */
export declare const warmMentionCache: any;
/**
 * Intelligent cache-first mention queries
 */
export declare const getCachedMentionStats: any;
/**
 * Update mention stats cache with fresh data
 */
export declare const updateMentionStatsCache: any;
/**
 * Batch cache operations for performance
 */
export declare const batchCacheOperations: any;
