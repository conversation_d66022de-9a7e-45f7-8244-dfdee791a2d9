/**
 * Security headers configuration for production environments
 * Implements CORS policies, CSP, and other security headers
 */
export declare const CORS_CONFIG: {
    development: {
        origin: string[];
        credentials: boolean;
        methods: string[];
        allowedHeaders: string[];
    };
    production: {
        origin: string[];
        credentials: boolean;
        methods: string[];
        allowedHeaders: string[];
    };
};
export declare const CSP_CONFIG: {
    directives: {
        'default-src': string[];
        'script-src': string[];
        'style-src': string[];
        'font-src': string[];
        'img-src': string[];
        'connect-src': string[];
        'frame-ancestors': string[];
        'base-uri': string[];
        'form-action': string[];
        'upgrade-insecure-requests': never[];
    };
};
/**
 * Security headers for all HTTP responses
 */
export declare const SECURITY_HEADERS: {
    'Permissions-Policy': string;
    'Strict-Transport-Security'?: string | undefined;
    'X-Content-Type-Options': string;
    'X-Frame-Options': string;
    'X-XSS-Protection': string;
    'Referrer-Policy': string;
    'Content-Security-Policy': string;
};
/**
 * Get CORS headers based on environment and origin
 */
export declare function getCORSHeaders(origin?: string): Record<string, string>;
/**
 * Apply security headers to HTTP response
 */
export declare function applySecurityHeaders(response: Response, origin?: string): Response;
/**
 * Create HTTP action with security headers
 */
export declare function createSecureHttpAction(handler: (ctx: any, request: Request) => Promise<Response>): import("convex/server").PublicHttpAction;
/**
 * Validate origin against allowed origins
 */
export declare function isValidOrigin(origin: string): boolean;
/**
 * Create secure HTTP router with default security headers
 */
export declare function createSecureHttpRouter(): import("convex/server").HttpRouter;
export { createSecureHttpAction as secureHttpAction };
