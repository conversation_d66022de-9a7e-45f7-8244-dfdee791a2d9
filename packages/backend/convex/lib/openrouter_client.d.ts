/**
 * OpenRouter AI Client Configuration
 * Uses OpenRouter as a proxy to access multiple AI models
 */
export interface OpenRouterConfig {
    apiKey: string;
    baseURL?: string;
    defaultModel?: string;
    fallbackModels?: string[];
    maxRetries?: number;
    timeout?: number;
}
export interface AIResponse {
    content: string;
    model: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    finishReason?: string;
}
export interface EmbeddingResponse {
    embedding: number[];
    model: string;
    usage?: {
        promptTokens: number;
        totalTokens: number;
    };
}
/**
 * OpenRouter client for AI completions and embeddings
 */
export declare class OpenRouterClient {
    private client;
    private config;
    constructor(config: OpenRouterConfig);
    /**
     * Generate AI completion with automatic fallback
     */
    generateCompletion(prompt: string, options?: {
        model?: string;
        maxTokens?: number;
        temperature?: number;
        systemPrompt?: string;
        streaming?: boolean;
    }): Promise<AIResponse>;
    /**
     * Generate embeddings for vector search
     */
    generateEmbedding(text: string, model?: string): Promise<EmbeddingResponse>;
    /**
     * Batch generate embeddings for multiple texts
     */
    generateEmbeddingsBatch(texts: string[], model?: string): Promise<EmbeddingResponse[]>;
    /**
     * Stream AI completion for real-time response generation
     */
    generateCompletionStream(prompt: string, options?: {
        model?: string;
        maxTokens?: number;
        temperature?: number;
        systemPrompt?: string;
    }): AsyncGenerator<string, void, unknown>;
    /**
     * Test the connection to OpenRouter
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models from OpenRouter
     */
    getAvailableModels(): Promise<string[]>;
}
/**
 * Initialize OpenRouter client with environment variables
 */
export declare function createOpenRouterClient(): OpenRouterClient;
export declare function getOpenRouterClient(): OpenRouterClient;
