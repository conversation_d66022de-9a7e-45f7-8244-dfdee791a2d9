/**
 * AI Model Selection Utility
 *
 * Intelligently selects the appropriate AI models based on:
 * - Performance requirements (ultra_fast, fast, quality, bulk)
 * - Model type (text, image, video, embedding, analysis)
 * - Environment configuration
 * - Cost optimization settings
 * - Current system load
 */
export type ModelStrategy = 'ultra_fast' | 'fast' | 'quality' | 'bulk' | 'auto';
export type ModelType = 'text' | 'image' | 'video' | 'embedding' | 'analysis';
export interface ModelSelection {
    primary: string;
    backup?: string;
    fallbacks: string[];
    timeout: number;
    maxRetries: number;
    strategy: ModelStrategy;
    estimatedCost: number;
}
export interface ModelUsageContext {
    userTier?: 'free' | 'premium' | 'enterprise';
    urgency?: 'low' | 'medium' | 'high' | 'critical';
    quality?: 'draft' | 'standard' | 'high' | 'premium';
    batchSize?: number;
    isBusinessHours?: boolean;
    systemLoad?: 'low' | 'medium' | 'high';
}
/**
 * Advanced model selector with intelligent strategy selection
 */
export declare class ModelSelector {
    private static instance;
    private constructor();
    static getInstance(): ModelSelector;
    /**
     * Select the best model based on strategy and context
     */
    selectModel(type: ModelType, strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
    /**
     * Determine the optimal strategy based on context
     */
    private determineStrategy;
    /**
     * Auto-select strategy based on intelligent context analysis
     */
    private autoSelectStrategy;
    /**
     * Select text generation model
     */
    private selectTextModel;
    /**
     * Select image generation model
     */
    private selectImageModel;
    /**
     * Select video generation model
     */
    private selectVideoModel;
    /**
     * Select embedding model
     */
    private selectEmbeddingModel;
    /**
     * Select analysis model
     */
    private selectAnalysisModel;
    /**
     * Parse fallback models from environment string
     */
    private parseFallbacks;
    /**
     * Get current system context
     */
    getCurrentContext(): ModelUsageContext;
    /**
     * Check if cost optimization is enabled and model fits budget
     */
    isWithinBudget(selection: ModelSelection, type: ModelType): boolean;
    /**
     * Get model selection with budget constraints
     */
    selectModelWithBudget(type: ModelType, strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
    /**
     * Get free/cheap model for budget constraints
     */
    private getFreeModel;
}
/**
 * Convenience functions for common use cases
 */
export declare function selectTextModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
export declare function selectImageModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
export declare function selectEmbeddingModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
export declare function selectAnalysisModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection;
/**
 * Get optimal model for real-time responses
 */
export declare function getRealtimeModel(type: ModelType): ModelSelection;
/**
 * Get optimal model for high-quality results
 */
export declare function getQualityModel(type: ModelType): ModelSelection;
/**
 * Get optimal model for batch processing
 */
export declare function getBulkModel(type: ModelType, batchSize: number): ModelSelection;
