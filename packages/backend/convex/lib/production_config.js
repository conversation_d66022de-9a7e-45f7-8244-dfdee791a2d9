/**
 * Production Configuration Validator
 * 🔐 SECURITY: Validates all required environment variables and enforces production security standards
 */
/**
 * Critical environment variables required for production
 */
const REQUIRED_ENV_VARS = [
    'TWITTERAPI_IO_API_KEY',
    'OPENROUTER_API_KEY',
];
/**
 * Environment variables that should be present in production
 */
const PRODUCTION_RECOMMENDED_ENV_VARS = [
    'CONVEX_DEPLOYMENT',
    'NODE_ENV',
];
/**
 * Validates that all required environment variables are present and properly configured
 */
export function validateEnvironmentConfig() {
    const missing = [];
    const warnings = [];
    // Check required environment variables
    for (const envVar of REQUIRED_ENV_VARS) {
        if (!process.env[envVar]) {
            missing.push(envVar);
        }
        else if (process.env[envVar]?.length === 0) {
            missing.push(`${envVar} (empty value)`);
        }
    }
    // Check recommended environment variables for production
    if (isProduction()) {
        for (const envVar of PRODUCTION_RECOMMENDED_ENV_VARS) {
            if (!process.env[envVar]) {
                warnings.push(`${envVar} not set (recommended for production)`);
            }
        }
        // Production-specific validations
        if (process.env.NODE_ENV !== 'production') {
            warnings.push('NODE_ENV should be set to "production" in production environment');
        }
    }
    // Validate API key formats
    if (process.env.TWITTERAPI_IO_API_KEY && !isValidApiKeyFormat(process.env.TWITTERAPI_IO_API_KEY)) {
        warnings.push('TWITTERAPI_IO_API_KEY format may be invalid');
    }
    if (process.env.OPENROUTER_API_KEY && !process.env.OPENROUTER_API_KEY.startsWith('sk-')) {
        warnings.push('OPENROUTER_API_KEY should start with "sk-"');
    }
    return {
        isValid: missing.length === 0,
        missing,
        warnings
    };
}
/**
 * Throws an error if environment validation fails (for startup validation)
 */
export function validateEnvironmentOrThrow() {
    const result = validateEnvironmentConfig();
    if (!result.isValid) {
        const errorMessage = [
            '🚨 CRITICAL: Environment validation failed!',
            `Missing required environment variables: ${result.missing.join(', ')}`,
            '',
            'Required environment variables:',
            ...REQUIRED_ENV_VARS.map(env => `  - ${env}`),
            '',
            'Please check your .env file or environment configuration.',
        ].join('\n');
        throw new Error(errorMessage);
    }
    if (result.warnings.length > 0 && isProduction()) {
        console.warn('⚠️  Environment warnings:', result.warnings.join(', '));
    }
}
/**
 * Safe way to get environment variables with fallbacks
 */
export function getEnvironmentConfig() {
    validateEnvironmentOrThrow();
    return {
        NODE_ENV: process.env.NODE_ENV || 'development',
        TWITTERAPI_IO_API_KEY: process.env.TWITTERAPI_IO_API_KEY,
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
        CONVEX_DEPLOYMENT: process.env.CONVEX_DEPLOYMENT,
    };
}
/**
 * Check if we're running in production
 */
export function isProduction() {
    return process.env.NODE_ENV === 'production';
}
/**
 * Check if we're running in development
 */
export function isDevelopment() {
    return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
}
/**
 * Basic API key format validation
 */
function isValidApiKeyFormat(apiKey) {
    // Basic validation - should be a reasonable length and contain alphanumeric characters
    return apiKey.length >= 10 && /^[a-zA-Z0-9_-]+$/.test(apiKey);
}
/**
 * Get a masked version of an API key for logging (shows first 4 and last 4 characters)
 */
export function maskApiKey(apiKey) {
    if (apiKey.length <= 8) {
        return '***';
    }
    return `${apiKey.slice(0, 4)}...${apiKey.slice(-4)}`;
}
/**
 * Production security configuration
 */
export const PRODUCTION_SECURITY_CONFIG = {
    // Rate limiting
    DEFAULT_RATE_LIMIT: 100, // requests per minute per user
    EXPENSIVE_OPERATION_RATE_LIMIT: 10, // for AI/API calls
    // API limits
    MAX_XAI_SEARCHES_PER_DAY: 50,
    MAX_TWITTER_REQUESTS_PER_HOUR: 100,
    // Input validation
    MAX_QUERY_LENGTH: 500,
    MAX_CONTENT_LENGTH: 50000,
    MAX_BATCH_SIZE: 10,
    // Timeouts
    API_TIMEOUT_MS: 30000,
    DATABASE_TIMEOUT_MS: 10000,
};
/**
 * Development vs Production feature flags
 */
export const FEATURE_FLAGS = {
    ENABLE_DEBUG_LOGGING: isDevelopment(),
    ENABLE_DETAILED_ERRORS: isDevelopment(),
    ENABLE_PERFORMANCE_MONITORING: isProduction(),
    ENABLE_STRICT_VALIDATION: isProduction(),
};
