/**
 * AI Fallback Client - Resilient AI response generation with multiple providers
 * Handles rate limits, model failures, and provides graceful degradation
 */
import { type ModelStrategy, type ModelUsageContext } from './model_selector';
export interface AIFallbackConfig {
    enableFallbacks: boolean;
    maxRetryAttempts: number;
    baseDelayMs: number;
    exponentialBackoff: boolean;
    enableLocalFallbacks: boolean;
}
export interface AIGenerationOptions {
    prompt: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    preferredModel?: string;
    enableFallbacks?: boolean;
    strategy?: ModelStrategy;
    context?: ModelUsageContext;
    urgency?: 'low' | 'medium' | 'high' | 'critical';
    quality?: 'draft' | 'standard' | 'high' | 'premium';
}
export interface AIResponse {
    content: string;
    model: string;
    provider: string;
    confidence: number;
    isFallback: boolean;
    generatedAt: number;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    strategy?: ModelStrategy;
    estimatedCost?: number;
    timeout?: number;
}
/**
 * Comprehensive AI client with multiple fallback strategies
 */
export declare class AIFallbackClient {
    private config;
    private rateLimitCache;
    constructor(config?: Partial<AIFallbackConfig>);
    /**
     * Check if a model is currently rate limited
     */
    private isRateLimited;
    /**
     * Mark a model as rate limited for a specific duration
     */
    private markRateLimited;
    /**
     * Apply exponential backoff delay
     */
    private applyDelay;
    /**
     * Generate AI response with comprehensive fallback handling
     */
    generateResponse(options: AIGenerationOptions): Promise<AIResponse>;
    /**
     * INTELLIGENT OpenRouter with smart model selection
     */
    private tryOpenRouter;
    /**
     * Generate super-smart local fallback with deep prompt analysis
     */
    private generateLocalFallback;
    /**
     * Generate response to questions
     */
    private generateQuestionResponse;
    /**
     * Generate casual/humorous response
     */
    private generateCasualResponse;
    /**
     * Generate professional response
     */
    private generateProfessionalResponse;
    /**
     * Generate technical response
     */
    private generateTechnicalResponse;
    /**
     * Generate casual positive response
     */
    private generateCasualPositiveResponse;
    /**
     * Generate supportive response
     */
    private generateSupportiveResponse;
    /**
     * Generate contextual generic response
     */
    private generateContextualGenericResponse;
    /**
     * Get current rate limit status for debugging
     */
    getRateLimitStatus(): Record<string, number>;
    /**
     * Clear rate limit cache (for testing/debugging)
     */
    clearRateLimits(): void;
}
export declare function getAIFallbackClient(config?: Partial<AIFallbackConfig>): AIFallbackClient;
/**
 * Quick generation function for simple use cases
 */
export declare function generateAIResponse(prompt: string, options?: Partial<AIGenerationOptions>): Promise<AIResponse>;
/**
 * Convenience functions for common scenarios
 */
export declare function generateFastResponse(prompt: string, systemPrompt?: string): Promise<AIResponse>;
export declare function generateQualityResponse(prompt: string, systemPrompt?: string): Promise<AIResponse>;
export declare function generateRealtimeResponse(prompt: string, systemPrompt?: string): Promise<AIResponse>;
export declare function generateBulkResponse(prompt: string, systemPrompt?: string): Promise<AIResponse>;
