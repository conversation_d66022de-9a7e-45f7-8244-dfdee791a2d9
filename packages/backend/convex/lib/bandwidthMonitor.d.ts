/**
 * 🚀 BANDWIDTH MONITORING & OPTIMIZATION TRACKING
 *
 * This module tracks bandwidth usage and optimization effectiveness
 * to ensure our optimization efforts are working as expected
 */
/**
 * Log bandwidth usage for monitoring and optimization tracking
 */
export declare const logBandwidthUsage: any;
/**
 * Get bandwidth usage report for monitoring dashboard
 */
export declare const getBandwidthReport: any;
/**
 * Get optimization effectiveness metrics
 */
export declare const getOptimizationMetrics: any;
/**
 * Helper function to log a bandwidth optimization
 * Use this in optimized queries to track effectiveness
 */
export declare function logOptimization(ctx: any, operation: string, originalSize: number, optimizedSize: number, executionTime: number, recordsScanned: number, recordsReturned: number, optimizationType: string, cacheHit?: boolean): Promise<void>;
/**
 * Helper to calculate estimated bandwidth savings
 */
export declare function calculateBandwidthSavings(recordCount: number, avgFullRecordSize: number, avgProjectedRecordSize: number): {
    originalSize: number;
    optimizedSize: number;
    savings: number;
    savingsPercent: number;
};
