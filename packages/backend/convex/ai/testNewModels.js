import { action } from "../_generated/server";
import { v } from "convex/values";
import { getOpenRouterClient } from "../lib/openrouter_client";
import { generateAIResponse } from "../lib/ai_fallback_client";
import { testImageGeneration } from "../lib/unified_image_client";
import { api } from "../_generated/api";
/**
 * Test the new Gemini 2.5 models via OpenRouter
 */
export const testGemini25Models = action({
    args: {
        testPrompt: v.optional(v.string()),
        includeAdvancedTest: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const testPrompt = args.testPrompt || "Explain the difference between fast and quality AI responses in one sentence.";
        const results = {
            timestamp: Date.now(),
            testPrompt,
            models: {},
        };
        // Test Gemini 2.5 Flash Preview (fast model)
        try {
            console.log('🧪 Testing Gemini 2.5 Flash Preview...');
            const startTime = Date.now();
            const flashResponse = await generateAIResponse(testPrompt, {
                strategy: 'fast',
                urgency: 'high',
                quality: 'standard',
            });
            const responseTime = Date.now() - startTime;
            results.models['gemini-2.5-flash-preview'] = {
                success: true,
                model: flashResponse.model,
                provider: flashResponse.provider,
                responseTime,
                strategy: flashResponse.strategy,
                estimatedCost: flashResponse.estimatedCost,
                confidence: flashResponse.confidence,
                content: flashResponse.content.substring(0, 200) + '...',
                isFallback: flashResponse.isFallback,
            };
        }
        catch (error) {
            results.models['gemini-2.5-flash-preview'] = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
        // Test Gemini 2.5 Pro Preview (quality model)
        try {
            console.log('🧪 Testing Gemini 2.5 Pro Preview...');
            const startTime = Date.now();
            const proResponse = await generateAIResponse(testPrompt, {
                strategy: 'quality',
                urgency: 'low',
                quality: 'premium',
            });
            const responseTime = Date.now() - startTime;
            results.models['gemini-2.5-pro-preview'] = {
                success: true,
                model: proResponse.model,
                provider: proResponse.provider,
                responseTime,
                strategy: proResponse.strategy,
                estimatedCost: proResponse.estimatedCost,
                confidence: proResponse.confidence,
                content: proResponse.content.substring(0, 200) + '...',
                isFallback: proResponse.isFallback,
            };
        }
        catch (error) {
            results.models['gemini-2.5-pro-preview'] = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
        // Advanced testing with complex prompts
        if (args.includeAdvancedTest) {
            try {
                console.log('🧪 Running advanced model comparison test...');
                const complexPrompt = `
        Analyze this scenario and provide a strategic recommendation:
        A tech startup is choosing between implementing AI features quickly with basic models
        versus taking more time to implement advanced AI capabilities. Consider factors like:
        - Time to market
        - User experience quality
        - Long-term scalability
        - Cost implications
        Provide a balanced analysis in 2-3 paragraphs.
        `;
                const advancedTests = await Promise.allSettled([
                    generateAIResponse(complexPrompt, { strategy: 'fast', quality: 'standard' }),
                    generateAIResponse(complexPrompt, { strategy: 'quality', quality: 'premium' }),
                ]);
                results.advancedTest = {
                    fast: advancedTests[0].status === 'fulfilled' ? {
                        success: true,
                        model: advancedTests[0].value.model,
                        responseTime: 'N/A', // Would need to track separately
                        wordCount: advancedTests[0].value.content.split(' ').length,
                        strategy: advancedTests[0].value.strategy,
                    } : {
                        success: false,
                        error: 'Failed to generate fast response',
                    },
                    quality: advancedTests[1].status === 'fulfilled' ? {
                        success: true,
                        model: advancedTests[1].value.model,
                        responseTime: 'N/A', // Would need to track separately
                        wordCount: advancedTests[1].value.content.split(' ').length,
                        strategy: advancedTests[1].value.strategy,
                    } : {
                        success: false,
                        error: 'Failed to generate quality response',
                    },
                };
            }
            catch (error) {
                results.advancedTest = {
                    error: error instanceof Error ? error.message : 'Advanced test failed',
                };
            }
        }
        return {
            ...results,
            summary: {
                totalModels: Object.keys(results.models).length,
                successfulModels: Object.values(results.models).filter((m) => m.success).length,
                overallSuccess: Object.values(results.models).some((m) => m.success),
                recommendations: [
                    results.models['gemini-2.5-flash-preview']?.success ?
                        "Gemini 2.5 Flash Preview is working well for fast responses" :
                        "Consider checking Gemini 2.5 Flash Preview configuration",
                    results.models['gemini-2.5-pro-preview']?.success ?
                        "Gemini 2.5 Pro Preview is working well for quality responses" :
                        "Consider checking Gemini 2.5 Pro Preview configuration",
                    "Both models use OpenRouter - ensure OPENROUTER_API_KEY is properly configured",
                    "Consider setting up fallback models for improved reliability",
                ],
            },
        };
    },
});
/**
 * Test image generation with new providers (OpenAI + Fal.ai)
 */
export const testUnifiedImageGeneration = action({
    args: {
        testPrompt: v.optional(v.string()),
        testBothProviders: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const testPrompt = args.testPrompt || "A modern tech startup office with AI-powered computers, clean minimal design";
        try {
            // Test unified image generation system
            const testResults = await testImageGeneration();
            const results = {
                timestamp: Date.now(),
                testPrompt,
                providerTests: testResults,
                generationTest: null,
                recommendations: [],
            };
            // If providers are available, test actual generation
            if (testResults.overall && args.testBothProviders) {
                try {
                    console.log('🎨 Testing actual image generation...');
                    // Import the unified client here to avoid circular dependencies
                    const { generateImage } = await import("../lib/unified_image_client");
                    const imageResponse = await generateImage({
                        prompt: testPrompt,
                        provider: 'auto',
                        strategy: 'fast',
                        style: 'professional',
                        aspectRatio: 'square',
                    });
                    results.generationTest = {
                        success: true,
                        provider: imageResponse.provider,
                        model: imageResponse.model,
                        url: imageResponse.url,
                        estimatedCost: imageResponse.estimatedCost,
                        strategy: imageResponse.strategy,
                        isFallback: imageResponse.isFallback,
                    };
                }
                catch (genError) {
                    results.generationTest = {
                        success: false,
                        error: genError instanceof Error ? genError.message : 'Generation test failed',
                    };
                }
            }
            // Add recommendations based on test results
            if (testResults.openai) {
                results.recommendations.push("✅ OpenAI DALL-E is available and working");
            }
            else {
                results.recommendations.push("❌ OpenAI DALL-E failed - check OPENAI_API_KEY");
            }
            if (testResults.fal) {
                results.recommendations.push("✅ Fal.ai Flux Pro is available and working");
            }
            else {
                results.recommendations.push("❌ Fal.ai failed - check FAL_API_KEY");
            }
            if (!testResults.overall) {
                results.recommendations.push("⚠️ No image providers are working - check API keys");
            }
            else {
                results.recommendations.push("🎯 Unified image generation is ready to use");
            }
            return results;
        }
        catch (error) {
            console.error('Unified image generation test failed:', error);
            return {
                timestamp: Date.now(),
                testPrompt,
                error: error instanceof Error ? error.message : 'Unknown error',
                success: false,
                recommendations: [
                    "Check that all required environment variables are set",
                    "Ensure OPENAI_API_KEY and FAL_API_KEY are valid",
                    "Verify network connectivity to API providers",
                ],
            };
        }
    },
});
/**
 * Complete system test for all new integrations
 */
export const testAllNewIntegrations = action({
    args: {
        includeImageGeneration: v.optional(v.boolean()),
        includeAdvancedTests: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const startTime = Date.now();
        console.log('🚀 Starting comprehensive integration test...');
        const results = {
            timestamp: startTime,
            testSuite: 'new-integrations-v1',
            tests: {},
            summary: {},
        };
        // Test 1: Gemini 2.5 Models
        try {
            console.log('📝 Testing Gemini 2.5 text models...');
            const geminiTest = await ctx.runAction(api.ai.testNewModels.testGemini25Models, {
                includeAdvancedTest: args.includeAdvancedTests,
            });
            results.tests.gemini25 = geminiTest;
        }
        catch (error) {
            results.tests.gemini25 = {
                success: false,
                error: error instanceof Error ? error.message : 'Gemini test failed',
            };
        }
        // Test 2: Image generation (if requested)
        if (args.includeImageGeneration) {
            try {
                console.log('🎨 Testing unified image generation...');
                const imageTest = await ctx.runAction(api.ai.testNewModels.testUnifiedImageGeneration, {
                    testBothProviders: true,
                });
                results.tests.imageGeneration = imageTest;
            }
            catch (error) {
                results.tests.imageGeneration = {
                    success: false,
                    error: error instanceof Error ? error.message : 'Image generation test failed',
                };
            }
        }
        // Test 3: OpenRouter connectivity
        try {
            console.log('🔗 Testing OpenRouter connectivity...');
            const client = getOpenRouterClient();
            const connectionTest = await client.testConnection();
            results.tests.openrouter = {
                success: connectionTest,
                timestamp: Date.now(),
            };
        }
        catch (error) {
            results.tests.openrouter = {
                success: false,
                error: error instanceof Error ? error.message : 'OpenRouter test failed',
            };
        }
        // Generate summary
        const totalTests = Object.keys(results.tests).length;
        const successfulTests = Object.values(results.tests).filter((test) => test.success || test.summary?.overallSuccess || test.providerTests?.overall).length;
        results.summary = {
            totalTests,
            successfulTests,
            overallSuccess: successfulTests > 0,
            successRate: totalTests > 0 ? (successfulTests / totalTests) * 100 : 0,
            testDuration: Date.now() - startTime,
            systemStatus: successfulTests === totalTests ? 'All systems operational' :
                successfulTests > 0 ? 'Partial functionality available' :
                    'System integration issues detected',
            nextSteps: successfulTests === totalTests ? [
                "✅ All integrations are working correctly",
                "🚀 System is ready for production use",
                "📊 Monitor usage and costs as traffic increases",
            ] : [
                "🔧 Check failed integrations and API keys",
                "📝 Review error messages for troubleshooting",
                "🔄 Retry tests after fixing configuration issues",
            ],
        };
        console.log(`✅ Integration test completed: ${successfulTests}/${totalTests} tests passed`);
        return results;
    },
});
/**
 * Quick health check for all systems
 */
export const quickHealthCheck = action({
    args: {},
    handler: async (ctx, args) => {
        const checks = {
            timestamp: Date.now(),
            openrouter: false,
            geminiModelsConfigured: false,
            imageProviders: { openai: false, fal: false },
            overall: false,
        };
        // Check OpenRouter
        try {
            const client = getOpenRouterClient();
            checks.openrouter = await client.testConnection();
        }
        catch (error) {
            console.warn('OpenRouter health check failed:', error);
        }
        // Check if Gemini models are configured
        checks.geminiModelsConfigured = !!(process.env.TEXT_MODEL_FAST?.includes('gemini-2.5') ||
            process.env.TEXT_MODEL_QUALITY?.includes('gemini-2.5'));
        // Check image providers
        try {
            const imageTests = await testImageGeneration();
            checks.imageProviders = {
                openai: imageTests.openai,
                fal: imageTests.fal,
            };
        }
        catch (error) {
            console.warn('Image provider health check failed:', error);
        }
        checks.overall = checks.openrouter && (checks.imageProviders.openai || checks.imageProviders.fal);
        return {
            ...checks,
            status: checks.overall ? 'healthy' : 'degraded',
            message: checks.overall ?
                'All core systems are operational' :
                'Some systems may need attention',
        };
    },
});
