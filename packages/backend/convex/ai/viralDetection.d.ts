/**
 * Viral Detection and Trend Analysis Engine
 * Identifies viral mentions and trending conversations for priority processing
 */
export interface ViralAnalysisInput {
    content: string;
    authorHandle: string;
    authorFollowerCount?: number;
    authorIsVerified?: boolean;
    currentEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
        views?: number;
    };
    timePosted?: number;
    hasMedia?: boolean;
    hashtags?: string[];
    mentions?: string[];
    context?: {
        trendingTopics?: string[];
        currentEvents?: string[];
        authorRecentPerformance?: number[];
    };
}
export interface ViralPrediction {
    viralProbability: number;
    predictedEngagement: {
        likes: number;
        retweets: number;
        replies: number;
        peakTime: number;
    };
    viralFactors: {
        contentQuality: number;
        authorInfluence: number;
        timingBonus: number;
        trendAlignment: number;
        emotionalImpact: number;
        shareability: number;
    };
    actionRecommendations: string[];
    confidenceLevel: number;
    riskFactors: string[];
}
/**
 * Main Viral Detection Action
 */
export declare const predictViralPotential: any;
/**
 * Get trending analytics for real-time dashboard
 */
export declare const getTrendingAnalytics: any;
/**
 * Batch Viral Analysis for Multiple Tweets
 */
export declare const batchViralAnalysis: any;
