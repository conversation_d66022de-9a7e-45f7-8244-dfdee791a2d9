/**
 * Test the new Gemini 2.5 models via OpenRouter
 */
export declare const testGemini25Models: any;
/**
 * Test image generation with new providers (OpenAI + Fal.ai)
 */
export declare const testUnifiedImageGeneration: any;
/**
 * Complete system test for all new integrations
 */
export declare const testAllNewIntegrations: any;
/**
 * Quick health check for all systems
 */
export declare const quickHealthCheck: any;
