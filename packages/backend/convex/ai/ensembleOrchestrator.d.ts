/**
 * Ensemble Orchestrator for AI-powered mention monitoring
 * Combines multiple AI models and strategies for optimal mention processing
 */
export interface EnsembleConfig {
    analysisModels: string;
    priorityModels: string;
    responseModels: string;
    confidenceThreshold: number;
    priorityThreshold: number;
    engagementThreshold: number;
    followersWeight: number;
    engagementWeight: number;
    verifiedWeight: number;
    timingWeight: number;
}
export interface MentionAnalysis {
    shouldRespond: boolean;
    confidence: number;
    priority: "high" | "medium" | "low";
    sentiment: "positive" | "negative" | "neutral" | "mixed";
    topics: string;
    urgency: "immediate" | "soon" | "low";
    responseStrategy: string;
    riskLevel: "low" | "medium" | "high";
    reasoning: string;
    sentimentAnalysis?: {
        sentiment: "bullish" | "bearish" | "neutral";
        sentimentScore: number;
        confidence: number;
        reasoning: string;
        keyWords: string[];
    };
}
export interface ProcessingMetrics {
    startTime: number;
    endTime: number;
    duration: number;
    modelsUsed: string;
    cacheHits: number;
    cacheMisses: number;
    errorCount: number;
}
/**
 * Ensemble AI analysis for mentions with advanced optimization
 */
export declare const analyzeMentionEnsemble: any;
/**
 * Batch process multiple mentions with intelligent prioritization
 */
export declare const batchProcessMentions: any;
/**
 * Get processing metrics and performance analytics
 */
export declare const getProcessingMetrics: any;
