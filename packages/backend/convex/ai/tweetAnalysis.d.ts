/**
 * Analyze if a tweet is worth responding to
 */
export declare const analyzeTweetWorthiness: any;
/**
 * Analyze tweet sentiment and emotional context
 */
export declare const analyzeTweetSentiment: any;
/**
 * Extract topics and themes from a tweet
 */
export declare const extractTweetTopics: any;
/**
 * Classify tweet priority for response queue
 */
export declare const classifyTweetPriority: any;
/**
 * Comprehensive tweet analysis combining all methods
 */
export declare const analyzeTweetComprehensive: any;
