export declare const ingestAnalyticsBatch: import("convex/server").RegisteredMutation<"public", {
    events: {
        userId?: import("convex/values").GenericId<"users"> | undefined;
        clerkUserId?: string | undefined;
        sessionId?: string | undefined;
        path?: string | undefined;
        timestamp: number;
        eventName: string;
        properties: any;
    }[];
}, Promise<{
    received: number;
}>>;
